import { SpeechState, Phoneme, MouthSyncData } from '@/types';

export class SpeechService {
  private mediaRecorder: MediaRecorder | null = null;
  private audioChunks: Blob[] = [];
  private recognition: SpeechRecognition | null = null;
  private synthesis: SpeechSynthesis;
  private currentUtterance: SpeechSynthesisUtterance | null = null;

  constructor() {
    this.synthesis = window.speechSynthesis;
    this.initializeSpeechRecognition();
  }

  private initializeSpeechRecognition() {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      this.recognition = new SpeechRecognition();
      
      this.recognition.continuous = true;
      this.recognition.interimResults = true;
      this.recognition.lang = 'en-US';
    }
  }

  // Start listening for speech
  async startListening(
    onTranscript: (transcript: string, isFinal: boolean) => void,
    onError: (error: string) => void
  ): Promise<void> {
    if (!this.recognition) {
      onError('Speech recognition not supported');
      return;
    }

    try {
      this.recognition.onresult = (event) => {
        let transcript = '';
        let isFinal = false;

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const result = event.results[i];
          transcript += result[0].transcript;
          if (result.isFinal) {
            isFinal = true;
          }
        }

        onTranscript(transcript, isFinal);
      };

      this.recognition.onerror = (event) => {
        onError(`Speech recognition error: ${event.error}`);
      };

      this.recognition.start();
    } catch (error) {
      onError(`Failed to start speech recognition: ${error}`);
    }
  }

  // Stop listening
  stopListening(): void {
    if (this.recognition) {
      this.recognition.stop();
    }
  }

  // Convert speech to text using Whisper API
  async transcribeAudio(audioBlob: Blob): Promise<string> {
    try {
      const formData = new FormData();
      formData.append('file', audioBlob, 'audio.wav');
      formData.append('model', 'whisper-1');

      const response = await fetch('/api/transcribe', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Transcription failed: ${response.statusText}`);
      }

      const result = await response.json();
      return result.text;
    } catch (error) {
      console.error('Transcription error:', error);
      throw error;
    }
  }

  // Text-to-speech with mouth sync data
  async speak(
    text: string,
    onStart?: () => void,
    onEnd?: () => void,
    onMouthSync?: (data: MouthSyncData) => void
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.synthesis) {
        reject(new Error('Speech synthesis not supported'));
        return;
      }

      // Cancel any ongoing speech
      this.synthesis.cancel();

      this.currentUtterance = new SpeechSynthesisUtterance(text);
      
      // Configure voice settings for a warm, professional trainer
      this.currentUtterance.rate = 0.9;
      this.currentUtterance.pitch = 1.1;
      this.currentUtterance.volume = 0.8;

      // Try to find a good voice
      const voices = this.synthesis.getVoices();
      const preferredVoice = voices.find(voice => 
        voice.name.includes('Google') && voice.lang.startsWith('en')
      ) || voices.find(voice => voice.lang.startsWith('en'));
      
      if (preferredVoice) {
        this.currentUtterance.voice = preferredVoice;
      }

      this.currentUtterance.onstart = () => {
        onStart?.();
        // Generate mouth sync data
        const mouthSyncData = this.generateMouthSyncData(text);
        onMouthSync?.(mouthSyncData);
      };

      this.currentUtterance.onend = () => {
        onEnd?.();
        resolve();
      };

      this.currentUtterance.onerror = (event) => {
        reject(new Error(`Speech synthesis error: ${event.error}`));
      };

      this.synthesis.speak(this.currentUtterance);
    });
  }

  // Stop current speech
  stopSpeaking(): void {
    if (this.synthesis) {
      this.synthesis.cancel();
    }
  }

  // Generate mouth sync data for avatar animation
  private generateMouthSyncData(text: string): MouthSyncData {
    const words = text.split(' ');
    const phonemes: Phoneme[] = [];
    let currentTime = 0;
    const averageWordDuration = 0.6; // seconds per word

    words.forEach((word, index) => {
      const wordDuration = averageWordDuration * (word.length / 5); // Adjust based on word length
      
      // Simple phoneme mapping (in a real app, you'd use a proper phoneme library)
      const wordPhonemes = this.mapWordToPhonemes(word);
      const phonemeDuration = wordDuration / wordPhonemes.length;

      wordPhonemes.forEach((phoneme, pIndex) => {
        phonemes.push({
          phoneme,
          start: currentTime + (pIndex * phonemeDuration),
          end: currentTime + ((pIndex + 1) * phonemeDuration),
          intensity: this.getPhonemeIntensity(phoneme)
        });
      });

      currentTime += wordDuration + 0.1; // Small pause between words
    });

    return {
      phonemes,
      duration: currentTime,
      currentTime: 0
    };
  }

  // Simple phoneme mapping (simplified for demo)
  private mapWordToPhonemes(word: string): string[] {
    // This is a very simplified mapping. In production, use a proper phoneme library
    const vowels = ['a', 'e', 'i', 'o', 'u'];
    const phonemes: string[] = [];
    
    for (let i = 0; i < word.length; i++) {
      const char = word[i].toLowerCase();
      if (vowels.includes(char)) {
        phonemes.push('open'); // Open mouth for vowels
      } else if (char === 'm' || char === 'p' || char === 'b') {
        phonemes.push('closed'); // Closed mouth for bilabials
      } else {
        phonemes.push('mid'); // Mid position for other consonants
      }
    }

    return phonemes.length > 0 ? phonemes : ['mid'];
  }

  // Get intensity for mouth animation
  private getPhonemeIntensity(phoneme: string): number {
    switch (phoneme) {
      case 'open': return 0.8;
      case 'closed': return 0.1;
      case 'mid': return 0.5;
      default: return 0.5;
    }
  }

  // Record audio for Whisper transcription
  async startRecording(): Promise<void> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      this.mediaRecorder = new MediaRecorder(stream);
      this.audioChunks = [];

      this.mediaRecorder.ondataavailable = (event) => {
        this.audioChunks.push(event.data);
      };

      this.mediaRecorder.start();
    } catch (error) {
      throw new Error(`Failed to start recording: ${error}`);
    }
  }

  async stopRecording(): Promise<Blob> {
    return new Promise((resolve, reject) => {
      if (!this.mediaRecorder) {
        reject(new Error('No active recording'));
        return;
      }

      this.mediaRecorder.onstop = () => {
        const audioBlob = new Blob(this.audioChunks, { type: 'audio/wav' });
        resolve(audioBlob);
      };

      this.mediaRecorder.stop();
      
      // Stop all tracks to release microphone
      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());
    });
  }

  // Check if speech services are available
  isAvailable(): boolean {
    return !!(this.recognition && this.synthesis);
  }
}

// Global speech service instance
export const speechService = new SpeechService();
