import { SpeechState, Phoneme, MouthSyncData } from '@/types';

export class SpeechService {
  private mediaRecorder: MediaRecorder | null = null;
  private audioChunks: Blob[] = [];
  private recognition: SpeechRecognition | null = null;
  private synthesis: SpeechSynthesis;
  private currentUtterance: SpeechSynthesisUtterance | null = null;
  private currentAudio: HTMLAudioElement | null = null;
  private isUsingOpenAITTS: boolean = false;
  private isSpeaking: boolean = false;

  constructor() {
    // Only initialize in browser environment
    if (typeof window !== 'undefined') {
      this.synthesis = window.speechSynthesis;
      this.initializeSpeechRecognition();
    }
  }

  private initializeSpeechRecognition() {
    if (typeof window !== 'undefined' && ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      this.recognition = new SpeechRecognition();

      this.recognition.continuous = true;
      this.recognition.interimResults = true;
      this.recognition.lang = 'en-US';
    }
  }

  // Start listening for speech
  async startListening(
    onTranscript: (transcript: string, isFinal: boolean) => void,
    onError: (error: string) => void
  ): Promise<void> {
    if (!this.recognition) {
      onError('Speech recognition not supported');
      return;
    }

    try {
      this.recognition.onresult = (event) => {
        let transcript = '';
        let isFinal = false;

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const result = event.results[i];
          transcript += result[0].transcript;
          if (result.isFinal) {
            isFinal = true;
          }
        }

        onTranscript(transcript, isFinal);
      };

      this.recognition.onerror = (event) => {
        onError(`Speech recognition error: ${event.error}`);
      };

      this.recognition.start();
    } catch (error) {
      onError(`Failed to start speech recognition: ${error}`);
    }
  }

  // Stop listening
  stopListening(): void {
    if (this.recognition) {
      this.recognition.stop();
    }
  }

  // Convert speech to text using Whisper API
  async transcribeAudio(audioBlob: Blob): Promise<string> {
    try {
      const formData = new FormData();
      formData.append('file', audioBlob, 'audio.wav');
      formData.append('model', 'whisper-1');

      const response = await fetch('/api/transcribe', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Transcription failed: ${response.statusText}`);
      }

      const result = await response.json();
      return result.text;
    } catch (error) {
      console.error('Transcription error:', error);
      throw error;
    }
  }

  // Text-to-speech using OpenAI TTS API
  async speak(
    text: string,
    onStart?: () => void,
    onEnd?: () => void,
    onMouthSync?: (data: MouthSyncData) => void
  ): Promise<void> {
    console.log('🎤 SPEAK CALLED:', { text: text.substring(0, 50) + '...', isSpeaking: this.isSpeaking });

    // CRITICAL: Stop any ongoing speech first
    this.stopSpeaking();

    // Set speaking state IMMEDIATELY
    this.isSpeaking = true;
    console.log('🎤 Speaking state set to TRUE');

    try {
      // Try OpenAI TTS first
      this.isUsingOpenAITTS = true;
      console.log('🎤 Attempting OpenAI TTS...');

      // Call onStart immediately
      onStart?.();
      console.log('🎤 onStart called');

      // Generate mouth sync data immediately
      const mouthSyncData = this.generateMouthSyncData(text);
      onMouthSync?.(mouthSyncData);
      console.log('🎤 onMouthSync called');

      // Call OpenAI TTS API
      const response = await fetch('/api/text-to-speech', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ text })
      });

      if (!response.ok) {
        console.warn('❌ OpenAI TTS API failed:', response.statusText);
        this.isUsingOpenAITTS = false;
        this.isSpeaking = false; // Reset before fallback
        return this.fallbackSpeak(text, onStart, onEnd, onMouthSync);
      }

      console.log('✅ OpenAI TTS API success, creating audio...');

      // Get audio blob from response
      const audioBlob = await response.blob();

      // CRITICAL: Check if we're still supposed to be speaking
      if (!this.isSpeaking) {
        console.log('🛑 Speaking was cancelled, aborting audio creation');
        return;
      }

      // Create audio element and play
      this.currentAudio = new Audio();
      const audioUrl = URL.createObjectURL(audioBlob);
      this.currentAudio.src = audioUrl;
      this.currentAudio.volume = 0.8;

      return new Promise((resolve, reject) => {
        if (!this.currentAudio || !this.isSpeaking) {
          console.log('🛑 Audio creation failed or speaking cancelled');
          this.isSpeaking = false;
          this.isUsingOpenAITTS = false;
          return reject(new Error('Audio creation failed or cancelled'));
        }

        this.currentAudio.onended = () => {
          console.log('✅ OpenAI TTS audio ended normally');
          URL.revokeObjectURL(audioUrl);
          this.currentAudio = null;
          this.isSpeaking = false;
          this.isUsingOpenAITTS = false;
          onEnd?.();
          resolve();
        };

        this.currentAudio.onerror = (e) => {
          console.warn('❌ OpenAI TTS audio error:', e);
          URL.revokeObjectURL(audioUrl);
          this.currentAudio = null;
          this.isUsingOpenAITTS = false;
          this.isSpeaking = false;
          // Don't fallback on audio error - just fail
          onEnd?.();
          reject(new Error('Audio playback failed'));
        };

        console.log('🎵 Starting OpenAI TTS audio playback...');
        this.currentAudio.play().catch((playError) => {
          console.warn('❌ OpenAI TTS play error:', playError);
          URL.revokeObjectURL(audioUrl);
          this.currentAudio = null;
          this.isUsingOpenAITTS = false;
          this.isSpeaking = false;
          // Don't fallback on play error - just fail
          onEnd?.();
          reject(playError);
        });
      });

    } catch (error) {
      console.warn('❌ OpenAI TTS error:', error);
      this.isUsingOpenAITTS = false;
      this.isSpeaking = false;
      // Only fallback on network/API errors, not audio errors
      return this.fallbackSpeak(text, onStart, onEnd, onMouthSync);
    }
  }

  // Fallback to browser speech synthesis
  private async fallbackSpeak(
    text: string,
    onStart?: () => void,
    onEnd?: () => void,
    onMouthSync?: (data: MouthSyncData) => void
  ): Promise<void> {
    console.log('🔄 FALLBACK SPEAK called');

    return new Promise((resolve, reject) => {
      if (!this.synthesis) {
        console.log('❌ Speech synthesis not supported');
        this.isSpeaking = false;
        reject(new Error('Speech synthesis not supported'));
        return;
      }

      // CRITICAL: Only proceed if OpenAI TTS is not active
      if (this.isUsingOpenAITTS) {
        console.log('🛑 OpenAI TTS is active, aborting browser synthesis');
        this.isSpeaking = false;
        resolve();
        return;
      }

      // Set speaking state for browser synthesis
      this.isSpeaking = true;
      console.log('🎤 Browser synthesis starting...');

      // Cancel any ongoing browser speech
      this.synthesis.cancel();

      this.currentUtterance = new SpeechSynthesisUtterance(text);

      // Configure voice settings
      this.currentUtterance.rate = 0.9;
      this.currentUtterance.pitch = 1.1;
      this.currentUtterance.volume = 0.8;

      // Try to find a good voice
      const voices = this.synthesis.getVoices();
      const preferredVoice = voices.find(voice =>
        voice.name.includes('Google') && voice.lang.startsWith('en')
      ) || voices.find(voice => voice.lang.startsWith('en'));

      if (preferredVoice) {
        this.currentUtterance.voice = preferredVoice;
      }

      this.currentUtterance.onstart = () => {
        console.log('🎵 Browser synthesis started');
        onStart?.();
        if (onMouthSync) {
          const mouthSyncData = this.generateMouthSyncData(text);
          onMouthSync(mouthSyncData);
        }
      };

      this.currentUtterance.onend = () => {
        console.log('✅ Browser synthesis ended');
        this.isSpeaking = false;
        this.currentUtterance = null;
        onEnd?.();
        resolve();
      };

      this.currentUtterance.onerror = (event) => {
        console.log('❌ Browser synthesis error:', event.error);
        this.isSpeaking = false;
        this.currentUtterance = null;
        reject(new Error(`Speech synthesis error: ${event.error}`));
      };

      console.log('🎵 Starting browser speech synthesis...');
      this.synthesis.speak(this.currentUtterance);
    });
  }

  // Stop current speech
  stopSpeaking(): void {
    console.log('🛑 STOP SPEAKING called');

    // Reset flags FIRST
    const wasUsingSpeech = this.isSpeaking;
    this.isSpeaking = false;
    this.isUsingOpenAITTS = false;

    if (wasUsingSpeech) {
      console.log('🛑 Stopping active speech...');
    }

    // Stop OpenAI TTS audio
    if (this.currentAudio) {
      try {
        this.currentAudio.pause();
        this.currentAudio.currentTime = 0;
        this.currentAudio.src = '';
        this.currentAudio.onended = null;
        this.currentAudio.onerror = null;
        this.currentAudio = null;
        console.log('🛑 OpenAI TTS audio stopped');
      } catch (error) {
        console.log('⚠️ Error stopping OpenAI audio:', error);
      }
    }

    // Stop browser speech synthesis
    if (this.synthesis) {
      try {
        this.synthesis.cancel();
        console.log('🛑 Browser speech synthesis cancelled');
      } catch (error) {
        console.log('⚠️ Error stopping browser synthesis:', error);
      }
    }

    // Clear current utterance
    if (this.currentUtterance) {
      this.currentUtterance.onstart = null;
      this.currentUtterance.onend = null;
      this.currentUtterance.onerror = null;
      this.currentUtterance = null;
      console.log('🛑 Current utterance cleared');
    }

    console.log('🛑 All speech stopped');
  }

  // Check if currently speaking
  isSpeakingNow(): boolean {
    return this.isSpeaking;
  }

  // Get current speech method
  getCurrentSpeechMethod(): 'openai' | 'browser' | 'none' {
    if (!this.isSpeaking) return 'none';
    return this.isUsingOpenAITTS ? 'openai' : 'browser';
  }

  // Generate mouth sync data for avatar animation
  private generateMouthSyncData(text: string): MouthSyncData {
    const words = text.split(' ');
    const phonemes: Phoneme[] = [];
    let currentTime = 0;
    const averageWordDuration = 0.6; // seconds per word

    words.forEach((word, index) => {
      const wordDuration = averageWordDuration * (word.length / 5); // Adjust based on word length
      
      // Simple phoneme mapping (in a real app, you'd use a proper phoneme library)
      const wordPhonemes = this.mapWordToPhonemes(word);
      const phonemeDuration = wordDuration / wordPhonemes.length;

      wordPhonemes.forEach((phoneme, pIndex) => {
        phonemes.push({
          phoneme,
          start: currentTime + (pIndex * phonemeDuration),
          end: currentTime + ((pIndex + 1) * phonemeDuration),
          intensity: this.getPhonemeIntensity(phoneme)
        });
      });

      currentTime += wordDuration + 0.1; // Small pause between words
    });

    return {
      phonemes,
      duration: currentTime,
      currentTime: 0
    };
  }

  // Simple phoneme mapping (simplified for demo)
  private mapWordToPhonemes(word: string): string[] {
    // This is a very simplified mapping. In production, use a proper phoneme library
    const vowels = ['a', 'e', 'i', 'o', 'u'];
    const phonemes: string[] = [];
    
    for (let i = 0; i < word.length; i++) {
      const char = word[i].toLowerCase();
      if (vowels.includes(char)) {
        phonemes.push('open'); // Open mouth for vowels
      } else if (char === 'm' || char === 'p' || char === 'b') {
        phonemes.push('closed'); // Closed mouth for bilabials
      } else {
        phonemes.push('mid'); // Mid position for other consonants
      }
    }

    return phonemes.length > 0 ? phonemes : ['mid'];
  }

  // Get intensity for mouth animation
  private getPhonemeIntensity(phoneme: string): number {
    switch (phoneme) {
      case 'open': return 0.8;
      case 'closed': return 0.1;
      case 'mid': return 0.5;
      default: return 0.5;
    }
  }

  // Record audio for Whisper transcription
  async startRecording(): Promise<void> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      this.mediaRecorder = new MediaRecorder(stream);
      this.audioChunks = [];

      this.mediaRecorder.ondataavailable = (event) => {
        this.audioChunks.push(event.data);
      };

      this.mediaRecorder.start();
    } catch (error) {
      throw new Error(`Failed to start recording: ${error}`);
    }
  }

  async stopRecording(): Promise<Blob> {
    return new Promise((resolve, reject) => {
      if (!this.mediaRecorder) {
        reject(new Error('No active recording'));
        return;
      }

      this.mediaRecorder.onstop = () => {
        const audioBlob = new Blob(this.audioChunks, { type: 'audio/wav' });
        resolve(audioBlob);
      };

      this.mediaRecorder.stop();
      
      // Stop all tracks to release microphone
      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());
    });
  }

  // Check if speech services are available
  isAvailable(): boolean {
    return typeof window !== 'undefined' && !!(this.recognition && this.synthesis);
  }
}

// Global speech service instance (only in browser)
export const speechService = typeof window !== 'undefined' ? new SpeechService() : null;
