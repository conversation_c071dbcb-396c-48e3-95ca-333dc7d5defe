{"version": 3, "sources": [], "sections": [{"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trainer%20test/ai-trainer-assessment/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport AssessmentInterface from '@/components/AssessmentInterface';\nimport { AssessmentPhase, AIInsight } from '@/types';\n\nexport default function Home() {\n  const [currentPhase, setCurrentPhase] = useState<AssessmentPhase>('welcome');\n  const [insights, setInsights] = useState<AIInsight[]>([]);\n  const [userName, setUserName] = useState('');\n  const [showAssessment, setShowAssessment] = useState(false);\n\n  const handleStartAssessment = () => {\n    if (userName.trim()) {\n      setShowAssessment(true);\n    }\n  };\n\n  const handlePhaseChange = (phase: AssessmentPhase) => {\n    setCurrentPhase(phase);\n  };\n\n  const handleInsights = (newInsights: AIInsight[]) => {\n    setInsights(prev => [...prev, ...newInsights]);\n  };\n\n  if (showAssessment) {\n    return (\n      <AssessmentInterface\n        userName={userName}\n        onPhaseChange={handlePhaseChange}\n        onInsights={handleInsights}\n      />\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center p-4\">\n      <div className=\"max-w-md w-full bg-black bg-opacity-50 rounded-lg shadow-xl border border-cyan-400 p-8 backdrop-blur-sm\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-cyan-300 mb-2 font-mono\">\n            AI HOLOGRAPHIC TRAINER\n          </h1>\n          <p className=\"text-cyan-100 text-sm\">\n            Experience the future of fitness with Alex, your AI holographic personal trainer\n          </p>\n        </div>\n\n        <div className=\"space-y-6\">\n          <div>\n            <label htmlFor=\"name\" className=\"block text-sm font-medium text-cyan-300 mb-2 font-mono\">\n              ENTER YOUR NAME:\n            </label>\n            <input\n              type=\"text\"\n              id=\"name\"\n              value={userName}\n              onChange={(e) => setUserName(e.target.value)}\n              placeholder=\"Your name here...\"\n              className=\"w-full px-3 py-2 bg-black bg-opacity-50 border border-cyan-400 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 text-cyan-100 placeholder-cyan-400 font-mono\"\n              onKeyPress={(e) => e.key === 'Enter' && handleStartAssessment()}\n            />\n          </div>\n\n          <button\n            onClick={handleStartAssessment}\n            disabled={!userName.trim()}\n            className=\"w-full bg-cyan-600 text-white py-3 px-4 rounded-md hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-offset-2 disabled:bg-gray-600 disabled:cursor-not-allowed transition-colors border border-cyan-400 font-mono\"\n            style={{\n              boxShadow: '0 0 20px rgba(34, 211, 238, 0.3)'\n            }}\n          >\n            INITIALIZE ASSESSMENT\n          </button>\n        </div>\n\n        <div className=\"mt-8 text-center text-sm text-cyan-400 space-y-2\">\n          <p className=\"font-mono\">🎤 MICROPHONE ACCESS REQUIRED</p>\n          <p className=\"font-mono\">🔊 AUDIO OUTPUT RECOMMENDED</p>\n          <p className=\"font-mono\">🤖 AI POWERED EXPERIENCE</p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAMe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAkB;IAClE,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAc,EAAE;IACxD,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAC;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,iNAAQ,EAAC;IAErD,MAAM,wBAAwB;QAC5B,IAAI,SAAS,IAAI,IAAI;YACnB,kBAAkB;QACpB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;IAClB;IAEA,MAAM,iBAAiB,CAAC;QACtB,YAAY,CAAA,OAAQ;mBAAI;mBAAS;aAAY;IAC/C;IAEA,IAAI,gBAAgB;QAClB,qBACE,8OAAC,oJAAmB;YAClB,UAAU;YACV,eAAe;YACf,YAAY;;;;;;IAGlB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAkD;;;;;;sCAGhE,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAKvC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAO,WAAU;8CAAyD;;;;;;8CAGzF,8OAAC;oCACC,MAAK;oCACL,IAAG;oCACH,OAAO;oCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC3C,aAAY;oCACZ,WAAU;oCACV,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;;;;;;;sCAI5C,8OAAC;4BACC,SAAS;4BACT,UAAU,CAAC,SAAS,IAAI;4BACxB,WAAU;4BACV,OAAO;gCACL,WAAW;4BACb;sCACD;;;;;;;;;;;;8BAKH,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAY;;;;;;sCACzB,8OAAC;4BAAE,WAAU;sCAAY;;;;;;sCACzB,8OAAC;4BAAE,WAAU;sCAAY;;;;;;;;;;;;;;;;;;;;;;;AAKnC", "debugId": null}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trainer%20test/ai-trainer-assessment/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 207, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trainer%20test/ai-trainer-assessment/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trainer%20test/ai-trainer-assessment/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}]}