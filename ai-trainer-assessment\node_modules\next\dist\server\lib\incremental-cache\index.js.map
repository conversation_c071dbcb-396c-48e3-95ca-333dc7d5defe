{"version": 3, "sources": ["../../../../src/server/lib/incremental-cache/index.ts"], "sourcesContent": ["import type { CacheFs } from '../../../shared/lib/utils'\nimport type { PrerenderManifest } from '../../../build'\nimport {\n  type IncrementalCacheValue,\n  type IncrementalCacheEntry,\n  type IncrementalCache as IncrementalCacheType,\n  IncrementalCacheKind,\n  CachedRouteKind,\n  type IncrementalResponseCacheEntry,\n  type IncrementalFetchCacheEntry,\n  type GetIncrementalFetchCacheContext,\n  type GetIncrementalResponseCacheContext,\n  type CachedFetchValue,\n  type SetIncrementalFetchCacheContext,\n  type SetIncrementalResponseCacheContext,\n} from '../../response-cache'\nimport type { DeepReadonly } from '../../../shared/lib/deep-readonly'\n\nimport FileSystemCache from './file-system-cache'\nimport { normalizePagePath } from '../../../shared/lib/page-path/normalize-page-path'\n\nimport {\n  CACHE_ONE_YEAR,\n  PRERENDER_REVALIDATE_HEADER,\n} from '../../../lib/constants'\nimport { toRoute } from '../to-route'\nimport { SharedCacheControls } from './shared-cache-controls.external'\nimport {\n  getPrerenderResumeDataCache,\n  getRenderResumeDataCache,\n  workUnitAsyncStorage,\n} from '../../app-render/work-unit-async-storage.external'\nimport { InvariantError } from '../../../shared/lib/invariant-error'\nimport type { Revalidate } from '../cache-control'\nimport { getPreviouslyRevalidatedTags } from '../../server-utils'\nimport { workAsyncStorage } from '../../app-render/work-async-storage.external'\nimport { DetachedPromise } from '../../../lib/detached-promise'\n\nexport interface CacheHandlerContext {\n  fs?: CacheFs\n  dev?: boolean\n  flushToDisk?: boolean\n  serverDistDir?: string\n  maxMemoryCacheSize?: number\n  fetchCacheKeyPrefix?: string\n  prerenderManifest?: PrerenderManifest\n  revalidatedTags: string[]\n  _requestHeaders: IncrementalCache['requestHeaders']\n}\n\nexport interface CacheHandlerValue {\n  lastModified?: number\n  age?: number\n  cacheState?: string\n  value: IncrementalCacheValue | null\n}\n\nexport class CacheHandler {\n  // eslint-disable-next-line\n  constructor(_ctx: CacheHandlerContext) {}\n\n  public async get(\n    _cacheKey: string,\n    _ctx: GetIncrementalFetchCacheContext | GetIncrementalResponseCacheContext\n  ): Promise<CacheHandlerValue | null> {\n    return {} as any\n  }\n\n  public async set(\n    _cacheKey: string,\n    _data: IncrementalCacheValue | null,\n    _ctx: SetIncrementalFetchCacheContext | SetIncrementalResponseCacheContext\n  ): Promise<void> {}\n\n  public async revalidateTag(\n    ..._args: Parameters<IncrementalCache['revalidateTag']>\n  ): Promise<void> {}\n\n  public resetRequestCache(): void {}\n}\n\nexport class IncrementalCache implements IncrementalCacheType {\n  readonly dev?: boolean\n  readonly disableForTestmode?: boolean\n  readonly cacheHandler?: CacheHandler\n  readonly hasCustomCacheHandler: boolean\n  readonly prerenderManifest: DeepReadonly<PrerenderManifest>\n  readonly requestHeaders: Record<string, undefined | string | string[]>\n  readonly allowedRevalidateHeaderKeys?: string[]\n  readonly minimalMode?: boolean\n  readonly fetchCacheKeyPrefix?: string\n  readonly revalidatedTags?: string[]\n  readonly isOnDemandRevalidate?: boolean\n\n  private static readonly debug: boolean =\n    !!process.env.NEXT_PRIVATE_DEBUG_CACHE\n  private readonly locks = new Map<string, Promise<void>>()\n\n  /**\n   * The cache controls for routes. This will source the values from the\n   * prerender manifest until the in-memory cache is updated with new values.\n   */\n  private readonly cacheControls: SharedCacheControls\n\n  constructor({\n    fs,\n    dev,\n    flushToDisk,\n    minimalMode,\n    serverDistDir,\n    requestHeaders,\n    maxMemoryCacheSize,\n    getPrerenderManifest,\n    fetchCacheKeyPrefix,\n    CurCacheHandler,\n    allowedRevalidateHeaderKeys,\n  }: {\n    fs?: CacheFs\n    dev: boolean\n    minimalMode?: boolean\n    serverDistDir?: string\n    flushToDisk?: boolean\n    allowedRevalidateHeaderKeys?: string[]\n    requestHeaders: IncrementalCache['requestHeaders']\n    maxMemoryCacheSize?: number\n    getPrerenderManifest: () => DeepReadonly<PrerenderManifest>\n    fetchCacheKeyPrefix?: string\n    CurCacheHandler?: typeof CacheHandler\n  }) {\n    this.hasCustomCacheHandler = Boolean(CurCacheHandler)\n\n    const cacheHandlersSymbol = Symbol.for('@next/cache-handlers')\n    const _globalThis: typeof globalThis & {\n      [cacheHandlersSymbol]?: {\n        FetchCache?: typeof CacheHandler\n      }\n    } = globalThis\n\n    if (!CurCacheHandler) {\n      // if we have a global cache handler available leverage it\n      const globalCacheHandler = _globalThis[cacheHandlersSymbol]\n\n      if (globalCacheHandler?.FetchCache) {\n        CurCacheHandler = globalCacheHandler.FetchCache\n      } else {\n        if (fs && serverDistDir) {\n          if (IncrementalCache.debug) {\n            console.log('using filesystem cache handler')\n          }\n          CurCacheHandler = FileSystemCache\n        }\n      }\n    } else if (IncrementalCache.debug) {\n      console.log('using custom cache handler', CurCacheHandler.name)\n    }\n\n    if (process.env.__NEXT_TEST_MAX_ISR_CACHE) {\n      // Allow cache size to be overridden for testing purposes\n      maxMemoryCacheSize = parseInt(process.env.__NEXT_TEST_MAX_ISR_CACHE, 10)\n    }\n    this.dev = dev\n    this.disableForTestmode = process.env.NEXT_PRIVATE_TEST_PROXY === 'true'\n    // this is a hack to avoid Webpack knowing this is equal to this.minimalMode\n    // because we replace this.minimalMode to true in production bundles.\n    const minimalModeKey = 'minimalMode'\n    this[minimalModeKey] = minimalMode\n    this.requestHeaders = requestHeaders\n    this.allowedRevalidateHeaderKeys = allowedRevalidateHeaderKeys\n    this.prerenderManifest = getPrerenderManifest()\n    this.cacheControls = new SharedCacheControls(this.prerenderManifest)\n    this.fetchCacheKeyPrefix = fetchCacheKeyPrefix\n    let revalidatedTags: string[] = []\n\n    if (\n      requestHeaders[PRERENDER_REVALIDATE_HEADER] ===\n      this.prerenderManifest?.preview?.previewModeId\n    ) {\n      this.isOnDemandRevalidate = true\n    }\n\n    if (minimalMode) {\n      revalidatedTags = getPreviouslyRevalidatedTags(\n        requestHeaders,\n        this.prerenderManifest?.preview?.previewModeId\n      )\n    }\n\n    if (CurCacheHandler) {\n      this.cacheHandler = new CurCacheHandler({\n        dev,\n        fs,\n        flushToDisk,\n        serverDistDir,\n        revalidatedTags,\n        maxMemoryCacheSize,\n        _requestHeaders: requestHeaders,\n        fetchCacheKeyPrefix,\n      })\n    }\n  }\n\n  private calculateRevalidate(\n    pathname: string,\n    fromTime: number,\n    dev: boolean,\n    isFallback: boolean | undefined\n  ): Revalidate {\n    // in development we don't have a prerender-manifest\n    // and default to always revalidating to allow easier debugging\n    if (dev)\n      return Math.floor(performance.timeOrigin + performance.now() - 1000)\n\n    const cacheControl = this.cacheControls.get(toRoute(pathname))\n\n    // if an entry isn't present in routes we fallback to a default\n    // of revalidating after 1 second unless it's a fallback request.\n    const initialRevalidateSeconds = cacheControl\n      ? cacheControl.revalidate\n      : isFallback\n        ? false\n        : 1\n\n    const revalidateAfter =\n      typeof initialRevalidateSeconds === 'number'\n        ? initialRevalidateSeconds * 1000 + fromTime\n        : initialRevalidateSeconds\n\n    return revalidateAfter\n  }\n\n  _getPathname(pathname: string, fetchCache?: boolean) {\n    return fetchCache ? pathname : normalizePagePath(pathname)\n  }\n\n  resetRequestCache() {\n    this.cacheHandler?.resetRequestCache?.()\n  }\n\n  async lock(cacheKey: string): Promise<() => Promise<void> | void> {\n    // Wait for any existing lock on this cache key to be released\n    // This implements a simple queue-based locking mechanism\n    while (true) {\n      const lock = this.locks.get(cacheKey)\n\n      if (IncrementalCache.debug) {\n        console.log('lock get', cacheKey, !!lock)\n      }\n\n      // If no lock exists, we can proceed to acquire it\n      if (!lock) break\n\n      // Wait for the existing lock to be released before trying again\n      await lock\n    }\n\n    // Create a new detached promise that will represent this lock\n    // The resolve function (unlock) will be returned to the caller\n    const { resolve, promise } = new DetachedPromise<void>()\n\n    if (IncrementalCache.debug) {\n      console.log('successfully locked', cacheKey)\n    }\n\n    // Store the lock promise in the locks map\n    this.locks.set(cacheKey, promise)\n\n    return () => {\n      // Resolve the promise to release the lock.\n      resolve()\n\n      // Remove the lock from the map once it's released so that future gets\n      // can acquire the lock.\n      this.locks.delete(cacheKey)\n    }\n  }\n\n  async revalidateTag(tags: string | string[]): Promise<void> {\n    return this.cacheHandler?.revalidateTag(tags)\n  }\n\n  // x-ref: https://github.com/facebook/react/blob/2655c9354d8e1c54ba888444220f63e836925caa/packages/react/src/ReactFetch.js#L23\n  async generateCacheKey(\n    url: string,\n    init: RequestInit | Request = {}\n  ): Promise<string> {\n    // this should be bumped anytime a fix is made to cache entries\n    // that should bust the cache\n    const MAIN_KEY_PREFIX = 'v3'\n\n    const bodyChunks: string[] = []\n\n    const encoder = new TextEncoder()\n    const decoder = new TextDecoder()\n\n    if (init.body) {\n      // handle Uint8Array body\n      if (init.body instanceof Uint8Array) {\n        bodyChunks.push(decoder.decode(init.body))\n        ;(init as any)._ogBody = init.body\n      } // handle ReadableStream body\n      else if (typeof (init.body as any).getReader === 'function') {\n        const readableBody = init.body as ReadableStream<Uint8Array | string>\n\n        const chunks: Uint8Array[] = []\n\n        try {\n          await readableBody.pipeTo(\n            new WritableStream({\n              write(chunk) {\n                if (typeof chunk === 'string') {\n                  chunks.push(encoder.encode(chunk))\n                  bodyChunks.push(chunk)\n                } else {\n                  chunks.push(chunk)\n                  bodyChunks.push(decoder.decode(chunk, { stream: true }))\n                }\n              },\n            })\n          )\n\n          // Flush the decoder.\n          bodyChunks.push(decoder.decode())\n\n          // Create a new buffer with all the chunks.\n          const length = chunks.reduce((total, arr) => total + arr.length, 0)\n          const arrayBuffer = new Uint8Array(length)\n\n          // Push each of the chunks into the new array buffer.\n          let offset = 0\n          for (const chunk of chunks) {\n            arrayBuffer.set(chunk, offset)\n            offset += chunk.length\n          }\n\n          ;(init as any)._ogBody = arrayBuffer\n        } catch (err) {\n          console.error('Problem reading body', err)\n        }\n      } // handle FormData or URLSearchParams bodies\n      else if (typeof (init.body as any).keys === 'function') {\n        const formData = init.body as FormData\n        ;(init as any)._ogBody = init.body\n        for (const key of new Set([...formData.keys()])) {\n          const values = formData.getAll(key)\n          bodyChunks.push(\n            `${key}=${(\n              await Promise.all(\n                values.map(async (val) => {\n                  if (typeof val === 'string') {\n                    return val\n                  } else {\n                    return await val.text()\n                  }\n                })\n              )\n            ).join(',')}`\n          )\n        }\n        // handle blob body\n      } else if (typeof (init.body as any).arrayBuffer === 'function') {\n        const blob = init.body as Blob\n        const arrayBuffer = await blob.arrayBuffer()\n        bodyChunks.push(await blob.text())\n        ;(init as any)._ogBody = new Blob([arrayBuffer], { type: blob.type })\n      } else if (typeof init.body === 'string') {\n        bodyChunks.push(init.body)\n        ;(init as any)._ogBody = init.body\n      }\n    }\n\n    const headers =\n      typeof (init.headers || {}).keys === 'function'\n        ? Object.fromEntries(init.headers as Headers)\n        : Object.assign({}, init.headers)\n\n    // w3c trace context headers can break request caching and deduplication\n    // so we remove them from the cache key\n    if ('traceparent' in headers) delete headers['traceparent']\n    if ('tracestate' in headers) delete headers['tracestate']\n\n    const cacheString = JSON.stringify([\n      MAIN_KEY_PREFIX,\n      this.fetchCacheKeyPrefix || '',\n      url,\n      init.method,\n      headers,\n      init.mode,\n      init.redirect,\n      init.credentials,\n      init.referrer,\n      init.referrerPolicy,\n      init.integrity,\n      init.cache,\n      bodyChunks,\n    ])\n\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      function bufferToHex(buffer: ArrayBuffer): string {\n        return Array.prototype.map\n          .call(new Uint8Array(buffer), (b) => b.toString(16).padStart(2, '0'))\n          .join('')\n      }\n      const buffer = encoder.encode(cacheString)\n      return bufferToHex(await crypto.subtle.digest('SHA-256', buffer))\n    } else {\n      const crypto = require('crypto') as typeof import('crypto')\n      return crypto.createHash('sha256').update(cacheString).digest('hex')\n    }\n  }\n\n  async get(\n    cacheKey: string,\n    ctx: GetIncrementalFetchCacheContext\n  ): Promise<IncrementalFetchCacheEntry | null>\n  async get(\n    cacheKey: string,\n    ctx: GetIncrementalResponseCacheContext\n  ): Promise<IncrementalResponseCacheEntry | null>\n  async get(\n    cacheKey: string,\n    ctx: GetIncrementalFetchCacheContext | GetIncrementalResponseCacheContext\n  ): Promise<IncrementalCacheEntry | null> {\n    // Unlike other caches if we have a resume data cache, we use it even if\n    // testmode would normally disable it or if requestHeaders say 'no-cache'.\n    if (ctx.kind === IncrementalCacheKind.FETCH) {\n      const workUnitStore = workUnitAsyncStorage.getStore()\n      const resumeDataCache = workUnitStore\n        ? getRenderResumeDataCache(workUnitStore)\n        : null\n      if (resumeDataCache) {\n        const memoryCacheData = resumeDataCache.fetch.get(cacheKey)\n        if (memoryCacheData?.kind === CachedRouteKind.FETCH) {\n          return { isStale: false, value: memoryCacheData }\n        }\n      }\n    }\n\n    // we don't leverage the prerender cache in dev mode\n    // so that getStaticProps is always called for easier debugging\n    if (\n      this.disableForTestmode ||\n      (this.dev &&\n        (ctx.kind !== IncrementalCacheKind.FETCH ||\n          this.requestHeaders['cache-control'] === 'no-cache'))\n    ) {\n      return null\n    }\n\n    cacheKey = this._getPathname(\n      cacheKey,\n      ctx.kind === IncrementalCacheKind.FETCH\n    )\n\n    const cacheData = await this.cacheHandler?.get(cacheKey, ctx)\n\n    if (ctx.kind === IncrementalCacheKind.FETCH) {\n      if (!cacheData) {\n        return null\n      }\n\n      if (cacheData.value?.kind !== CachedRouteKind.FETCH) {\n        throw new InvariantError(\n          `Expected cached value for cache key ${JSON.stringify(cacheKey)} to be a \"FETCH\" kind, got ${JSON.stringify(cacheData.value?.kind)} instead.`\n        )\n      }\n\n      const workStore = workAsyncStorage.getStore()\n      const combinedTags = [...(ctx.tags || []), ...(ctx.softTags || [])]\n      // if a tag was revalidated we don't return stale data\n      if (\n        combinedTags.some(\n          (tag) =>\n            this.revalidatedTags?.includes(tag) ||\n            workStore?.pendingRevalidatedTags?.includes(tag)\n        )\n      ) {\n        return null\n      }\n\n      const revalidate = ctx.revalidate || cacheData.value.revalidate\n      const age =\n        (performance.timeOrigin +\n          performance.now() -\n          (cacheData.lastModified || 0)) /\n        1000\n\n      const isStale = age > revalidate\n      const data = cacheData.value.data\n\n      return {\n        isStale,\n        value: { kind: CachedRouteKind.FETCH, data, revalidate },\n      }\n    } else if (cacheData?.value?.kind === CachedRouteKind.FETCH) {\n      throw new InvariantError(\n        `Expected cached value for cache key ${JSON.stringify(cacheKey)} not to be a ${JSON.stringify(ctx.kind)} kind, got \"FETCH\" instead.`\n      )\n    }\n\n    let entry: IncrementalResponseCacheEntry | null = null\n    const cacheControl = this.cacheControls.get(toRoute(cacheKey))\n\n    let isStale: boolean | -1 | undefined\n    let revalidateAfter: Revalidate\n\n    if (cacheData?.lastModified === -1) {\n      isStale = -1\n      revalidateAfter = -1 * CACHE_ONE_YEAR\n    } else {\n      revalidateAfter = this.calculateRevalidate(\n        cacheKey,\n        cacheData?.lastModified || performance.timeOrigin + performance.now(),\n        this.dev ?? false,\n        ctx.isFallback\n      )\n      isStale =\n        revalidateAfter !== false &&\n        revalidateAfter < performance.timeOrigin + performance.now()\n          ? true\n          : undefined\n    }\n\n    if (cacheData) {\n      entry = {\n        isStale,\n        cacheControl,\n        revalidateAfter,\n        value: cacheData.value,\n      }\n    }\n\n    if (\n      !cacheData &&\n      this.prerenderManifest.notFoundRoutes.includes(cacheKey)\n    ) {\n      // for the first hit after starting the server the cache\n      // may not have a way to save notFound: true so if\n      // the prerender-manifest marks this as notFound then we\n      // return that entry and trigger a cache set to give it a\n      // chance to update in-memory entries\n      entry = {\n        isStale,\n        value: null,\n        cacheControl,\n        revalidateAfter,\n      }\n      this.set(cacheKey, entry.value, { ...ctx, cacheControl })\n    }\n    return entry\n  }\n\n  async set(\n    pathname: string,\n    data: CachedFetchValue | null,\n    ctx: SetIncrementalFetchCacheContext\n  ): Promise<void>\n  async set(\n    pathname: string,\n    data: Exclude<IncrementalCacheValue, CachedFetchValue> | null,\n    ctx: SetIncrementalResponseCacheContext\n  ): Promise<void>\n  async set(\n    pathname: string,\n    data: IncrementalCacheValue | null,\n    ctx: SetIncrementalFetchCacheContext | SetIncrementalResponseCacheContext\n  ): Promise<void> {\n    // Even if we otherwise disable caching for testMode or if no fetchCache is\n    // configured we still always stash results in the resume data cache if one\n    // exists. This is because this is a transient in memory cache that\n    // populates caches ahead of a dynamic render in dev mode to allow the RSC\n    // debug info to have the right environment associated to it.\n    if (data?.kind === CachedRouteKind.FETCH) {\n      const workUnitStore = workUnitAsyncStorage.getStore()\n      const prerenderResumeDataCache = workUnitStore\n        ? getPrerenderResumeDataCache(workUnitStore)\n        : null\n      if (prerenderResumeDataCache) {\n        prerenderResumeDataCache.fetch.set(pathname, data)\n      }\n    }\n\n    if (this.disableForTestmode || (this.dev && !ctx.fetchCache)) return\n\n    pathname = this._getPathname(pathname, ctx.fetchCache)\n\n    // FetchCache has upper limit of 2MB per-entry currently\n    const itemSize = JSON.stringify(data).length\n    if (\n      ctx.fetchCache &&\n      itemSize > 2 * 1024 * 1024 &&\n      // We ignore the size limit when custom cache handler is being used, as it\n      // might not have this limit\n      !this.hasCustomCacheHandler &&\n      // We also ignore the size limit when it's an implicit build-time-only\n      // caching that the user isn't even aware of.\n      !ctx.isImplicitBuildTimeCache\n    ) {\n      const warningText = `Failed to set Next.js data cache for ${ctx.fetchUrl || pathname}, items over 2MB can not be cached (${itemSize} bytes)`\n\n      if (this.dev) {\n        throw new Error(warningText)\n      }\n      console.warn(warningText)\n      return\n    }\n\n    try {\n      if (!ctx.fetchCache && ctx.cacheControl) {\n        this.cacheControls.set(toRoute(pathname), ctx.cacheControl)\n      }\n\n      await this.cacheHandler?.set(pathname, data, ctx)\n    } catch (error) {\n      console.warn('Failed to update prerender cache for', pathname, error)\n    }\n  }\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "IncrementalCache", "constructor", "_ctx", "get", "_cacheKey", "set", "_data", "revalidateTag", "_args", "resetRequestCache", "debug", "process", "env", "NEXT_PRIVATE_DEBUG_CACHE", "fs", "dev", "flushToDisk", "minimalMode", "serverDistDir", "requestHeaders", "maxMemoryCacheSize", "getPrerenderManifest", "fetchCacheKeyPrefix", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "allowedRevalidateHeaderKeys", "locks", "Map", "hasCustomCacheHandler", "Boolean", "cacheHandlersSymbol", "Symbol", "for", "_globalThis", "globalThis", "globalCacheHandler", "<PERSON><PERSON><PERSON><PERSON>", "console", "log", "FileSystemCache", "name", "__NEXT_TEST_MAX_ISR_CACHE", "parseInt", "disableForTestmode", "NEXT_PRIVATE_TEST_PROXY", "minimalModeKey", "prerenderManifest", "cacheControls", "SharedCacheControls", "revalidatedTags", "PRERENDER_REVALIDATE_HEADER", "preview", "previewModeId", "isOnDemandRevalidate", "getPreviouslyRevalidatedTags", "cache<PERSON><PERSON><PERSON>", "_requestHeaders", "calculateRevalidate", "pathname", "fromTime", "<PERSON><PERSON><PERSON><PERSON>", "Math", "floor", "performance", "<PERSON><PERSON><PERSON><PERSON>", "now", "cacheControl", "toRoute", "initialRevalidateSeconds", "revalidate", "revalidateAfter", "_getPathname", "fetchCache", "normalizePagePath", "lock", "cache<PERSON>ey", "resolve", "promise", "Detached<PERSON>romise", "delete", "tags", "generate<PERSON>ache<PERSON>ey", "url", "init", "MAIN_KEY_PREFIX", "bodyChunks", "encoder", "TextEncoder", "decoder", "TextDecoder", "body", "Uint8Array", "push", "decode", "_ogBody", "<PERSON><PERSON><PERSON><PERSON>", "readableBody", "chunks", "pipeTo", "WritableStream", "write", "chunk", "encode", "stream", "length", "reduce", "total", "arr", "arrayBuffer", "offset", "err", "error", "keys", "formData", "key", "Set", "values", "getAll", "Promise", "all", "map", "val", "text", "join", "blob", "Blob", "type", "headers", "Object", "fromEntries", "assign", "cacheString", "JSON", "stringify", "method", "mode", "redirect", "credentials", "referrer", "referrerPolicy", "integrity", "cache", "NEXT_RUNTIME", "bufferToHex", "buffer", "Array", "prototype", "call", "b", "toString", "padStart", "crypto", "subtle", "digest", "require", "createHash", "update", "ctx", "cacheData", "kind", "IncrementalCacheKind", "FETCH", "workUnitStore", "workUnitAsyncStorage", "getStore", "resumeDataCache", "getRenderResumeDataCache", "memoryCacheData", "fetch", "CachedRouteKind", "isStale", "value", "InvariantError", "workStore", "workAsyncStorage", "combinedTags", "softTags", "some", "tag", "includes", "pendingRevalidatedTags", "age", "lastModified", "data", "entry", "CACHE_ONE_YEAR", "undefined", "notFoundRoutes", "prerenderResumeDataCache", "getPrerenderResumeDataCache", "itemSize", "isImplicitBuildTimeCache", "warningText", "fetchUrl", "Error", "warn"], "mappings": ";;;;;;;;;;;;;;;IAyDaA,YAAY;eAAZA;;IAwBAC,gBAAgB;eAAhBA;;;+BAlEN;wEAGqB;mCACM;2BAK3B;yBACiB;6CACY;8CAK7B;gCACwB;6BAEc;0CACZ;iCACD;;;;;;AAqBzB,MAAMD;IACX,2BAA2B;IAC3BE,YAAYC,IAAyB,CAAE,CAAC;IAExC,MAAaC,IACXC,SAAiB,EACjBF,IAA0E,EACvC;QACnC,OAAO,CAAC;IACV;IAEA,MAAaG,IACXD,SAAiB,EACjBE,KAAmC,EACnCJ,IAA0E,EAC3D,CAAC;IAElB,MAAaK,cACX,GAAGC,KAAoD,EACxC,CAAC;IAEXC,oBAA0B,CAAC;AACpC;AAEO,MAAMT;qBAaaU,QACtB,CAAC,CAACC,QAAQC,GAAG,CAACC,wBAAwB;IASxCZ,YAAY,EACVa,EAAE,EACFC,GAAG,EACHC,WAAW,EACXC,WAAW,EACXC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,oBAAoB,EACpBC,mBAAmB,EACnBC,eAAe,EACfC,2BAA2B,EAa5B,CAAE;YA+CC,iCAAA;aA/EaC,QAAQ,IAAIC;QAiC3B,IAAI,CAACC,qBAAqB,GAAGC,QAAQL;QAErC,MAAMM,sBAAsBC,OAAOC,GAAG,CAAC;QACvC,MAAMC,cAIFC;QAEJ,IAAI,CAACV,iBAAiB;YACpB,0DAA0D;YAC1D,MAAMW,qBAAqBF,WAAW,CAACH,oBAAoB;YAE3D,IAAIK,sCAAAA,mBAAoBC,UAAU,EAAE;gBAClCZ,kBAAkBW,mBAAmBC,UAAU;YACjD,OAAO;gBACL,IAAIrB,MAAMI,eAAe;oBACvB,IAAIlB,iBAAiBU,KAAK,EAAE;wBAC1B0B,QAAQC,GAAG,CAAC;oBACd;oBACAd,kBAAkBe,wBAAe;gBACnC;YACF;QACF,OAAO,IAAItC,iBAAiBU,KAAK,EAAE;YACjC0B,QAAQC,GAAG,CAAC,8BAA8Bd,gBAAgBgB,IAAI;QAChE;QAEA,IAAI5B,QAAQC,GAAG,CAAC4B,yBAAyB,EAAE;YACzC,yDAAyD;YACzDpB,qBAAqBqB,SAAS9B,QAAQC,GAAG,CAAC4B,yBAAyB,EAAE;QACvE;QACA,IAAI,CAACzB,GAAG,GAAGA;QACX,IAAI,CAAC2B,kBAAkB,GAAG/B,QAAQC,GAAG,CAAC+B,uBAAuB,KAAK;QAClE,4EAA4E;QAC5E,qEAAqE;QACrE,MAAMC,iBAAiB;QACvB,IAAI,CAACA,eAAe,GAAG3B;QACvB,IAAI,CAACE,cAAc,GAAGA;QACtB,IAAI,CAACK,2BAA2B,GAAGA;QACnC,IAAI,CAACqB,iBAAiB,GAAGxB;QACzB,IAAI,CAACyB,aAAa,GAAG,IAAIC,gDAAmB,CAAC,IAAI,CAACF,iBAAiB;QACnE,IAAI,CAACvB,mBAAmB,GAAGA;QAC3B,IAAI0B,kBAA4B,EAAE;QAElC,IACE7B,cAAc,CAAC8B,sCAA2B,CAAC,OAC3C,0BAAA,IAAI,CAACJ,iBAAiB,sBAAtB,kCAAA,wBAAwBK,OAAO,qBAA/B,gCAAiCC,aAAa,GAC9C;YACA,IAAI,CAACC,oBAAoB,GAAG;QAC9B;QAEA,IAAInC,aAAa;gBAGb,kCAAA;YAFF+B,kBAAkBK,IAAAA,yCAA4B,EAC5ClC,iBACA,2BAAA,IAAI,CAAC0B,iBAAiB,sBAAtB,mCAAA,yBAAwBK,OAAO,qBAA/B,iCAAiCC,aAAa;QAElD;QAEA,IAAI5B,iBAAiB;YACnB,IAAI,CAAC+B,YAAY,GAAG,IAAI/B,gBAAgB;gBACtCR;gBACAD;gBACAE;gBACAE;gBACA8B;gBACA5B;gBACAmC,iBAAiBpC;gBACjBG;YACF;QACF;IACF;IAEQkC,oBACNC,QAAgB,EAChBC,QAAgB,EAChB3C,GAAY,EACZ4C,UAA+B,EACnB;QACZ,oDAAoD;QACpD,+DAA+D;QAC/D,IAAI5C,KACF,OAAO6C,KAAKC,KAAK,CAACC,YAAYC,UAAU,GAAGD,YAAYE,GAAG,KAAK;QAEjE,MAAMC,eAAe,IAAI,CAACnB,aAAa,CAAC3C,GAAG,CAAC+D,IAAAA,gBAAO,EAACT;QAEpD,+DAA+D;QAC/D,iEAAiE;QACjE,MAAMU,2BAA2BF,eAC7BA,aAAaG,UAAU,GACvBT,aACE,QACA;QAEN,MAAMU,kBACJ,OAAOF,6BAA6B,WAChCA,2BAA2B,OAAOT,WAClCS;QAEN,OAAOE;IACT;IAEAC,aAAab,QAAgB,EAAEc,UAAoB,EAAE;QACnD,OAAOA,aAAad,WAAWe,IAAAA,oCAAiB,EAACf;IACnD;IAEAhD,oBAAoB;YAClB,sCAAA;SAAA,qBAAA,IAAI,CAAC6C,YAAY,sBAAjB,uCAAA,mBAAmB7C,iBAAiB,qBAApC,0CAAA;IACF;IAEA,MAAMgE,KAAKC,QAAgB,EAAuC;QAChE,8DAA8D;QAC9D,yDAAyD;QACzD,MAAO,KAAM;YACX,MAAMD,OAAO,IAAI,CAAChD,KAAK,CAACtB,GAAG,CAACuE;YAE5B,IAAI1E,iBAAiBU,KAAK,EAAE;gBAC1B0B,QAAQC,GAAG,CAAC,YAAYqC,UAAU,CAAC,CAACD;YACtC;YAEA,kDAAkD;YAClD,IAAI,CAACA,MAAM;YAEX,gEAAgE;YAChE,MAAMA;QACR;QAEA,8DAA8D;QAC9D,+DAA+D;QAC/D,MAAM,EAAEE,OAAO,EAAEC,OAAO,EAAE,GAAG,IAAIC,gCAAe;QAEhD,IAAI7E,iBAAiBU,KAAK,EAAE;YAC1B0B,QAAQC,GAAG,CAAC,uBAAuBqC;QACrC;QAEA,0CAA0C;QAC1C,IAAI,CAACjD,KAAK,CAACpB,GAAG,CAACqE,UAAUE;QAEzB,OAAO;YACL,2CAA2C;YAC3CD;YAEA,sEAAsE;YACtE,wBAAwB;YACxB,IAAI,CAAClD,KAAK,CAACqD,MAAM,CAACJ;QACpB;IACF;IAEA,MAAMnE,cAAcwE,IAAuB,EAAiB;YACnD;QAAP,QAAO,qBAAA,IAAI,CAACzB,YAAY,qBAAjB,mBAAmB/C,aAAa,CAACwE;IAC1C;IAEA,8HAA8H;IAC9H,MAAMC,iBACJC,GAAW,EACXC,OAA8B,CAAC,CAAC,EACf;QACjB,+DAA+D;QAC/D,6BAA6B;QAC7B,MAAMC,kBAAkB;QAExB,MAAMC,aAAuB,EAAE;QAE/B,MAAMC,UAAU,IAAIC;QACpB,MAAMC,UAAU,IAAIC;QAEpB,IAAIN,KAAKO,IAAI,EAAE;YACb,yBAAyB;YACzB,IAAIP,KAAKO,IAAI,YAAYC,YAAY;gBACnCN,WAAWO,IAAI,CAACJ,QAAQK,MAAM,CAACV,KAAKO,IAAI;gBACtCP,KAAaW,OAAO,GAAGX,KAAKO,IAAI;YACpC,OACK,IAAI,OAAO,AAACP,KAAKO,IAAI,CAASK,SAAS,KAAK,YAAY;gBAC3D,MAAMC,eAAeb,KAAKO,IAAI;gBAE9B,MAAMO,SAAuB,EAAE;gBAE/B,IAAI;oBACF,MAAMD,aAAaE,MAAM,CACvB,IAAIC,eAAe;wBACjBC,OAAMC,KAAK;4BACT,IAAI,OAAOA,UAAU,UAAU;gCAC7BJ,OAAOL,IAAI,CAACN,QAAQgB,MAAM,CAACD;gCAC3BhB,WAAWO,IAAI,CAACS;4BAClB,OAAO;gCACLJ,OAAOL,IAAI,CAACS;gCACZhB,WAAWO,IAAI,CAACJ,QAAQK,MAAM,CAACQ,OAAO;oCAAEE,QAAQ;gCAAK;4BACvD;wBACF;oBACF;oBAGF,qBAAqB;oBACrBlB,WAAWO,IAAI,CAACJ,QAAQK,MAAM;oBAE9B,2CAA2C;oBAC3C,MAAMW,SAASP,OAAOQ,MAAM,CAAC,CAACC,OAAOC,MAAQD,QAAQC,IAAIH,MAAM,EAAE;oBACjE,MAAMI,cAAc,IAAIjB,WAAWa;oBAEnC,qDAAqD;oBACrD,IAAIK,SAAS;oBACb,KAAK,MAAMR,SAASJ,OAAQ;wBAC1BW,YAAYtG,GAAG,CAAC+F,OAAOQ;wBACvBA,UAAUR,MAAMG,MAAM;oBACxB;;oBAEErB,KAAaW,OAAO,GAAGc;gBAC3B,EAAE,OAAOE,KAAK;oBACZzE,QAAQ0E,KAAK,CAAC,wBAAwBD;gBACxC;YACF,OACK,IAAI,OAAO,AAAC3B,KAAKO,IAAI,CAASsB,IAAI,KAAK,YAAY;gBACtD,MAAMC,WAAW9B,KAAKO,IAAI;gBACxBP,KAAaW,OAAO,GAAGX,KAAKO,IAAI;gBAClC,KAAK,MAAMwB,OAAO,IAAIC,IAAI;uBAAIF,SAASD,IAAI;iBAAG,EAAG;oBAC/C,MAAMI,SAASH,SAASI,MAAM,CAACH;oBAC/B7B,WAAWO,IAAI,CACb,GAAGsB,IAAI,CAAC,EAAE,AACR,CAAA,MAAMI,QAAQC,GAAG,CACfH,OAAOI,GAAG,CAAC,OAAOC;wBAChB,IAAI,OAAOA,QAAQ,UAAU;4BAC3B,OAAOA;wBACT,OAAO;4BACL,OAAO,MAAMA,IAAIC,IAAI;wBACvB;oBACF,GACF,EACAC,IAAI,CAAC,MAAM;gBAEjB;YACA,mBAAmB;YACrB,OAAO,IAAI,OAAO,AAACxC,KAAKO,IAAI,CAASkB,WAAW,KAAK,YAAY;gBAC/D,MAAMgB,OAAOzC,KAAKO,IAAI;gBACtB,MAAMkB,cAAc,MAAMgB,KAAKhB,WAAW;gBAC1CvB,WAAWO,IAAI,CAAC,MAAMgC,KAAKF,IAAI;gBAC7BvC,KAAaW,OAAO,GAAG,IAAI+B,KAAK;oBAACjB;iBAAY,EAAE;oBAAEkB,MAAMF,KAAKE,IAAI;gBAAC;YACrE,OAAO,IAAI,OAAO3C,KAAKO,IAAI,KAAK,UAAU;gBACxCL,WAAWO,IAAI,CAACT,KAAKO,IAAI;gBACvBP,KAAaW,OAAO,GAAGX,KAAKO,IAAI;YACpC;QACF;QAEA,MAAMqC,UACJ,OAAO,AAAC5C,CAAAA,KAAK4C,OAAO,IAAI,CAAC,CAAA,EAAGf,IAAI,KAAK,aACjCgB,OAAOC,WAAW,CAAC9C,KAAK4C,OAAO,IAC/BC,OAAOE,MAAM,CAAC,CAAC,GAAG/C,KAAK4C,OAAO;QAEpC,wEAAwE;QACxE,uCAAuC;QACvC,IAAI,iBAAiBA,SAAS,OAAOA,OAAO,CAAC,cAAc;QAC3D,IAAI,gBAAgBA,SAAS,OAAOA,OAAO,CAAC,aAAa;QAEzD,MAAMI,cAAcC,KAAKC,SAAS,CAAC;YACjCjD;YACA,IAAI,CAAC7D,mBAAmB,IAAI;YAC5B2D;YACAC,KAAKmD,MAAM;YACXP;YACA5C,KAAKoD,IAAI;YACTpD,KAAKqD,QAAQ;YACbrD,KAAKsD,WAAW;YAChBtD,KAAKuD,QAAQ;YACbvD,KAAKwD,cAAc;YACnBxD,KAAKyD,SAAS;YACdzD,KAAK0D,KAAK;YACVxD;SACD;QAED,IAAIzE,QAAQC,GAAG,CAACiI,YAAY,KAAK,QAAQ;YACvC,SAASC,YAAYC,MAAmB;gBACtC,OAAOC,MAAMC,SAAS,CAAC1B,GAAG,CACvB2B,IAAI,CAAC,IAAIxD,WAAWqD,SAAS,CAACI,IAAMA,EAAEC,QAAQ,CAAC,IAAIC,QAAQ,CAAC,GAAG,MAC/D3B,IAAI,CAAC;YACV;YACA,MAAMqB,SAAS1D,QAAQgB,MAAM,CAAC6B;YAC9B,OAAOY,YAAY,MAAMQ,OAAOC,MAAM,CAACC,MAAM,CAAC,WAAWT;QAC3D,OAAO;YACL,MAAMO,UAASG,QAAQ;YACvB,OAAOH,QAAOI,UAAU,CAAC,UAAUC,MAAM,CAACzB,aAAasB,MAAM,CAAC;QAChE;IACF;IAUA,MAAMrJ,IACJuE,QAAgB,EAChBkF,GAAyE,EAClC;YAgCf,oBAwCbC;QAvEX,wEAAwE;QACxE,0EAA0E;QAC1E,IAAID,IAAIE,IAAI,KAAKC,mCAAoB,CAACC,KAAK,EAAE;YAC3C,MAAMC,gBAAgBC,kDAAoB,CAACC,QAAQ;YACnD,MAAMC,kBAAkBH,gBACpBI,IAAAA,sDAAwB,EAACJ,iBACzB;YACJ,IAAIG,iBAAiB;gBACnB,MAAME,kBAAkBF,gBAAgBG,KAAK,CAACpK,GAAG,CAACuE;gBAClD,IAAI4F,CAAAA,mCAAAA,gBAAiBR,IAAI,MAAKU,8BAAe,CAACR,KAAK,EAAE;oBACnD,OAAO;wBAAES,SAAS;wBAAOC,OAAOJ;oBAAgB;gBAClD;YACF;QACF;QAEA,oDAAoD;QACpD,+DAA+D;QAC/D,IACE,IAAI,CAAC5H,kBAAkB,IACtB,IAAI,CAAC3B,GAAG,IACN6I,CAAAA,IAAIE,IAAI,KAAKC,mCAAoB,CAACC,KAAK,IACtC,IAAI,CAAC7I,cAAc,CAAC,gBAAgB,KAAK,UAAS,GACtD;YACA,OAAO;QACT;QAEAuD,WAAW,IAAI,CAACJ,YAAY,CAC1BI,UACAkF,IAAIE,IAAI,KAAKC,mCAAoB,CAACC,KAAK;QAGzC,MAAMH,YAAY,QAAM,qBAAA,IAAI,CAACvG,YAAY,qBAAjB,mBAAmBnD,GAAG,CAACuE,UAAUkF;QAEzD,IAAIA,IAAIE,IAAI,KAAKC,mCAAoB,CAACC,KAAK,EAAE;gBAKvCH;YAJJ,IAAI,CAACA,WAAW;gBACd,OAAO;YACT;YAEA,IAAIA,EAAAA,oBAAAA,UAAUa,KAAK,qBAAfb,kBAAiBC,IAAI,MAAKU,8BAAe,CAACR,KAAK,EAAE;oBAE2DH;gBAD9G,MAAM,qBAEL,CAFK,IAAIc,8BAAc,CACtB,CAAC,oCAAoC,EAAExC,KAAKC,SAAS,CAAC1D,UAAU,2BAA2B,EAAEyD,KAAKC,SAAS,EAACyB,oBAAAA,UAAUa,KAAK,qBAAfb,kBAAiBC,IAAI,EAAE,SAAS,CAAC,GADzI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,MAAMc,YAAYC,0CAAgB,CAACV,QAAQ;YAC3C,MAAMW,eAAe;mBAAKlB,IAAI7E,IAAI,IAAI,EAAE;mBAAO6E,IAAImB,QAAQ,IAAI,EAAE;aAAE;YACnE,sDAAsD;YACtD,IACED,aAAaE,IAAI,CACf,CAACC;oBACC,uBACAL;uBADA,EAAA,wBAAA,IAAI,CAAC5H,eAAe,qBAApB,sBAAsBkI,QAAQ,CAACD,UAC/BL,8BAAAA,oCAAAA,UAAWO,sBAAsB,qBAAjCP,kCAAmCM,QAAQ,CAACD;gBAEhD;gBACA,OAAO;YACT;YAEA,MAAM7G,aAAawF,IAAIxF,UAAU,IAAIyF,UAAUa,KAAK,CAACtG,UAAU;YAC/D,MAAMgH,MACJ,AAACtH,CAAAA,YAAYC,UAAU,GACrBD,YAAYE,GAAG,KACd6F,CAAAA,UAAUwB,YAAY,IAAI,CAAA,CAAC,IAC9B;YAEF,MAAMZ,UAAUW,MAAMhH;YACtB,MAAMkH,OAAOzB,UAAUa,KAAK,CAACY,IAAI;YAEjC,OAAO;gBACLb;gBACAC,OAAO;oBAAEZ,MAAMU,8BAAe,CAACR,KAAK;oBAAEsB;oBAAMlH;gBAAW;YACzD;QACF,OAAO,IAAIyF,CAAAA,8BAAAA,mBAAAA,UAAWa,KAAK,qBAAhBb,iBAAkBC,IAAI,MAAKU,8BAAe,CAACR,KAAK,EAAE;YAC3D,MAAM,qBAEL,CAFK,IAAIW,8BAAc,CACtB,CAAC,oCAAoC,EAAExC,KAAKC,SAAS,CAAC1D,UAAU,aAAa,EAAEyD,KAAKC,SAAS,CAACwB,IAAIE,IAAI,EAAE,2BAA2B,CAAC,GADhI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIyB,QAA8C;QAClD,MAAMtH,eAAe,IAAI,CAACnB,aAAa,CAAC3C,GAAG,CAAC+D,IAAAA,gBAAO,EAACQ;QAEpD,IAAI+F;QACJ,IAAIpG;QAEJ,IAAIwF,CAAAA,6BAAAA,UAAWwB,YAAY,MAAK,CAAC,GAAG;YAClCZ,UAAU,CAAC;YACXpG,kBAAkB,CAAC,IAAImH,yBAAc;QACvC,OAAO;YACLnH,kBAAkB,IAAI,CAACb,mBAAmB,CACxCkB,UACAmF,CAAAA,6BAAAA,UAAWwB,YAAY,KAAIvH,YAAYC,UAAU,GAAGD,YAAYE,GAAG,IACnE,IAAI,CAACjD,GAAG,IAAI,OACZ6I,IAAIjG,UAAU;YAEhB8G,UACEpG,oBAAoB,SACpBA,kBAAkBP,YAAYC,UAAU,GAAGD,YAAYE,GAAG,KACtD,OACAyH;QACR;QAEA,IAAI5B,WAAW;YACb0B,QAAQ;gBACNd;gBACAxG;gBACAI;gBACAqG,OAAOb,UAAUa,KAAK;YACxB;QACF;QAEA,IACE,CAACb,aACD,IAAI,CAAChH,iBAAiB,CAAC6I,cAAc,CAACR,QAAQ,CAACxG,WAC/C;YACA,wDAAwD;YACxD,kDAAkD;YAClD,wDAAwD;YACxD,yDAAyD;YACzD,qCAAqC;YACrC6G,QAAQ;gBACNd;gBACAC,OAAO;gBACPzG;gBACAI;YACF;YACA,IAAI,CAAChE,GAAG,CAACqE,UAAU6G,MAAMb,KAAK,EAAE;gBAAE,GAAGd,GAAG;gBAAE3F;YAAa;QACzD;QACA,OAAOsH;IACT;IAYA,MAAMlL,IACJoD,QAAgB,EAChB6H,IAAkC,EAClC1B,GAAyE,EAC1D;QACf,2EAA2E;QAC3E,2EAA2E;QAC3E,mEAAmE;QACnE,0EAA0E;QAC1E,6DAA6D;QAC7D,IAAI0B,CAAAA,wBAAAA,KAAMxB,IAAI,MAAKU,8BAAe,CAACR,KAAK,EAAE;YACxC,MAAMC,gBAAgBC,kDAAoB,CAACC,QAAQ;YACnD,MAAMwB,2BAA2B1B,gBAC7B2B,IAAAA,yDAA2B,EAAC3B,iBAC5B;YACJ,IAAI0B,0BAA0B;gBAC5BA,yBAAyBpB,KAAK,CAAClK,GAAG,CAACoD,UAAU6H;YAC/C;QACF;QAEA,IAAI,IAAI,CAAC5I,kBAAkB,IAAK,IAAI,CAAC3B,GAAG,IAAI,CAAC6I,IAAIrF,UAAU,EAAG;QAE9Dd,WAAW,IAAI,CAACa,YAAY,CAACb,UAAUmG,IAAIrF,UAAU;QAErD,wDAAwD;QACxD,MAAMsH,WAAW1D,KAAKC,SAAS,CAACkD,MAAM/E,MAAM;QAC5C,IACEqD,IAAIrF,UAAU,IACdsH,WAAW,IAAI,OAAO,QACtB,0EAA0E;QAC1E,4BAA4B;QAC5B,CAAC,IAAI,CAAClK,qBAAqB,IAC3B,sEAAsE;QACtE,6CAA6C;QAC7C,CAACiI,IAAIkC,wBAAwB,EAC7B;YACA,MAAMC,cAAc,CAAC,qCAAqC,EAAEnC,IAAIoC,QAAQ,IAAIvI,SAAS,oCAAoC,EAAEoI,SAAS,OAAO,CAAC;YAE5I,IAAI,IAAI,CAAC9K,GAAG,EAAE;gBACZ,MAAM,qBAAsB,CAAtB,IAAIkL,MAAMF,cAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAAqB;YAC7B;YACA3J,QAAQ8J,IAAI,CAACH;YACb;QACF;QAEA,IAAI;gBAKI;YAJN,IAAI,CAACnC,IAAIrF,UAAU,IAAIqF,IAAI3F,YAAY,EAAE;gBACvC,IAAI,CAACnB,aAAa,CAACzC,GAAG,CAAC6D,IAAAA,gBAAO,EAACT,WAAWmG,IAAI3F,YAAY;YAC5D;YAEA,QAAM,qBAAA,IAAI,CAACX,YAAY,qBAAjB,mBAAmBjD,GAAG,CAACoD,UAAU6H,MAAM1B;QAC/C,EAAE,OAAO9C,OAAO;YACd1E,QAAQ8J,IAAI,CAAC,wCAAwCzI,UAAUqD;QACjE;IACF;AACF", "ignoreList": [0]}