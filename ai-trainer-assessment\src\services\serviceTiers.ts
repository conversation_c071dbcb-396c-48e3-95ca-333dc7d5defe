import { ServiceTier } from '@/types';

export const SERVICE_TIERS: ServiceTier[] = [
  {
    id: 'basic',
    name: 'BASIC TRANSFORMATION',
    price: 47,
    duration: 4, // weeks
    features: [
      'Personalized workout plan based on your assessment',
      'Nutrition guidelines tailored to your goals',
      'Monthly progress check-in with <PERSON>',
      'Access to exercise video library',
      'Basic form correction tips'
    ],
    benefits: [
      'Get started with a solid foundation',
      'Learn proper exercise techniques',
      'Establish healthy habits',
      'See initial results in 4 weeks'
    ],
    targetPersona: [
      'beginners',
      'budget_conscious',
      'self_motivated',
      'minimal_time_commitment'
    ],
    urgencyFactors: [
      'Perfect for getting started without a big commitment',
      'Limited spots available this month'
    ]
  },
  {
    id: 'premium',
    name: 'PREMIUM COACHING',
    price: 147,
    duration: 12, // weeks
    features: [
      'Everything in Basic PLUS:',
      'Weekly 1-on-1 video coaching sessions with <PERSON>',
      'Real-time form analysis and corrections',
      'Personalized meal planning with recipes',
      'Weekly plan adjustments based on progress',
      'Direct messaging access to Alex',
      'Progress tracking with detailed analytics'
    ],
    benefits: [
      'Accelerated results with expert guidance',
      'Avoid injuries with proper form coaching',
      'Stay motivated with weekly accountability',
      'Achieve your goals 3x faster than going alone'
    ],
    targetPersona: [
      'serious_about_results',
      'needs_accountability',
      'wants_personalization',
      'previous_failures'
    ],
    urgencyFactors: [
      'Most popular choice - 89% success rate',
      'Only 5 premium spots left this month',
      'Includes $200 worth of bonus content'
    ]
  },
  {
    id: 'elite',
    name: 'ELITE TRANSFORMATION',
    price: 297,
    duration: 16, // weeks
    features: [
      'Everything in Premium PLUS:',
      'Daily check-ins and motivation',
      'Complete lifestyle transformation coaching',
      'Stress management and sleep optimization',
      'Supplement recommendations',
      'Emergency support for challenging days',
      'Exclusive access to Alex\'s transformation masterclass',
      'Lifetime access to all materials'
    ],
    benefits: [
      'Complete life transformation, not just fitness',
      'VIP treatment with daily support',
      'Guaranteed results or money back',
      'Build habits that last a lifetime'
    ],
    targetPersona: [
      'high_achiever',
      'wants_premium_experience',
      'major_transformation_needed',
      'values_convenience'
    ],
    urgencyFactors: [
      'Exclusive - only 2 elite spots per month',
      'Includes personal consultation worth $500',
      'Results guaranteed or full refund'
    ]
  }
];

export function getRecommendedTier(userProfile: any): ServiceTier {
  const { painPoints = [], motivations = [], goals = [], emotionalTriggers = [] } = userProfile;
  
  // Score each tier based on user profile
  let basicScore = 0;
  let premiumScore = 0;
  let eliteScore = 0;

  // Pain point analysis
  if (painPoints.includes('lack_of_time')) {
    basicScore += 2;
    premiumScore += 1;
  }
  
  if (painPoints.includes('lack_of_motivation') || painPoints.includes('consistency')) {
    premiumScore += 3;
    eliteScore += 2;
  }

  if (painPoints.includes('previous_failures')) {
    premiumScore += 2;
    eliteScore += 3;
  }

  // Motivation analysis
  if (motivations.includes('confidence') || motivations.includes('health')) {
    premiumScore += 2;
    eliteScore += 1;
  }

  // Goal analysis
  if (goals.includes('weight_loss') && goals.length === 1) {
    basicScore += 1;
    premiumScore += 2;
  }

  if (goals.length > 2) {
    premiumScore += 1;
    eliteScore += 2;
  }

  // Emotional trigger analysis
  if (emotionalTriggers.includes('frustrated') || emotionalTriggers.includes('stressed')) {
    eliteScore += 2;
  }

  if (emotionalTriggers.includes('excited') || emotionalTriggers.includes('motivated')) {
    premiumScore += 2;
  }

  // Determine recommended tier
  const scores = [
    { tier: SERVICE_TIERS[0], score: basicScore },
    { tier: SERVICE_TIERS[1], score: premiumScore },
    { tier: SERVICE_TIERS[2], score: eliteScore }
  ];

  return scores.sort((a, b) => b.score - a.score)[0].tier;
}

export function generateUpsellMessage(recommendedTier: ServiceTier, userProfile: any): string {
  const { name, painPoints = [], motivations = [] } = userProfile;
  
  let message = `${name}, based on everything we've discussed today, I'm genuinely excited about your potential. `;

  // Address specific pain points
  if (painPoints.includes('lack_of_motivation')) {
    message += `I know you mentioned struggling with motivation in the past, and that's exactly why I'd love to work closely with you. `;
  }

  if (painPoints.includes('previous_failures')) {
    message += `You've tried before and it didn't stick - I get it, and that's not your fault. The difference this time is you won't be going it alone. `;
  }

  // Connect to motivations
  if (motivations.includes('confidence')) {
    message += `I can see how important feeling confident is to you, and I know we can get you there. `;
  }

  // Tier-specific recommendation
  switch (recommendedTier.id) {
    case 'basic':
      message += `For someone at your level, I'd recommend starting with our Basic Transformation program. It's perfect for building that solid foundation without overwhelming you. `;
      break;
    case 'premium':
      message += `For someone with your goals and drive, I'd strongly recommend our Premium Coaching program. The weekly check-ins and personalized adjustments make all the difference - my premium clients see results 3x faster. `;
      break;
    case 'elite':
      message += `${name}, I can tell you're serious about making a real transformation. For someone like you, I'd recommend our Elite program. It's my most comprehensive offering, and frankly, it's designed for people who are ready to completely transform their life, not just their fitness. `;
      break;
  }

  message += `The question is: are you ready to invest in yourself and make this transformation happen? Which option feels right for your goals?`;

  return message;
}

export function getComparisonData() {
  return {
    features: [
      { name: 'Personalized Workout Plan', basic: true, premium: true, elite: true },
      { name: 'Nutrition Guidelines', basic: true, premium: true, elite: true },
      { name: 'Exercise Video Library', basic: true, premium: true, elite: true },
      { name: 'Weekly 1-on-1 Coaching', basic: false, premium: true, elite: true },
      { name: 'Real-time Form Analysis', basic: false, premium: true, elite: true },
      { name: 'Direct Messaging Access', basic: false, premium: true, elite: true },
      { name: 'Daily Check-ins', basic: false, premium: false, elite: true },
      { name: 'Lifestyle Transformation', basic: false, premium: false, elite: true },
      { name: 'Emergency Support', basic: false, premium: false, elite: true },
      { name: 'Money-back Guarantee', basic: false, premium: false, elite: true }
    ],
    successRates: {
      basic: '67%',
      premium: '89%',
      elite: '96%'
    },
    averageResults: {
      basic: '8-12 lbs in 4 weeks',
      premium: '15-25 lbs in 12 weeks',
      elite: '25-40 lbs in 16 weeks'
    }
  };
}
