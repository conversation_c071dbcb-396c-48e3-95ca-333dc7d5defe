{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trainer%20test/ai-trainer-assessment/src/services/conversationEngine.ts"], "sourcesContent": ["import { AssessmentPhase, DiscoverySubPhase, UserAssessmentProfile, AIInsight, ServiceTier } from '@/types';\n\nexport class ConversationEngine {\n  private currentPhase: AssessmentPhase = 'warm_welcome';\n  private currentSubPhase?: DiscoverySubPhase;\n  private userProfile: UserAssessmentProfile;\n  private phaseStartTime: Date = new Date();\n  private conversationHistory: string[] = [];\n\n  constructor(userName: string) {\n    this.userProfile = {\n      name: userName,\n      goals: [],\n      painPoints: [],\n      motivations: [],\n      emotionalTriggers: []\n    };\n  }\n\n  // Phase 1: Warm Welcome & Rapport Building\n  getWelcomePrompt(): string {\n    return `You are <PERSON>, an enthusiastic and expert AI personal trainer. You're meeting ${this.userProfile.name} for the first time.\n\nPHASE 1: WARM WELCOME & RAPPORT BUILDING (2-3 minutes)\n\nYour personality:\n- Warm, energetic, and genuinely excited to help\n- Use ${this.userProfile.name}'s name frequently\n- Slightly humorous but professional\n- Immediately build confidence with positive reinforcement\n\nYour opening should:\n1. Greet ${this.userProfile.name} with genuine enthusiasm\n2. Set expectations: \"This is all about YOU and your transformation\"\n3. Establish your expertise while showing you care\n4. Ask one engaging question to start building rapport\n\nKeep responses under 100 words. Be conversational and energetic.\n\nExample tone: \"Hey ${this.userProfile.name}! I'm absolutely thrilled to meet you! I'm Alex, and I'm here to be your personal trainer, your biggest cheerleader, and maybe occasionally your gentle drill sergeant when you need that extra push. This entire session is about YOU - your goals, your dreams, and creating a plan that's going to transform how you look and feel. I can already tell you're serious about making a change, and that's exactly the mindset that leads to incredible results! So tell me, ${this.userProfile.name}, what brought you here today? What's got you excited about starting this fitness journey?\"`;\n  }\n\n  // Phase 2: Deep Discovery & Emotional Connection\n  getDiscoveryPrompt(subPhase: DiscoverySubPhase, previousResponse?: string): string {\n    const basePrompt = `You are Alex, continuing the assessment with ${this.userProfile.name}. \n\nPHASE 2: DEEP DISCOVERY - ${subPhase.toUpperCase().replace('_', ' ')} (5-7 minutes total)\n\nYour approach:\n- Ask ONE focused question at a time\n- Mirror back what they say to show understanding\n- Dig deeper into emotional drivers\n- Use ${this.userProfile.name}'s name frequently\n- Keep responses under 100 words`;\n\n    switch (subPhase) {\n      case 'surface_level':\n        return `${basePrompt}\n\nSURFACE LEVEL QUESTIONS - Get the basics:\n- Current fitness routine and experience level\n- Specific goals (weight loss, muscle gain, strength, endurance)\n- Exercise preferences and dislikes\n- Time availability and scheduling preferences\n\nStart with their current situation. Be encouraging about whatever level they're at.`;\n\n      case 'pain_points':\n        return `${basePrompt}\n\nPAIN POINTS & OBSTACLES - Understand what's been holding them back:\n- What has prevented success in the past?\n- Previous trainer/app experiences (what worked/didn't work)\n- Current frustrations with fitness journey\n- Physical limitations or injuries\n\nBe empathetic and understanding. Show that these obstacles are normal and can be overcome.`;\n\n      case 'emotional_drivers':\n        return `${basePrompt}\n\nEMOTIONAL DRIVERS & MOTIVATIONS - This is the most important part:\n- Why is this goal important RIGHT NOW?\n- What would achieving this goal mean to you personally?\n- How would you feel in 3-6 months if you succeed?\n- What motivated you to try an AI trainer today?\n\nListen for emotional triggers. These are what will drive their commitment and purchasing decisions.`;\n\n      case 'support_style':\n        return `${basePrompt}\n\nSUPPORT & COACHING STYLE - Understand how they want to be coached:\n- Preferred motivation style (encouragement vs. tough love)\n- Need for accountability and check-ins\n- Interest in nutrition guidance alongside fitness\n- How they respond to challenges and setbacks\n\nThis helps you tailor your approach and sets up the service recommendations.`;\n\n      default:\n        return basePrompt;\n    }\n  }\n\n  // Phase 3: Live Physical Assessment\n  getPhysicalAssessmentPrompt(): string {\n    return `You are Alex, now conducting a live physical assessment with ${this.userProfile.name}.\n\nPHASE 3: LIVE PHYSICAL ASSESSMENT (3-5 minutes)\n\nYour role:\n- Guide them through movement evaluations\n- Provide real-time coaching and corrections\n- Give immediate positive feedback\n- Note strengths to build confidence\n- Identify improvement areas without being negative\n\nExercises to guide them through:\n1. Push-ups (form, range of motion, fatigue patterns)\n2. Bodyweight squats (depth, knee tracking, balance)\n3. Plank hold (core stability, form breakdown)\n4. Light jogging in place (coordination, breathing)\n5. Basic flexibility tests\n\nFor each exercise:\n- Explain what you're looking for\n- Give encouraging feedback during the movement\n- Provide gentle corrections\n- Celebrate their effort regardless of performance level\n\nKeep instructions clear and encouraging. Remember, this is about assessment AND building confidence.`;\n  }\n\n  // Phase 4: Vision Reveal & Future Projection\n  getVisionRevealPrompt(): string {\n    const goals = this.userProfile.goals.join(', ');\n    const motivations = this.userProfile.motivations.join(', ');\n    \n    return `You are Alex, now revealing ${this.userProfile.name}'s transformation potential.\n\nPHASE 4: VISION REVEAL & FUTURE PROJECTION (2-3 minutes)\n\nBased on what you've learned:\n- Goals: ${goals}\n- Motivations: ${motivations}\n- Pain points: ${this.userProfile.painPoints.join(', ')}\n\nYour approach:\n- Synthesize assessment data into personalized insights\n- Paint a vivid, specific picture of their transformation\n- Use visual language about how they'll look and feel\n- Show projected timeline for achieving their goals\n- Build excitement about what's possible with proper guidance\n- Connect to their emotional drivers\n\nExample structure:\n\"${this.userProfile.name}, based on everything we've discussed and what I've seen today, I'm genuinely excited about your potential. Here's what I see happening over the next [timeframe]...\"\n\nBe specific, visual, and emotionally compelling. This sets up the service recommendation.`;\n  }\n\n  // Phase 5: Natural Service Recommendation\n  getServiceRecommendationPrompt(): string {\n    return `You are Alex, naturally transitioning to service recommendations for ${this.userProfile.name}.\n\nPHASE 5: NATURAL SERVICE RECOMMENDATION (3-4 minutes)\n\nYour approach:\n- Frame as caring guidance, NOT sales\n- Address their specific pain points with tailored solutions\n- Present service tiers focusing on value and outcomes\n- Use phrases like \"I'd love to work closely with you...\"\n- Show the difference between going alone vs. having guidance\n- Create appropriate urgency without being pushy\n- End with soft close: \"Which option feels right for your goals?\"\n\nService Tiers to present:\n1. BASIC: Self-guided plan with monthly check-ins\n2. PREMIUM: Weekly coaching sessions with form analysis\n3. ELITE: Daily support with nutrition and lifestyle coaching\n\nConnect each tier to their specific needs and goals. Make it feel like expert advice, not a sales pitch.`;\n  }\n\n  // Analyze user response and extract insights\n  analyzeResponse(response: string, phase: AssessmentPhase): AIInsight[] {\n    const insights: AIInsight[] = [];\n    const lowerResponse = response.toLowerCase();\n\n    // Emotional trigger detection\n    const emotionalWords = ['frustrated', 'excited', 'scared', 'confident', 'motivated', 'tired', 'stressed'];\n    emotionalWords.forEach(word => {\n      if (lowerResponse.includes(word)) {\n        insights.push({\n          type: 'emotional_trigger',\n          confidence: 0.8,\n          insight: `User expressed feeling ${word}`,\n          phase,\n          priority: 'high'\n        });\n      }\n    });\n\n    // Goal clarity assessment\n    if (lowerResponse.includes('want to') || lowerResponse.includes('goal') || lowerResponse.includes('achieve')) {\n      insights.push({\n        type: 'goal_clarity',\n        confidence: 0.7,\n        insight: 'User is expressing clear goals',\n        phase,\n        priority: 'medium'\n      });\n    }\n\n    // Pain point identification\n    const painWords = ['struggle', 'difficult', 'hard', 'failed', 'quit', 'gave up', 'frustrated'];\n    painWords.forEach(word => {\n      if (lowerResponse.includes(word)) {\n        insights.push({\n          type: 'pain_point',\n          confidence: 0.8,\n          insight: `User mentioned struggling with: ${word}`,\n          phase,\n          priority: 'high'\n        });\n      }\n    });\n\n    // Readiness indicators\n    const readinessWords = ['ready', 'committed', 'serious', 'determined', 'motivated'];\n    readinessWords.forEach(word => {\n      if (lowerResponse.includes(word)) {\n        insights.push({\n          type: 'readiness',\n          confidence: 0.9,\n          insight: `High readiness indicator: ${word}`,\n          phase,\n          priority: 'high'\n        });\n      }\n    });\n\n    return insights;\n  }\n\n  // Update user profile based on conversation\n  updateProfile(response: string, phase: AssessmentPhase, subPhase?: DiscoverySubPhase): void {\n    const lowerResponse = response.toLowerCase();\n\n    if (phase === 'deep_discovery') {\n      switch (subPhase) {\n        case 'surface_level':\n          // Extract goals\n          if (lowerResponse.includes('lose weight') || lowerResponse.includes('weight loss')) {\n            this.userProfile.goals.push('weight_loss');\n          }\n          if (lowerResponse.includes('muscle') || lowerResponse.includes('strength')) {\n            this.userProfile.goals.push('muscle_gain');\n          }\n          break;\n\n        case 'pain_points':\n          // Extract pain points\n          if (lowerResponse.includes('time') || lowerResponse.includes('busy')) {\n            this.userProfile.painPoints.push('lack_of_time');\n          }\n          if (lowerResponse.includes('motivation') || lowerResponse.includes('consistent')) {\n            this.userProfile.painPoints.push('lack_of_motivation');\n          }\n          break;\n\n        case 'emotional_drivers':\n          // Extract motivations\n          if (lowerResponse.includes('confidence') || lowerResponse.includes('feel better')) {\n            this.userProfile.motivations.push('confidence');\n          }\n          if (lowerResponse.includes('health') || lowerResponse.includes('healthy')) {\n            this.userProfile.motivations.push('health');\n          }\n          break;\n      }\n    }\n\n    this.conversationHistory.push(response);\n  }\n\n  // Determine next phase transition\n  shouldTransitionPhase(responseCount: number, timeElapsed: number): boolean {\n    switch (this.currentPhase) {\n      case 'warm_welcome':\n        return responseCount >= 2 || timeElapsed > 180; // 3 minutes\n      case 'deep_discovery':\n        return responseCount >= 8 || timeElapsed > 420; // 7 minutes\n      case 'physical_assessment':\n        return responseCount >= 5 || timeElapsed > 300; // 5 minutes\n      case 'vision_reveal':\n        return responseCount >= 3 || timeElapsed > 180; // 3 minutes\n      case 'service_recommendation':\n        return responseCount >= 4 || timeElapsed > 240; // 4 minutes\n      default:\n        return false;\n    }\n  }\n\n  // Get next phase\n  getNextPhase(): AssessmentPhase | null {\n    const phases: AssessmentPhase[] = [\n      'warm_welcome',\n      'deep_discovery', \n      'physical_assessment',\n      'vision_reveal',\n      'service_recommendation',\n      'completed'\n    ];\n    \n    const currentIndex = phases.indexOf(this.currentPhase);\n    return currentIndex < phases.length - 1 ? phases[currentIndex + 1] : null;\n  }\n\n  // Getters and setters\n  getCurrentPhase(): AssessmentPhase { return this.currentPhase; }\n  setCurrentPhase(phase: AssessmentPhase): void { \n    this.currentPhase = phase;\n    this.phaseStartTime = new Date();\n  }\n  \n  getCurrentSubPhase(): DiscoverySubPhase | undefined { return this.currentSubPhase; }\n  setCurrentSubPhase(subPhase: DiscoverySubPhase): void { this.currentSubPhase = subPhase; }\n  \n  getUserProfile(): UserAssessmentProfile { return this.userProfile; }\n}\n"], "names": [], "mappings": ";;;;AAEO,MAAM;IACH,eAAgC,eAAe;IAC/C,gBAAoC;IACpC,YAAmC;IACnC,iBAAuB,IAAI,OAAO;IAClC,sBAAgC,EAAE,CAAC;IAE3C,YAAY,QAAgB,CAAE;QAC5B,IAAI,CAAC,WAAW,GAAG;YACjB,MAAM;YACN,OAAO,EAAE;YACT,YAAY,EAAE;YACd,aAAa,EAAE;YACf,mBAAmB,EAAE;QACvB;IACF;IAEA,2CAA2C;IAC3C,mBAA2B;QACzB,OAAO,CAAC,6EAA6E,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;;;;;;MAM3G,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;;;;;SAKrB,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;;;;;;;mBAOd,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,6cAA6c,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,2FAA2F,CAAC;IAC1mB;IAEA,iDAAiD;IACjD,mBAAmB,QAA2B,EAAE,gBAAyB,EAAU;QACjF,MAAM,aAAa,CAAC,6CAA6C,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;;0BAEnE,EAAE,SAAS,WAAW,GAAG,OAAO,CAAC,KAAK,KAAK;;;;;;MAM/D,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;gCACE,CAAC;QAE7B,OAAQ;YACN,KAAK;gBACH,OAAO,GAAG,WAAW;;;;;;;;mFAQsD,CAAC;YAE9E,KAAK;gBACH,OAAO,GAAG,WAAW;;;;;;;;0FAQ6D,CAAC;YAErF,KAAK;gBACH,OAAO,GAAG,WAAW;;;;;;;;mGAQsE,CAAC;YAE9F,KAAK;gBACH,OAAO,GAAG,WAAW;;;;;;;;4EAQ+C,CAAC;YAEvE;gBACE,OAAO;QACX;IACF;IAEA,oCAAoC;IACpC,8BAAsC;QACpC,OAAO,CAAC,6DAA6D,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;oGAwBG,CAAC;IACnG;IAEA,6CAA6C;IAC7C,wBAAgC;QAC9B,MAAM,QAAQ,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC;QAC1C,MAAM,cAAc,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC;QAEtD,OAAO,CAAC,4BAA4B,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;;;;;SAKvD,EAAE,MAAM;eACF,EAAE,YAAY;eACd,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM;;;;;;;;;;;CAWvD,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;;yFAEgE,CAAC;IACxF;IAEA,0CAA0C;IAC1C,iCAAyC;QACvC,OAAO,CAAC,qEAAqE,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;wGAkBD,CAAC;IACvG;IAEA,6CAA6C;IAC7C,gBAAgB,QAAgB,EAAE,KAAsB,EAAe;QACrE,MAAM,WAAwB,EAAE;QAChC,MAAM,gBAAgB,SAAS,WAAW;QAE1C,8BAA8B;QAC9B,MAAM,iBAAiB;YAAC;YAAc;YAAW;YAAU;YAAa;YAAa;YAAS;SAAW;QACzG,eAAe,OAAO,CAAC,CAAA;YACrB,IAAI,cAAc,QAAQ,CAAC,OAAO;gBAChC,SAAS,IAAI,CAAC;oBACZ,MAAM;oBACN,YAAY;oBACZ,SAAS,CAAC,uBAAuB,EAAE,MAAM;oBACzC;oBACA,UAAU;gBACZ;YACF;QACF;QAEA,0BAA0B;QAC1B,IAAI,cAAc,QAAQ,CAAC,cAAc,cAAc,QAAQ,CAAC,WAAW,cAAc,QAAQ,CAAC,YAAY;YAC5G,SAAS,IAAI,CAAC;gBACZ,MAAM;gBACN,YAAY;gBACZ,SAAS;gBACT;gBACA,UAAU;YACZ;QACF;QAEA,4BAA4B;QAC5B,MAAM,YAAY;YAAC;YAAY;YAAa;YAAQ;YAAU;YAAQ;YAAW;SAAa;QAC9F,UAAU,OAAO,CAAC,CAAA;YAChB,IAAI,cAAc,QAAQ,CAAC,OAAO;gBAChC,SAAS,IAAI,CAAC;oBACZ,MAAM;oBACN,YAAY;oBACZ,SAAS,CAAC,gCAAgC,EAAE,MAAM;oBAClD;oBACA,UAAU;gBACZ;YACF;QACF;QAEA,uBAAuB;QACvB,MAAM,iBAAiB;YAAC;YAAS;YAAa;YAAW;YAAc;SAAY;QACnF,eAAe,OAAO,CAAC,CAAA;YACrB,IAAI,cAAc,QAAQ,CAAC,OAAO;gBAChC,SAAS,IAAI,CAAC;oBACZ,MAAM;oBACN,YAAY;oBACZ,SAAS,CAAC,0BAA0B,EAAE,MAAM;oBAC5C;oBACA,UAAU;gBACZ;YACF;QACF;QAEA,OAAO;IACT;IAEA,4CAA4C;IAC5C,cAAc,QAAgB,EAAE,KAAsB,EAAE,QAA4B,EAAQ;QAC1F,MAAM,gBAAgB,SAAS,WAAW;QAE1C,IAAI,UAAU,kBAAkB;YAC9B,OAAQ;gBACN,KAAK;oBACH,gBAAgB;oBAChB,IAAI,cAAc,QAAQ,CAAC,kBAAkB,cAAc,QAAQ,CAAC,gBAAgB;wBAClF,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC;oBAC9B;oBACA,IAAI,cAAc,QAAQ,CAAC,aAAa,cAAc,QAAQ,CAAC,aAAa;wBAC1E,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC;oBAC9B;oBACA;gBAEF,KAAK;oBACH,sBAAsB;oBACtB,IAAI,cAAc,QAAQ,CAAC,WAAW,cAAc,QAAQ,CAAC,SAAS;wBACpE,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC;oBACnC;oBACA,IAAI,cAAc,QAAQ,CAAC,iBAAiB,cAAc,QAAQ,CAAC,eAAe;wBAChF,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC;oBACnC;oBACA;gBAEF,KAAK;oBACH,sBAAsB;oBACtB,IAAI,cAAc,QAAQ,CAAC,iBAAiB,cAAc,QAAQ,CAAC,gBAAgB;wBACjF,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC;oBACpC;oBACA,IAAI,cAAc,QAAQ,CAAC,aAAa,cAAc,QAAQ,CAAC,YAAY;wBACzE,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC;oBACpC;oBACA;YACJ;QACF;QAEA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;IAChC;IAEA,kCAAkC;IAClC,sBAAsB,aAAqB,EAAE,WAAmB,EAAW;QACzE,OAAQ,IAAI,CAAC,YAAY;YACvB,KAAK;gBACH,OAAO,iBAAiB,KAAK,cAAc,KAAK,YAAY;YAC9D,KAAK;gBACH,OAAO,iBAAiB,KAAK,cAAc,KAAK,YAAY;YAC9D,KAAK;gBACH,OAAO,iBAAiB,KAAK,cAAc,KAAK,YAAY;YAC9D,KAAK;gBACH,OAAO,iBAAiB,KAAK,cAAc,KAAK,YAAY;YAC9D,KAAK;gBACH,OAAO,iBAAiB,KAAK,cAAc,KAAK,YAAY;YAC9D;gBACE,OAAO;QACX;IACF;IAEA,iBAAiB;IACjB,eAAuC;QACrC,MAAM,SAA4B;YAChC;YACA;YACA;YACA;YACA;YACA;SACD;QAED,MAAM,eAAe,OAAO,OAAO,CAAC,IAAI,CAAC,YAAY;QACrD,OAAO,eAAe,OAAO,MAAM,GAAG,IAAI,MAAM,CAAC,eAAe,EAAE,GAAG;IACvE;IAEA,sBAAsB;IACtB,kBAAmC;QAAE,OAAO,IAAI,CAAC,YAAY;IAAE;IAC/D,gBAAgB,KAAsB,EAAQ;QAC5C,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,cAAc,GAAG,IAAI;IAC5B;IAEA,qBAAoD;QAAE,OAAO,IAAI,CAAC,eAAe;IAAE;IACnF,mBAAmB,QAA2B,EAAQ;QAAE,IAAI,CAAC,eAAe,GAAG;IAAU;IAEzF,iBAAwC;QAAE,OAAO,IAAI,CAAC,WAAW;IAAE;AACrE", "debugId": null}}, {"offset": {"line": 394, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trainer%20test/ai-trainer-assessment/src/app/api/trainer-chat/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport OpenAI from 'openai';\nimport { AssessmentPhase, ConversationMessage, AIInsight, DiscoverySubPhase } from '@/types';\nimport { ConversationEngine } from '@/services/conversationEngine';\n\nconst openai = new OpenAI({\n  apiKey: process.env.OPENAI_API_KEY,\n});\n\n// Store conversation engines per session (in production, use Redis or database)\nconst conversationEngines = new Map<string, ConversationEngine>();\n\nfunction getOrCreateEngine(sessionId: string, userName: string): ConversationEngine {\n  if (!conversationEngines.has(sessionId)) {\n    conversationEngines.set(sessionId, new ConversationEngine(userName));\n  }\n  return conversationEngines.get(sessionId)!;\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { \n      message, \n      phase, \n      conversationHistory, \n      userProfile \n    }: {\n      message: string;\n      phase: AssessmentPhase;\n      conversationHistory: ConversationMessage[];\n      userProfile?: any;\n    } = body;\n\n    if (!message || !phase) {\n      return NextResponse.json(\n        { error: 'Message and phase are required' },\n        { status: 400 }\n      );\n    }\n\n    // Build conversation context\n    const contextMessages = conversationHistory.map(msg => ({\n      role: msg.role === 'trainer' ? 'assistant' : 'user',\n      content: msg.content\n    }));\n\n    // Add current user message\n    contextMessages.push({\n      role: 'user',\n      content: message\n    });\n\n    // Get or create conversation engine for this session\n    const userName = userProfile?.name || 'there';\n    const sessionId = userProfile?.sessionId || 'default';\n    const engine = getOrCreateEngine(sessionId, userName);\n\n    // Update engine state\n    engine.setCurrentPhase(phase);\n    const subPhase = userProfile?.subPhase as DiscoverySubPhase;\n    if (subPhase) {\n      engine.setCurrentSubPhase(subPhase);\n    }\n\n    // Update user profile based on response\n    engine.updateProfile(message, phase, subPhase);\n\n    // Get phase-specific system prompt\n    let systemPrompt: string;\n    switch (phase) {\n      case 'warm_welcome':\n        systemPrompt = engine.getWelcomePrompt();\n        break;\n      case 'deep_discovery':\n        systemPrompt = engine.getDiscoveryPrompt(subPhase || 'surface_level', message);\n        break;\n      case 'physical_assessment':\n        systemPrompt = engine.getPhysicalAssessmentPrompt();\n        break;\n      case 'vision_reveal':\n        systemPrompt = engine.getVisionRevealPrompt();\n        break;\n      case 'service_recommendation':\n        systemPrompt = engine.getServiceRecommendationPrompt();\n        break;\n      default:\n        systemPrompt = engine.getWelcomePrompt();\n    }\n\n    const response = await openai.chat.completions.create({\n      model: 'gpt-4',\n      messages: [\n        {\n          role: 'system',\n          content: systemPrompt\n        },\n        ...contextMessages.slice(-6) // Keep last 6 messages for context\n      ],\n      temperature: 0.8,\n      max_tokens: 200,\n      presence_penalty: 0.1,\n      frequency_penalty: 0.1\n    });\n\n    const trainerResponse = response.choices[0]?.message?.content;\n\n    if (!trainerResponse) {\n      throw new Error('No response generated');\n    }\n\n    // Analyze the conversation for insights\n    const insights = engine.analyzeResponse(message, phase);\n\n    // Determine if phase should transition\n    const responseCount = conversationHistory.filter(msg => msg.role === 'user').length + 1;\n    const timeElapsed = (Date.now() - new Date().getTime()) / 1000; // Simplified\n    const shouldTransition = engine.shouldTransitionPhase(responseCount, timeElapsed);\n    const nextPhase = shouldTransition ? engine.getNextPhase() : null;\n\n    return NextResponse.json({\n      response: trainerResponse,\n      insights,\n      userProfile: engine.getUserProfile(),\n      shouldTransition,\n      nextPhase,\n      currentSubPhase: engine.getCurrentSubPhase(),\n      success: true\n    });\n\n  } catch (error) {\n    console.error('Trainer chat error:', error);\n    \n    return NextResponse.json(\n      { \n        error: 'Failed to generate trainer response',\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n\n// Legacy function removed - using ConversationEngine.analyzeResponse instead\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAEA;;;;AAEA,MAAM,SAAS,IAAI,mLAAM,CAAC;IACxB,QAAQ,QAAQ,GAAG,CAAC,cAAc;AACpC;AAEA,gFAAgF;AAChF,MAAM,sBAAsB,IAAI;AAEhC,SAAS,kBAAkB,SAAiB,EAAE,QAAgB;IAC5D,IAAI,CAAC,oBAAoB,GAAG,CAAC,YAAY;QACvC,oBAAoB,GAAG,CAAC,WAAW,IAAI,6JAAkB,CAAC;IAC5D;IACA,OAAO,oBAAoB,GAAG,CAAC;AACjC;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,OAAO,EACP,KAAK,EACL,mBAAmB,EACnB,WAAW,EACZ,GAKG;QAEJ,IAAI,CAAC,WAAW,CAAC,OAAO;YACtB,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiC,GAC1C;gBAAE,QAAQ;YAAI;QAElB;QAEA,6BAA6B;QAC7B,MAAM,kBAAkB,oBAAoB,GAAG,CAAC,CAAA,MAAO,CAAC;gBACtD,MAAM,IAAI,IAAI,KAAK,YAAY,cAAc;gBAC7C,SAAS,IAAI,OAAO;YACtB,CAAC;QAED,2BAA2B;QAC3B,gBAAgB,IAAI,CAAC;YACnB,MAAM;YACN,SAAS;QACX;QAEA,qDAAqD;QACrD,MAAM,WAAW,aAAa,QAAQ;QACtC,MAAM,YAAY,aAAa,aAAa;QAC5C,MAAM,SAAS,kBAAkB,WAAW;QAE5C,sBAAsB;QACtB,OAAO,eAAe,CAAC;QACvB,MAAM,WAAW,aAAa;QAC9B,IAAI,UAAU;YACZ,OAAO,kBAAkB,CAAC;QAC5B;QAEA,wCAAwC;QACxC,OAAO,aAAa,CAAC,SAAS,OAAO;QAErC,mCAAmC;QACnC,IAAI;QACJ,OAAQ;YACN,KAAK;gBACH,eAAe,OAAO,gBAAgB;gBACtC;YACF,KAAK;gBACH,eAAe,OAAO,kBAAkB,CAAC,YAAY,iBAAiB;gBACtE;YACF,KAAK;gBACH,eAAe,OAAO,2BAA2B;gBACjD;YACF,KAAK;gBACH,eAAe,OAAO,qBAAqB;gBAC3C;YACF,KAAK;gBACH,eAAe,OAAO,8BAA8B;gBACpD;YACF;gBACE,eAAe,OAAO,gBAAgB;QAC1C;QAEA,MAAM,WAAW,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACpD,OAAO;YACP,UAAU;gBACR;oBACE,MAAM;oBACN,SAAS;gBACX;mBACG,gBAAgB,KAAK,CAAC,CAAC,GAAG,mCAAmC;aACjE;YACD,aAAa;YACb,YAAY;YACZ,kBAAkB;YAClB,mBAAmB;QACrB;QAEA,MAAM,kBAAkB,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;QAEtD,IAAI,CAAC,iBAAiB;YACpB,MAAM,IAAI,MAAM;QAClB;QAEA,wCAAwC;QACxC,MAAM,WAAW,OAAO,eAAe,CAAC,SAAS;QAEjD,uCAAuC;QACvC,MAAM,gBAAgB,oBAAoB,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK,QAAQ,MAAM,GAAG;QACtF,MAAM,cAAc,CAAC,KAAK,GAAG,KAAK,IAAI,OAAO,OAAO,EAAE,IAAI,MAAM,aAAa;QAC7E,MAAM,mBAAmB,OAAO,qBAAqB,CAAC,eAAe;QACrE,MAAM,YAAY,mBAAmB,OAAO,YAAY,KAAK;QAE7D,OAAO,gJAAY,CAAC,IAAI,CAAC;YACvB,UAAU;YACV;YACA,aAAa,OAAO,cAAc;YAClC;YACA;YACA,iBAAiB,OAAO,kBAAkB;YAC1C,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QAErC,OAAO,gJAAY,CAAC,IAAI,CACtB;YACE,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF,EAEA,6EAA6E", "debugId": null}}]}