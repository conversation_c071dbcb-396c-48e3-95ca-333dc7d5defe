// Core Assessment Types
export interface UserProfile {
  id: string;
  name: string;
  email: string;
  age?: number;
  gender?: 'male' | 'female' | 'other';
  height?: number; // in cm
  weight?: number; // in kg
  fitnessLevel: 'beginner' | 'intermediate' | 'advanced';
  goals: FitnessGoal[];
  createdAt: Date;
  updatedAt: Date;
}

export interface FitnessGoal {
  id: string;
  type: 'weight_loss' | 'muscle_gain' | 'strength' | 'endurance' | 'flexibility' | 'general_fitness';
  description: string;
  targetValue?: number;
  targetDate?: Date;
  priority: 'high' | 'medium' | 'low';
}

// Assessment Data Types
export interface AssessmentSession {
  id: string;
  userId: string;
  phase: AssessmentPhase;
  subPhase?: DiscoverySubPhase;
  startedAt: Date;
  completedAt?: Date;
  responses: AssessmentResponse[];
  physicalAssessment?: PhysicalAssessment;
  aiInsights: AIInsight[];
  userProfile: UserAssessmentProfile;
  recommendedPlan?: FitnessPlan;
  phaseTimings: Record<AssessmentPhase, { start: Date; end?: Date; duration?: number }>;
}

export interface UserAssessmentProfile {
  name: string;
  fitnessLevel?: 'beginner' | 'intermediate' | 'advanced';
  goals: string[];
  painPoints: string[];
  motivations: string[];
  timeAvailability?: string;
  preferredStyle?: 'encouragement' | 'tough_love' | 'balanced';
  previousExperience?: string;
  physicalLimitations?: string[];
  emotionalTriggers: string[];
}

export type AssessmentPhase =
  | 'warm_welcome'
  | 'deep_discovery'
  | 'physical_assessment'
  | 'vision_reveal'
  | 'service_recommendation'
  | 'completed';

export type DiscoverySubPhase =
  | 'surface_level'
  | 'pain_points'
  | 'emotional_drivers'
  | 'support_style';

export type PhysicalExercise =
  | 'push_ups'
  | 'squats'
  | 'plank'
  | 'jogging'
  | 'flexibility';

export interface AssessmentResponse {
  questionId: string;
  question: string;
  answer: string;
  timestamp: Date;
  emotionalTone?: 'excited' | 'frustrated' | 'motivated' | 'uncertain';
}

export interface PhysicalAssessment {
  movements: MovementAnalysis[];
  overallScore: number;
  strengths: string[];
  weaknesses: string[];
  riskFactors: string[];
}

export interface MovementAnalysis {
  exercise: string;
  reps: number;
  formScore: number; // 0-100
  feedback: string[];
  keyPoints: PoseKeyPoint[];
}

export interface PoseKeyPoint {
  x: number;
  y: number;
  z?: number;
  visibility: number;
  landmark: string;
}

// AI Conversation Types
export interface ConversationMessage {
  id: string;
  role: 'user' | 'trainer';
  content: string;
  timestamp: Date;
  audioUrl?: string;
  transcription?: string;
}

export interface AIInsight {
  type: 'motivation' | 'pain_point' | 'goal_clarity' | 'readiness' | 'upsell_opportunity' | 'emotional_trigger' | 'coaching_style' | 'urgency_indicator';
  confidence: number; // 0-1
  insight: string;
  suggestedResponse?: string;
  phase: AssessmentPhase;
  priority: 'high' | 'medium' | 'low';
}

export interface ServiceTier {
  id: string;
  name: string;
  price: number;
  duration: number; // weeks
  features: string[];
  benefits: string[];
  targetPersona: string[];
  urgencyFactors: string[];
}

// Speech and Avatar Types
export interface SpeechState {
  isListening: boolean;
  isProcessing: boolean;
  isSpeaking: boolean;
  transcript: string;
  confidence: number;
}

export interface AvatarState {
  isAnimating: boolean;
  currentAnimation: 'idle' | 'talking' | 'listening' | 'thinking';
  mouthSyncData?: MouthSyncData;
}

export interface MouthSyncData {
  phonemes: Phoneme[];
  duration: number;
  currentTime: number;
}

export interface Phoneme {
  phoneme: string;
  start: number;
  end: number;
  intensity: number;
}

// WebRTC Types
export interface VideoCallState {
  isConnected: boolean;
  isVideoEnabled: boolean;
  isAudioEnabled: boolean;
  isRecording: boolean;
  stream?: MediaStream;
  peerConnection?: RTCPeerConnection;
}

// Fitness Plan Types
export interface FitnessPlan {
  id: string;
  userId: string;
  type: 'basic' | 'premium' | 'elite';
  duration: number; // weeks
  workouts: Workout[];
  nutritionPlan?: NutritionPlan;
  projectedResults: ProjectedResults;
  price: number;
  features: string[];
}

export interface Workout {
  id: string;
  name: string;
  description: string;
  duration: number; // minutes
  exercises: Exercise[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  equipment: string[];
}

export interface Exercise {
  id: string;
  name: string;
  description: string;
  sets: number;
  reps: number | string;
  restTime: number;
  instructions: string[];
  videoUrl?: string;
  muscleGroups: string[];
}

export interface NutritionPlan {
  dailyCalories: number;
  macros: {
    protein: number;
    carbs: number;
    fat: number;
  };
  mealSuggestions: MealSuggestion[];
}

export interface MealSuggestion {
  type: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  name: string;
  calories: number;
  ingredients: string[];
  instructions: string[];
}

export interface ProjectedResults {
  timeline: number;
  weightChange: number;
  bodyFatChange: number;
  strengthGains: Record<string, number>;
  visualProjection?: string;
}

// UI State Types
export interface UIState {
  currentPhase: AssessmentPhase;
  isLoading: boolean;
  error?: string;
  showUpsell: boolean;
  selectedPlan?: 'basic' | 'premium' | 'elite';
}
