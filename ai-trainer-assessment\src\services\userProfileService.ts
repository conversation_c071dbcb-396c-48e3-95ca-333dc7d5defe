import { UserAssessmentProfile, ConversationMessage, AssessmentSession, AIInsight, PhysicalAssessment } from '@/types';

export interface PersistentUserProfile extends UserAssessmentProfile {
  id: string;
  email?: string;
  createdAt: Date;
  lastActiveAt: Date;
  totalSessions: number;
  assessmentHistory: AssessmentSession[];
  conversationHistory: ConversationMessage[];
  progressTracking: ProgressEntry[];
  preferences: UserPreferences;
  subscription?: SubscriptionInfo;
}

export interface ProgressEntry {
  id: string;
  date: Date;
  weight?: number;
  bodyFat?: number;
  measurements?: Record<string, number>;
  photos?: string[];
  notes?: string;
  workoutCompleted?: boolean;
  mood?: 'excellent' | 'good' | 'okay' | 'struggling';
}

export interface UserPreferences {
  preferredWorkoutTime: string;
  trainerPersonality: string; // personality ID
  reminderSettings: {
    workouts: boolean;
    nutrition: boolean;
    checkIns: boolean;
  };
  voiceSettings: {
    speed: number;
    volume: number;
    preferredVoice: string;
  };
  privacySettings: {
    shareProgress: boolean;
    allowAnalytics: boolean;
  };
}

export interface SubscriptionInfo {
  tier: 'basic' | 'premium' | 'elite';
  startDate: Date;
  endDate: Date;
  status: 'active' | 'cancelled' | 'expired';
  autoRenew: boolean;
}

export class UserProfileService {
  private static readonly STORAGE_KEY = 'ai_trainer_profiles';
  private static readonly CURRENT_USER_KEY = 'ai_trainer_current_user';

  // Create or get user profile
  static async createOrGetProfile(name: string, email?: string): Promise<PersistentUserProfile> {
    const profiles = this.getAllProfiles();
    
    // Try to find existing profile by name or email
    let existingProfile = profiles.find(p => 
      p.name.toLowerCase() === name.toLowerCase() || 
      (email && p.email?.toLowerCase() === email.toLowerCase())
    );

    if (existingProfile) {
      // Update last active time
      existingProfile.lastActiveAt = new Date();
      this.saveProfile(existingProfile);
      this.setCurrentUser(existingProfile.id);
      return existingProfile;
    }

    // Create new profile
    const newProfile: PersistentUserProfile = {
      id: this.generateUserId(),
      name,
      email,
      createdAt: new Date(),
      lastActiveAt: new Date(),
      totalSessions: 0,
      goals: [],
      painPoints: [],
      motivations: [],
      emotionalTriggers: [],
      assessmentHistory: [],
      conversationHistory: [],
      progressTracking: [],
      preferences: {
        preferredWorkoutTime: '18:00',
        trainerPersonality: 'supportive_coach', // Default to supportive
        reminderSettings: {
          workouts: true,
          nutrition: true,
          checkIns: true
        },
        voiceSettings: {
          speed: 1.0,
          volume: 0.8,
          preferredVoice: 'nova'
        },
        privacySettings: {
          shareProgress: false,
          allowAnalytics: true
        }
      }
    };

    this.saveProfile(newProfile);
    this.setCurrentUser(newProfile.id);
    return newProfile;
  }

  // Get current user profile
  static getCurrentProfile(): PersistentUserProfile | null {
    const currentUserId = localStorage.getItem(this.CURRENT_USER_KEY);
    if (!currentUserId) return null;

    const profiles = this.getAllProfiles();
    return profiles.find(p => p.id === currentUserId) || null;
  }

  // Update user profile
  static updateProfile(updates: Partial<PersistentUserProfile>): void {
    const currentProfile = this.getCurrentProfile();
    if (!currentProfile) return;

    const updatedProfile = { ...currentProfile, ...updates, lastActiveAt: new Date() };
    this.saveProfile(updatedProfile);
  }

  // Add conversation message
  static addConversationMessage(message: ConversationMessage): void {
    const profile = this.getCurrentProfile();
    if (!profile) return;

    profile.conversationHistory.push(message);
    
    // Keep only last 100 messages to prevent storage bloat
    if (profile.conversationHistory.length > 100) {
      profile.conversationHistory = profile.conversationHistory.slice(-100);
    }

    this.saveProfile(profile);
  }

  // Add assessment session
  static addAssessmentSession(session: AssessmentSession): void {
    const profile = this.getCurrentProfile();
    if (!profile) return;

    profile.assessmentHistory.push(session);
    profile.totalSessions += 1;
    
    // Update profile with session insights
    if (session.userProfile) {
      profile.goals = [...new Set([...profile.goals, ...session.userProfile.goals])];
      profile.painPoints = [...new Set([...profile.painPoints, ...session.userProfile.painPoints])];
      profile.motivations = [...new Set([...profile.motivations, ...session.userProfile.motivations])];
      profile.emotionalTriggers = [...new Set([...profile.emotionalTriggers, ...session.userProfile.emotionalTriggers])];
      
      if (session.userProfile.fitnessLevel) {
        profile.fitnessLevel = session.userProfile.fitnessLevel;
      }
      if (session.userProfile.preferredStyle) {
        profile.preferredStyle = session.userProfile.preferredStyle;
      }
    }

    this.saveProfile(profile);
  }

  // Add progress entry
  static addProgressEntry(entry: Omit<ProgressEntry, 'id'>): void {
    const profile = this.getCurrentProfile();
    if (!profile) return;

    const progressEntry: ProgressEntry = {
      ...entry,
      id: this.generateProgressId()
    };

    profile.progressTracking.push(progressEntry);
    this.saveProfile(profile);
  }

  // Get conversation context for AI
  static getConversationContext(limit: number = 10): ConversationMessage[] {
    const profile = this.getCurrentProfile();
    if (!profile) return [];

    return profile.conversationHistory.slice(-limit);
  }

  // Get user summary for AI context
  static getUserSummaryForAI(): string {
    const profile = this.getCurrentProfile();
    if (!profile) return '';

    const recentProgress = profile.progressTracking.slice(-3);
    const lastSession = profile.assessmentHistory[profile.assessmentHistory.length - 1];

    let summary = `User Profile Summary for ${profile.name}:\n`;
    summary += `- Member since: ${profile.createdAt.toDateString()}\n`;
    summary += `- Total sessions: ${profile.totalSessions}\n`;
    summary += `- Fitness level: ${profile.fitnessLevel || 'Not assessed'}\n`;
    summary += `- Primary goals: ${profile.goals.join(', ') || 'Not specified'}\n`;
    summary += `- Main pain points: ${profile.painPoints.join(', ') || 'None identified'}\n`;
    summary += `- Key motivations: ${profile.motivations.join(', ') || 'Not specified'}\n`;
    summary += `- Preferred coaching style: ${profile.preferredStyle || 'Not specified'}\n`;

    if (recentProgress.length > 0) {
      summary += `- Recent progress: ${recentProgress.length} entries in tracking\n`;
      const latest = recentProgress[recentProgress.length - 1];
      if (latest.weight) summary += `- Current weight: ${latest.weight} lbs\n`;
      if (latest.mood) summary += `- Recent mood: ${latest.mood}\n`;
    }

    if (lastSession) {
      summary += `- Last assessment: ${lastSession.startedAt.toDateString()}\n`;
      summary += `- Last phase completed: ${lastSession.phase}\n`;
    }

    return summary;
  }

  // Private helper methods
  private static getAllProfiles(): PersistentUserProfile[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      return stored ? JSON.parse(stored, this.dateReviver) : [];
    } catch (error) {
      console.error('Error loading profiles:', error);
      return [];
    }
  }

  private static saveProfile(profile: PersistentUserProfile): void {
    try {
      const profiles = this.getAllProfiles();
      const index = profiles.findIndex(p => p.id === profile.id);
      
      if (index >= 0) {
        profiles[index] = profile;
      } else {
        profiles.push(profile);
      }

      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(profiles));
    } catch (error) {
      console.error('Error saving profile:', error);
    }
  }

  private static setCurrentUser(userId: string): void {
    localStorage.setItem(this.CURRENT_USER_KEY, userId);
  }

  private static generateUserId(): string {
    return `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private static generateProgressId(): string {
    return `progress_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Date reviver for JSON.parse to handle Date objects
  private static dateReviver(key: string, value: any): any {
    if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(value)) {
      return new Date(value);
    }
    return value;
  }

  // Export/Import functionality for data portability
  static exportUserData(): string {
    const profile = this.getCurrentProfile();
    if (!profile) throw new Error('No current user profile');

    return JSON.stringify(profile, null, 2);
  }

  static importUserData(jsonData: string): void {
    try {
      const profile = JSON.parse(jsonData, this.dateReviver) as PersistentUserProfile;
      this.saveProfile(profile);
      this.setCurrentUser(profile.id);
    } catch (error) {
      throw new Error('Invalid user data format');
    }
  }

  // Clear all data (for privacy/reset)
  static clearAllData(): void {
    localStorage.removeItem(this.STORAGE_KEY);
    localStorage.removeItem(this.CURRENT_USER_KEY);
  }

  // Get user statistics
  static getUserStats(): {
    totalConversations: number;
    totalSessions: number;
    memberSince: Date;
    progressEntries: number;
    currentStreak: number;
  } {
    const profile = this.getCurrentProfile();
    if (!profile) {
      return {
        totalConversations: 0,
        totalSessions: 0,
        memberSince: new Date(),
        progressEntries: 0,
        currentStreak: 0
      };
    }

    // Calculate current streak (days with activity)
    const now = new Date();
    let currentStreak = 0;
    const sortedProgress = profile.progressTracking
      .sort((a, b) => b.date.getTime() - a.date.getTime());

    for (const entry of sortedProgress) {
      const daysDiff = Math.floor((now.getTime() - entry.date.getTime()) / (1000 * 60 * 60 * 24));
      if (daysDiff <= currentStreak + 1) {
        currentStreak++;
      } else {
        break;
      }
    }

    return {
      totalConversations: profile.conversationHistory.length,
      totalSessions: profile.totalSessions,
      memberSince: profile.createdAt,
      progressEntries: profile.progressTracking.length,
      currentStreak
    };
  }
}
