{{# def.definitions }}
{{# def.errors }}
{{# def.setupKeyword }}
{{# def.setupNextLevel }}

{{
  var $currentBaseId = $it.baseId
    , $allSchemasEmpty = true;
}}

{{~ $schema:$sch:$i }}
  {{? {{# def.nonEmptySchema:$sch }} }}
    {{
      $allSchemasEmpty = false;
      $it.schema = $sch;
      $it.schemaPath = $schemaPath + '[' + $i + ']';
      $it.errSchemaPath = $errSchemaPath + '/' + $i;
    }}

    {{# def.insertSubschemaCode }}

    {{# def.ifResultValid }}
  {{?}}
{{~}}

{{? $breakOnError }}
  {{? $allSchemasEmpty }}
    if (true) {
  {{??}}
    {{= $closingBraces.slice(0,-1) }}
  {{?}}
{{?}}
