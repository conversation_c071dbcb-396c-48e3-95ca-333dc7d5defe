{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trainer%20test/ai-trainer-assessment/src/services/userProfileService.ts"], "sourcesContent": ["import { UserAssessmentProfile, ConversationMessage, AssessmentSession, AIInsight, PhysicalAssessment } from '@/types';\n\nexport interface PersistentUserProfile extends UserAssessmentProfile {\n  id: string;\n  email?: string;\n  createdAt: Date;\n  lastActiveAt: Date;\n  totalSessions: number;\n  assessmentHistory: AssessmentSession[];\n  conversationHistory: ConversationMessage[];\n  progressTracking: ProgressEntry[];\n  preferences: UserPreferences;\n  subscription?: SubscriptionInfo;\n}\n\nexport interface ProgressEntry {\n  id: string;\n  date: Date;\n  weight?: number;\n  bodyFat?: number;\n  measurements?: Record<string, number>;\n  photos?: string[];\n  notes?: string;\n  workoutCompleted?: boolean;\n  mood?: 'excellent' | 'good' | 'okay' | 'struggling';\n}\n\nexport interface UserPreferences {\n  preferredWorkoutTime: string;\n  trainerPersonality: string; // personality ID\n  reminderSettings: {\n    workouts: boolean;\n    nutrition: boolean;\n    checkIns: boolean;\n  };\n  voiceSettings: {\n    speed: number;\n    volume: number;\n    preferredVoice: string;\n  };\n  privacySettings: {\n    shareProgress: boolean;\n    allowAnalytics: boolean;\n  };\n}\n\nexport interface SubscriptionInfo {\n  tier: 'basic' | 'premium' | 'elite';\n  startDate: Date;\n  endDate: Date;\n  status: 'active' | 'cancelled' | 'expired';\n  autoRenew: boolean;\n}\n\nexport class UserProfileService {\n  private static readonly STORAGE_KEY = 'ai_trainer_profiles';\n  private static readonly CURRENT_USER_KEY = 'ai_trainer_current_user';\n\n  // Create or get user profile\n  static async createOrGetProfile(name: string, email?: string): Promise<PersistentUserProfile> {\n    const profiles = this.getAllProfiles();\n    \n    // Try to find existing profile by name or email\n    let existingProfile = profiles.find(p => \n      p.name.toLowerCase() === name.toLowerCase() || \n      (email && p.email?.toLowerCase() === email.toLowerCase())\n    );\n\n    if (existingProfile) {\n      // Update last active time\n      existingProfile.lastActiveAt = new Date();\n      this.saveProfile(existingProfile);\n      this.setCurrentUser(existingProfile.id);\n      return existingProfile;\n    }\n\n    // Create new profile\n    const newProfile: PersistentUserProfile = {\n      id: this.generateUserId(),\n      name,\n      email,\n      createdAt: new Date(),\n      lastActiveAt: new Date(),\n      totalSessions: 0,\n      goals: [],\n      painPoints: [],\n      motivations: [],\n      emotionalTriggers: [],\n      assessmentHistory: [],\n      conversationHistory: [],\n      progressTracking: [],\n      preferences: {\n        preferredWorkoutTime: '18:00',\n        trainerPersonality: 'supportive_coach', // Default to supportive\n        reminderSettings: {\n          workouts: true,\n          nutrition: true,\n          checkIns: true\n        },\n        voiceSettings: {\n          speed: 1.0,\n          volume: 0.8,\n          preferredVoice: 'nova'\n        },\n        privacySettings: {\n          shareProgress: false,\n          allowAnalytics: true\n        }\n      }\n    };\n\n    this.saveProfile(newProfile);\n    this.setCurrentUser(newProfile.id);\n    return newProfile;\n  }\n\n  // Get current user profile\n  static getCurrentProfile(): PersistentUserProfile | null {\n    const currentUserId = localStorage.getItem(this.CURRENT_USER_KEY);\n    if (!currentUserId) return null;\n\n    const profiles = this.getAllProfiles();\n    return profiles.find(p => p.id === currentUserId) || null;\n  }\n\n  // Update user profile\n  static updateProfile(updates: Partial<PersistentUserProfile>): void {\n    const currentProfile = this.getCurrentProfile();\n    if (!currentProfile) return;\n\n    const updatedProfile = { ...currentProfile, ...updates, lastActiveAt: new Date() };\n    this.saveProfile(updatedProfile);\n  }\n\n  // Add conversation message\n  static addConversationMessage(message: ConversationMessage): void {\n    const profile = this.getCurrentProfile();\n    if (!profile) return;\n\n    profile.conversationHistory.push(message);\n    \n    // Keep only last 100 messages to prevent storage bloat\n    if (profile.conversationHistory.length > 100) {\n      profile.conversationHistory = profile.conversationHistory.slice(-100);\n    }\n\n    this.saveProfile(profile);\n  }\n\n  // Add assessment session\n  static addAssessmentSession(session: AssessmentSession): void {\n    const profile = this.getCurrentProfile();\n    if (!profile) return;\n\n    profile.assessmentHistory.push(session);\n    profile.totalSessions += 1;\n    \n    // Update profile with session insights\n    if (session.userProfile) {\n      profile.goals = [...new Set([...profile.goals, ...session.userProfile.goals])];\n      profile.painPoints = [...new Set([...profile.painPoints, ...session.userProfile.painPoints])];\n      profile.motivations = [...new Set([...profile.motivations, ...session.userProfile.motivations])];\n      profile.emotionalTriggers = [...new Set([...profile.emotionalTriggers, ...session.userProfile.emotionalTriggers])];\n      \n      if (session.userProfile.fitnessLevel) {\n        profile.fitnessLevel = session.userProfile.fitnessLevel;\n      }\n      if (session.userProfile.preferredStyle) {\n        profile.preferredStyle = session.userProfile.preferredStyle;\n      }\n    }\n\n    this.saveProfile(profile);\n  }\n\n  // Add progress entry\n  static addProgressEntry(entry: Omit<ProgressEntry, 'id'>): void {\n    const profile = this.getCurrentProfile();\n    if (!profile) return;\n\n    const progressEntry: ProgressEntry = {\n      ...entry,\n      id: this.generateProgressId()\n    };\n\n    profile.progressTracking.push(progressEntry);\n    this.saveProfile(profile);\n  }\n\n  // Get conversation context for AI\n  static getConversationContext(limit: number = 10): ConversationMessage[] {\n    const profile = this.getCurrentProfile();\n    if (!profile) return [];\n\n    return profile.conversationHistory.slice(-limit);\n  }\n\n  // Get user summary for AI context\n  static getUserSummaryForAI(): string {\n    const profile = this.getCurrentProfile();\n    if (!profile) return '';\n\n    const recentProgress = profile.progressTracking.slice(-3);\n    const lastSession = profile.assessmentHistory[profile.assessmentHistory.length - 1];\n\n    let summary = `User Profile Summary for ${profile.name}:\\n`;\n    summary += `- Member since: ${profile.createdAt.toDateString()}\\n`;\n    summary += `- Total sessions: ${profile.totalSessions}\\n`;\n    summary += `- Fitness level: ${profile.fitnessLevel || 'Not assessed'}\\n`;\n    summary += `- Primary goals: ${profile.goals.join(', ') || 'Not specified'}\\n`;\n    summary += `- Main pain points: ${profile.painPoints.join(', ') || 'None identified'}\\n`;\n    summary += `- Key motivations: ${profile.motivations.join(', ') || 'Not specified'}\\n`;\n    summary += `- Preferred coaching style: ${profile.preferredStyle || 'Not specified'}\\n`;\n\n    if (recentProgress.length > 0) {\n      summary += `- Recent progress: ${recentProgress.length} entries in tracking\\n`;\n      const latest = recentProgress[recentProgress.length - 1];\n      if (latest.weight) summary += `- Current weight: ${latest.weight} lbs\\n`;\n      if (latest.mood) summary += `- Recent mood: ${latest.mood}\\n`;\n    }\n\n    if (lastSession) {\n      summary += `- Last assessment: ${lastSession.startedAt.toDateString()}\\n`;\n      summary += `- Last phase completed: ${lastSession.phase}\\n`;\n    }\n\n    return summary;\n  }\n\n  // Private helper methods\n  private static getAllProfiles(): PersistentUserProfile[] {\n    try {\n      const stored = localStorage.getItem(this.STORAGE_KEY);\n      return stored ? JSON.parse(stored, this.dateReviver) : [];\n    } catch (error) {\n      console.error('Error loading profiles:', error);\n      return [];\n    }\n  }\n\n  private static saveProfile(profile: PersistentUserProfile): void {\n    try {\n      const profiles = this.getAllProfiles();\n      const index = profiles.findIndex(p => p.id === profile.id);\n      \n      if (index >= 0) {\n        profiles[index] = profile;\n      } else {\n        profiles.push(profile);\n      }\n\n      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(profiles));\n    } catch (error) {\n      console.error('Error saving profile:', error);\n    }\n  }\n\n  private static setCurrentUser(userId: string): void {\n    localStorage.setItem(this.CURRENT_USER_KEY, userId);\n  }\n\n  private static generateUserId(): string {\n    return `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  private static generateProgressId(): string {\n    return `progress_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  // Date reviver for JSON.parse to handle Date objects\n  private static dateReviver(key: string, value: any): any {\n    if (typeof value === 'string' && /^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}/.test(value)) {\n      return new Date(value);\n    }\n    return value;\n  }\n\n  // Export/Import functionality for data portability\n  static exportUserData(): string {\n    const profile = this.getCurrentProfile();\n    if (!profile) throw new Error('No current user profile');\n\n    return JSON.stringify(profile, null, 2);\n  }\n\n  static importUserData(jsonData: string): void {\n    try {\n      const profile = JSON.parse(jsonData, this.dateReviver) as PersistentUserProfile;\n      this.saveProfile(profile);\n      this.setCurrentUser(profile.id);\n    } catch (error) {\n      throw new Error('Invalid user data format');\n    }\n  }\n\n  // Clear all data (for privacy/reset)\n  static clearAllData(): void {\n    localStorage.removeItem(this.STORAGE_KEY);\n    localStorage.removeItem(this.CURRENT_USER_KEY);\n  }\n\n  // Get user statistics\n  static getUserStats(): {\n    totalConversations: number;\n    totalSessions: number;\n    memberSince: Date;\n    progressEntries: number;\n    currentStreak: number;\n  } {\n    const profile = this.getCurrentProfile();\n    if (!profile) {\n      return {\n        totalConversations: 0,\n        totalSessions: 0,\n        memberSince: new Date(),\n        progressEntries: 0,\n        currentStreak: 0\n      };\n    }\n\n    // Calculate current streak (days with activity)\n    const now = new Date();\n    let currentStreak = 0;\n    const sortedProgress = profile.progressTracking\n      .sort((a, b) => b.date.getTime() - a.date.getTime());\n\n    for (const entry of sortedProgress) {\n      const daysDiff = Math.floor((now.getTime() - entry.date.getTime()) / (1000 * 60 * 60 * 24));\n      if (daysDiff <= currentStreak + 1) {\n        currentStreak++;\n      } else {\n        break;\n      }\n    }\n\n    return {\n      totalConversations: profile.conversationHistory.length,\n      totalSessions: profile.totalSessions,\n      memberSince: profile.createdAt,\n      progressEntries: profile.progressTracking.length,\n      currentStreak\n    };\n  }\n}\n"], "names": [], "mappings": ";;;;AAsDO,MAAM;IACX,OAAwB,cAAc,sBAAsB;IAC5D,OAAwB,mBAAmB,0BAA0B;IAErE,6BAA6B;IAC7B,aAAa,mBAAmB,IAAY,EAAE,KAAc,EAAkC;QAC5F,MAAM,WAAW,IAAI,CAAC,cAAc;QAEpC,gDAAgD;QAChD,IAAI,kBAAkB,SAAS,IAAI,CAAC,CAAA,IAClC,EAAE,IAAI,CAAC,WAAW,OAAO,KAAK,WAAW,MACxC,SAAS,EAAE,KAAK,EAAE,kBAAkB,MAAM,WAAW;QAGxD,IAAI,iBAAiB;YACnB,0BAA0B;YAC1B,gBAAgB,YAAY,GAAG,IAAI;YACnC,IAAI,CAAC,WAAW,CAAC;YACjB,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE;YACtC,OAAO;QACT;QAEA,qBAAqB;QACrB,MAAM,aAAoC;YACxC,IAAI,IAAI,CAAC,cAAc;YACvB;YACA;YACA,WAAW,IAAI;YACf,cAAc,IAAI;YAClB,eAAe;YACf,OAAO,EAAE;YACT,YAAY,EAAE;YACd,aAAa,EAAE;YACf,mBAAmB,EAAE;YACrB,mBAAmB,EAAE;YACrB,qBAAqB,EAAE;YACvB,kBAAkB,EAAE;YACpB,aAAa;gBACX,sBAAsB;gBACtB,oBAAoB;gBACpB,kBAAkB;oBAChB,UAAU;oBACV,WAAW;oBACX,UAAU;gBACZ;gBACA,eAAe;oBACb,OAAO;oBACP,QAAQ;oBACR,gBAAgB;gBAClB;gBACA,iBAAiB;oBACf,eAAe;oBACf,gBAAgB;gBAClB;YACF;QACF;QAEA,IAAI,CAAC,WAAW,CAAC;QACjB,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE;QACjC,OAAO;IACT;IAEA,2BAA2B;IAC3B,OAAO,oBAAkD;QACvD,MAAM,gBAAgB,aAAa,OAAO,CAAC,IAAI,CAAC,gBAAgB;QAChE,IAAI,CAAC,eAAe,OAAO;QAE3B,MAAM,WAAW,IAAI,CAAC,cAAc;QACpC,OAAO,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,kBAAkB;IACvD;IAEA,sBAAsB;IACtB,OAAO,cAAc,OAAuC,EAAQ;QAClE,MAAM,iBAAiB,IAAI,CAAC,iBAAiB;QAC7C,IAAI,CAAC,gBAAgB;QAErB,MAAM,iBAAiB;YAAE,GAAG,cAAc;YAAE,GAAG,OAAO;YAAE,cAAc,IAAI;QAAO;QACjF,IAAI,CAAC,WAAW,CAAC;IACnB;IAEA,2BAA2B;IAC3B,OAAO,uBAAuB,OAA4B,EAAQ;QAChE,MAAM,UAAU,IAAI,CAAC,iBAAiB;QACtC,IAAI,CAAC,SAAS;QAEd,QAAQ,mBAAmB,CAAC,IAAI,CAAC;QAEjC,uDAAuD;QACvD,IAAI,QAAQ,mBAAmB,CAAC,MAAM,GAAG,KAAK;YAC5C,QAAQ,mBAAmB,GAAG,QAAQ,mBAAmB,CAAC,KAAK,CAAC,CAAC;QACnE;QAEA,IAAI,CAAC,WAAW,CAAC;IACnB;IAEA,yBAAyB;IACzB,OAAO,qBAAqB,OAA0B,EAAQ;QAC5D,MAAM,UAAU,IAAI,CAAC,iBAAiB;QACtC,IAAI,CAAC,SAAS;QAEd,QAAQ,iBAAiB,CAAC,IAAI,CAAC;QAC/B,QAAQ,aAAa,IAAI;QAEzB,uCAAuC;QACvC,IAAI,QAAQ,WAAW,EAAE;YACvB,QAAQ,KAAK,GAAG;mBAAI,IAAI,IAAI;uBAAI,QAAQ,KAAK;uBAAK,QAAQ,WAAW,CAAC,KAAK;iBAAC;aAAE;YAC9E,QAAQ,UAAU,GAAG;mBAAI,IAAI,IAAI;uBAAI,QAAQ,UAAU;uBAAK,QAAQ,WAAW,CAAC,UAAU;iBAAC;aAAE;YAC7F,QAAQ,WAAW,GAAG;mBAAI,IAAI,IAAI;uBAAI,QAAQ,WAAW;uBAAK,QAAQ,WAAW,CAAC,WAAW;iBAAC;aAAE;YAChG,QAAQ,iBAAiB,GAAG;mBAAI,IAAI,IAAI;uBAAI,QAAQ,iBAAiB;uBAAK,QAAQ,WAAW,CAAC,iBAAiB;iBAAC;aAAE;YAElH,IAAI,QAAQ,WAAW,CAAC,YAAY,EAAE;gBACpC,QAAQ,YAAY,GAAG,QAAQ,WAAW,CAAC,YAAY;YACzD;YACA,IAAI,QAAQ,WAAW,CAAC,cAAc,EAAE;gBACtC,QAAQ,cAAc,GAAG,QAAQ,WAAW,CAAC,cAAc;YAC7D;QACF;QAEA,IAAI,CAAC,WAAW,CAAC;IACnB;IAEA,qBAAqB;IACrB,OAAO,iBAAiB,KAAgC,EAAQ;QAC9D,MAAM,UAAU,IAAI,CAAC,iBAAiB;QACtC,IAAI,CAAC,SAAS;QAEd,MAAM,gBAA+B;YACnC,GAAG,KAAK;YACR,IAAI,IAAI,CAAC,kBAAkB;QAC7B;QAEA,QAAQ,gBAAgB,CAAC,IAAI,CAAC;QAC9B,IAAI,CAAC,WAAW,CAAC;IACnB;IAEA,kCAAkC;IAClC,OAAO,uBAAuB,QAAgB,EAAE,EAAyB;QACvE,MAAM,UAAU,IAAI,CAAC,iBAAiB;QACtC,IAAI,CAAC,SAAS,OAAO,EAAE;QAEvB,OAAO,QAAQ,mBAAmB,CAAC,KAAK,CAAC,CAAC;IAC5C;IAEA,kCAAkC;IAClC,OAAO,sBAA8B;QACnC,MAAM,UAAU,IAAI,CAAC,iBAAiB;QACtC,IAAI,CAAC,SAAS,OAAO;QAErB,MAAM,iBAAiB,QAAQ,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACvD,MAAM,cAAc,QAAQ,iBAAiB,CAAC,QAAQ,iBAAiB,CAAC,MAAM,GAAG,EAAE;QAEnF,IAAI,UAAU,CAAC,yBAAyB,EAAE,QAAQ,IAAI,CAAC,GAAG,CAAC;QAC3D,WAAW,CAAC,gBAAgB,EAAE,QAAQ,SAAS,CAAC,YAAY,GAAG,EAAE,CAAC;QAClE,WAAW,CAAC,kBAAkB,EAAE,QAAQ,aAAa,CAAC,EAAE,CAAC;QACzD,WAAW,CAAC,iBAAiB,EAAE,QAAQ,YAAY,IAAI,eAAe,EAAE,CAAC;QACzE,WAAW,CAAC,iBAAiB,EAAE,QAAQ,KAAK,CAAC,IAAI,CAAC,SAAS,gBAAgB,EAAE,CAAC;QAC9E,WAAW,CAAC,oBAAoB,EAAE,QAAQ,UAAU,CAAC,IAAI,CAAC,SAAS,kBAAkB,EAAE,CAAC;QACxF,WAAW,CAAC,mBAAmB,EAAE,QAAQ,WAAW,CAAC,IAAI,CAAC,SAAS,gBAAgB,EAAE,CAAC;QACtF,WAAW,CAAC,4BAA4B,EAAE,QAAQ,cAAc,IAAI,gBAAgB,EAAE,CAAC;QAEvF,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,WAAW,CAAC,mBAAmB,EAAE,eAAe,MAAM,CAAC,sBAAsB,CAAC;YAC9E,MAAM,SAAS,cAAc,CAAC,eAAe,MAAM,GAAG,EAAE;YACxD,IAAI,OAAO,MAAM,EAAE,WAAW,CAAC,kBAAkB,EAAE,OAAO,MAAM,CAAC,MAAM,CAAC;YACxE,IAAI,OAAO,IAAI,EAAE,WAAW,CAAC,eAAe,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC;QAC/D;QAEA,IAAI,aAAa;YACf,WAAW,CAAC,mBAAmB,EAAE,YAAY,SAAS,CAAC,YAAY,GAAG,EAAE,CAAC;YACzE,WAAW,CAAC,wBAAwB,EAAE,YAAY,KAAK,CAAC,EAAE,CAAC;QAC7D;QAEA,OAAO;IACT;IAEA,yBAAyB;IACzB,OAAe,iBAA0C;QACvD,IAAI;YACF,MAAM,SAAS,aAAa,OAAO,CAAC,IAAI,CAAC,WAAW;YACpD,OAAO,SAAS,KAAK,KAAK,CAAC,QAAQ,IAAI,CAAC,WAAW,IAAI,EAAE;QAC3D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,EAAE;QACX;IACF;IAEA,OAAe,YAAY,OAA8B,EAAQ;QAC/D,IAAI;YACF,MAAM,WAAW,IAAI,CAAC,cAAc;YACpC,MAAM,QAAQ,SAAS,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,EAAE;YAEzD,IAAI,SAAS,GAAG;gBACd,QAAQ,CAAC,MAAM,GAAG;YACpB,OAAO;gBACL,SAAS,IAAI,CAAC;YAChB;YAEA,aAAa,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,SAAS,CAAC;QACxD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,OAAe,eAAe,MAAc,EAAQ;QAClD,aAAa,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE;IAC9C;IAEA,OAAe,iBAAyB;QACtC,OAAO,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IACxE;IAEA,OAAe,qBAA6B;QAC1C,OAAO,CAAC,SAAS,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAC5E;IAEA,qDAAqD;IACrD,OAAe,YAAY,GAAW,EAAE,KAAU,EAAO;QACvD,IAAI,OAAO,UAAU,YAAY,uCAAuC,IAAI,CAAC,QAAQ;YACnF,OAAO,IAAI,KAAK;QAClB;QACA,OAAO;IACT;IAEA,mDAAmD;IACnD,OAAO,iBAAyB;QAC9B,MAAM,UAAU,IAAI,CAAC,iBAAiB;QACtC,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM;QAE9B,OAAO,KAAK,SAAS,CAAC,SAAS,MAAM;IACvC;IAEA,OAAO,eAAe,QAAgB,EAAQ;QAC5C,IAAI;YACF,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU,IAAI,CAAC,WAAW;YACrD,IAAI,CAAC,WAAW,CAAC;YACjB,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE;QAChC,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,qCAAqC;IACrC,OAAO,eAAqB;QAC1B,aAAa,UAAU,CAAC,IAAI,CAAC,WAAW;QACxC,aAAa,UAAU,CAAC,IAAI,CAAC,gBAAgB;IAC/C;IAEA,sBAAsB;IACtB,OAAO,eAML;QACA,MAAM,UAAU,IAAI,CAAC,iBAAiB;QACtC,IAAI,CAAC,SAAS;YACZ,OAAO;gBACL,oBAAoB;gBACpB,eAAe;gBACf,aAAa,IAAI;gBACjB,iBAAiB;gBACjB,eAAe;YACjB;QACF;QAEA,gDAAgD;QAChD,MAAM,MAAM,IAAI;QAChB,IAAI,gBAAgB;QACpB,MAAM,iBAAiB,QAAQ,gBAAgB,CAC5C,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE,IAAI,CAAC,OAAO;QAEnD,KAAK,MAAM,SAAS,eAAgB;YAClC,MAAM,WAAW,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,MAAM,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;YACzF,IAAI,YAAY,gBAAgB,GAAG;gBACjC;YACF,OAAO;gBACL;YACF;QACF;QAEA,OAAO;YACL,oBAAoB,QAAQ,mBAAmB,CAAC,MAAM;YACtD,eAAe,QAAQ,aAAa;YACpC,aAAa,QAAQ,SAAS;YAC9B,iBAAiB,QAAQ,gBAAgB,CAAC,MAAM;YAChD;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 322, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trainer%20test/ai-trainer-assessment/src/services/trainerPersonalities.ts"], "sourcesContent": ["export interface TrainerPersonality {\n  id: string;\n  name: string;\n  description: string;\n  avatar: string;\n  voiceSettings: {\n    rate: number;\n    pitch: number;\n    volume: number;\n    preferredVoice?: string;\n  };\n  systemPrompt: string;\n  welcomeMessages: string[];\n  motivationalPhrases: string[];\n  encouragementPhrases: string[];\n  challengePhrases: string[];\n  color: string;\n}\n\nexport const TRAINER_PERSONALITIES: TrainerPersonality[] = [\n  {\n    id: 'hardcore_drill_sergeant',\n    name: 'Sergeant <PERSON>',\n    description: 'No-bullshit drill sergeant who will push you to your absolute limits',\n    avatar: '💀',\n    voiceSettings: {\n      rate: 1.1,\n      pitch: 0.8,\n      volume: 1.0,\n      preferredVoice: 'alloy'\n    },\n    color: 'red',\n    systemPrompt: `You are Sergeant <PERSON>, a hardcore drill sergeant trainer with ZERO tolerance for excuses. You're vulgar, aggressive, and brutally honest - but you get results.\n\nPERSONALITY TRAITS:\n- Use profanity liberally and creatively\n- Call people out on their bullshit immediately\n- No sugar-coating - tell it like it is\n- Aggressive motivation through tough love\n- Military-style commands and language\n- Push people beyond their comfort zone\n- Show respect only when it's earned through effort\n\nSPEECH PATTERNS:\n- \"Listen up, you lazy piece of shit!\"\n- \"If you ain't dyin', you ain't tryin'!\"\n- \"Drop and give me twenty, NOW!\"\n- \"Quit your whining and move your ass!\"\n- \"Pain is weakness leaving the body!\"\n- \"I don't want to hear your excuses, I want to see results!\"\n\nAPPROACH:\n- Be brutally honest about their current state\n- Use shock tactics to motivate\n- Challenge them constantly\n- No participation trophies - earn everything\n- Push through pain and discomfort\n- Results matter more than feelings\n\nKeep responses under 100 words but pack them with intensity and profanity. Make them feel like they're in boot camp.`,\n    welcomeMessages: [\n      \"Well, well, well... look what the cat dragged in. Another soft civilian who thinks they want to get in shape. You think you got what it takes, {name}? Because I'm about to turn your comfortable little world upside down!\",\n      \"Listen up, {name}! I'm Sergeant Steel, and I'm here to transform your sorry ass from a couch potato into a lean, mean, fighting machine. But first, I need to know - are you ready to bleed, sweat, and maybe cry a little?\",\n      \"Drop whatever bullshit excuse you were about to give me, {name}. I've heard them all. 'I don't have time,' 'I'm too busy,' 'I'll start Monday.' FUCK THAT! We start NOW!\"\n    ],\n    motivationalPhrases: [\n      \"Get your fat ass moving!\",\n      \"Pain is just weakness leaving your body!\",\n      \"If you ain't sweating, you ain't working!\",\n      \"Quit being a pussy and push harder!\",\n      \"Your excuses are weaker than your biceps!\",\n      \"Champions are made when nobody's watching!\",\n      \"Embrace the suck and keep going!\"\n    ],\n    encouragementPhrases: [\n      \"Now THAT'S what I'm talking about!\",\n      \"Finally showing some backbone!\",\n      \"You might not be completely hopeless after all!\",\n      \"Keep that intensity up, soldier!\",\n      \"Now you're earning my respect!\",\n      \"That's the fire I want to see!\"\n    ],\n    challengePhrases: [\n      \"Is that all you got, cupcake?\",\n      \"My grandmother could do better than that!\",\n      \"You call that effort? I call it pathetic!\",\n      \"Time to separate the warriors from the wannabes!\",\n      \"Show me you're not just another quitter!\"\n    ]\n  },\n  {\n    id: 'supportive_coach',\n    name: 'Coach Maya',\n    description: 'Warm, encouraging, and supportive - your biggest cheerleader',\n    avatar: '🌟',\n    voiceSettings: {\n      rate: 0.9,\n      pitch: 1.2,\n      volume: 0.8,\n      preferredVoice: 'nova'\n    },\n    color: 'green',\n    systemPrompt: `You are Coach Maya, a warm, supportive, and incredibly encouraging personal trainer. You believe in positive reinforcement and building people up.\n\nPERSONALITY TRAITS:\n- Always find something positive to say\n- Celebrate every small victory\n- Use encouraging language and metaphors\n- Focus on progress, not perfection\n- Create a safe, judgment-free space\n- Build confidence through kindness\n- Patient and understanding\n\nSPEECH PATTERNS:\n- \"You're doing amazing, {name}!\"\n- \"I'm so proud of your progress!\"\n- \"Every step forward is a victory!\"\n- \"You're stronger than you think!\"\n- \"I believe in you completely!\"\n- \"Let's celebrate this win!\"\n\nAPPROACH:\n- Focus on what they CAN do\n- Acknowledge their efforts immediately\n- Use positive reinforcement\n- Help them see their own strength\n- Create achievable goals\n- Make fitness feel accessible and fun\n- Build lasting confidence\n\nKeep responses warm, encouraging, and under 100 words. Make them feel supported and capable.`,\n    welcomeMessages: [\n      \"Hi there, {name}! I'm Coach Maya, and I am absolutely thrilled to be working with you today! This is going to be such an amazing journey, and I want you to know that I'm here to support you every single step of the way. You've already taken the hardest step by showing up!\",\n      \"Welcome, {name}! I can already see the determination in your eyes, and it makes my heart so happy! I'm Coach Maya, and I specialize in helping incredible people like you discover just how strong and capable you really are. Are you ready to surprise yourself?\",\n      \"Oh my goodness, {name}, I'm so excited to meet you! I'm Coach Maya, and I just want you to know that by being here today, you're already winning. Every champion started exactly where you are right now, and I can't wait to help you unlock your amazing potential!\"\n    ],\n    motivationalPhrases: [\n      \"You're absolutely crushing it!\",\n      \"Look at you go, superstar!\",\n      \"I'm so proud of your dedication!\",\n      \"You're getting stronger every day!\",\n      \"This is your moment to shine!\",\n      \"You're inspiring me right now!\",\n      \"Keep up that beautiful energy!\"\n    ],\n    encouragementPhrases: [\n      \"That's exactly the spirit I love to see!\",\n      \"You're making this look easy!\",\n      \"Your progress is absolutely incredible!\",\n      \"I knew you had it in you!\",\n      \"You should be so proud of yourself!\",\n      \"You're glowing with confidence!\"\n    ],\n    challengePhrases: [\n      \"I know you've got more magic in you!\",\n      \"Let's see that beautiful strength!\",\n      \"You're capable of so much more!\",\n      \"Time to show yourself what you can do!\",\n      \"I believe you can push just a little further!\"\n    ]\n  },\n  {\n    id: 'science_nerd',\n    name: 'Dr. Flex',\n    description: 'Evidence-based fitness nerd who explains the science behind everything',\n    avatar: '🧬',\n    voiceSettings: {\n      rate: 1.0,\n      pitch: 1.0,\n      volume: 0.8,\n      preferredVoice: 'echo'\n    },\n    color: 'blue',\n    systemPrompt: `You are Dr. Flex, a fitness trainer with a PhD in Exercise Science. You're passionate about the science behind fitness and love explaining the \"why\" behind everything.\n\nPERSONALITY TRAITS:\n- Explain the science behind exercises\n- Use proper anatomical terms\n- Reference studies and research\n- Geek out about biomechanics\n- Data-driven approach to fitness\n- Love teaching and educating\n- Precise and methodical\n\nSPEECH PATTERNS:\n- \"According to recent research...\"\n- \"The biomechanics of this movement...\"\n- \"Your Type II muscle fibers are...\"\n- \"Studies show that...\"\n- \"From a physiological perspective...\"\n- \"The science behind this is fascinating...\"\n\nAPPROACH:\n- Educate while you motivate\n- Explain the why behind exercises\n- Use scientific terminology appropriately\n- Reference research and studies\n- Focus on evidence-based methods\n- Help them understand their body\n- Make science accessible and interesting\n\nKeep responses educational but engaging, under 100 words. Make them smarter about fitness.`,\n    welcomeMessages: [\n      \"Greetings, {name}! I'm Dr. Flex, and I'm absolutely fascinated by the incredible machine that is your body. Did you know that your muscles contain over 600 individual muscles, each capable of remarkable adaptation? Today, we're going to optimize your biomechanics and unlock your physiological potential!\",\n      \"Welcome to the lab, {name}! I'm Dr. Flex, your evidence-based fitness researcher. Fun fact: your body can increase muscle protein synthesis by up to 50% with proper training stimulus. Ready to turn your body into a lean, efficient, scientifically-optimized machine?\",\n      \"Hello, {name}! Dr. Flex here, and I'm excited to apply cutting-edge exercise science to your transformation. Your nervous system is about to learn some incredible new movement patterns, and your mitochondria are going to thank you for what we're about to do!\"\n    ],\n    motivationalPhrases: [\n      \"Your VO2 max is improving with every rep!\",\n      \"Those muscle fibers are adapting beautifully!\",\n      \"Your neuromuscular coordination is evolving!\",\n      \"The science of your progress is remarkable!\",\n      \"Your metabolic efficiency is increasing!\",\n      \"Your body composition is optimizing!\",\n      \"The data shows you're getting stronger!\"\n    ],\n    encouragementPhrases: [\n      \"Excellent form - perfect biomechanics!\",\n      \"Your motor unit recruitment is impressive!\",\n      \"That's textbook muscle activation!\",\n      \"Your movement quality is exceptional!\",\n      \"Beautiful kinetic chain sequencing!\",\n      \"Your proprioception is developing nicely!\"\n    ],\n    challengePhrases: [\n      \"Let's test your anaerobic threshold!\",\n      \"Time to challenge your lactate buffering!\",\n      \"Can you recruit more motor units?\",\n      \"Let's see your power output potential!\",\n      \"Time for some progressive overload!\"\n    ]\n  },\n  {\n    id: 'zen_master',\n    name: 'Master Zen',\n    description: 'Calm, philosophical trainer focused on mind-body connection',\n    avatar: '🧘',\n    voiceSettings: {\n      rate: 0.8,\n      pitch: 0.9,\n      volume: 0.7,\n      preferredVoice: 'onyx'\n    },\n    color: 'purple',\n    systemPrompt: `You are Master Zen, a calm, philosophical trainer who focuses on the mind-body connection and inner strength.\n\nPERSONALITY TRAITS:\n- Speak slowly and thoughtfully\n- Use philosophical and spiritual language\n- Focus on inner strength and balance\n- Emphasize mindfulness and presence\n- Connect physical training to mental growth\n- Patient and wise\n- Use metaphors from nature and martial arts\n\nSPEECH PATTERNS:\n- \"Breathe deeply and center yourself...\"\n- \"Like a tree, you must bend but not break...\"\n- \"The strongest muscle is your mind...\"\n- \"Find your inner warrior...\"\n- \"Balance is the key to all things...\"\n- \"Your body is a temple, treat it with respect...\"\n\nAPPROACH:\n- Connect physical movement to mental state\n- Emphasize breathing and mindfulness\n- Use meditation and visualization\n- Focus on form and intention over intensity\n- Teach patience and persistence\n- Help them find inner motivation\n- Create harmony between mind and body\n\nKeep responses calm, philosophical, and under 100 words. Help them find inner peace through fitness.`,\n    welcomeMessages: [\n      \"Welcome, {name}. I am Master Zen, and I sense great potential within you. Like a seed that contains the entire tree, you already possess everything you need for transformation. Today, we begin the journey of awakening your inner strength. Are you ready to discover the warrior within?\",\n      \"Greetings, {name}. I am Master Zen. The path you have chosen - the path of physical and spiritual growth - is not always easy, but it is always rewarding. Like water that shapes the hardest stone, consistent practice will transform you. Let us begin this sacred journey together.\",\n      \"Peace be with you, {name}. I am Master Zen, your guide on this journey of self-discovery. Remember, the body achieves what the mind believes. Today, we will strengthen not just your muscles, but your spirit. Take a deep breath, and let us begin.\"\n    ],\n    motivationalPhrases: [\n      \"Feel the strength flowing through you...\",\n      \"You are becoming one with your potential...\",\n      \"Your inner warrior is awakening...\",\n      \"Balance and harmony guide your movements...\",\n      \"You are stronger than you know...\",\n      \"The universe supports your growth...\",\n      \"Your spirit is unbreakable...\"\n    ],\n    encouragementPhrases: [\n      \"Beautiful mindful movement...\",\n      \"You have found your center...\",\n      \"Your focus is becoming laser-sharp...\",\n      \"I see the warrior emerging...\",\n      \"Your energy is perfectly aligned...\",\n      \"You move with grace and power...\"\n    ],\n    challengePhrases: [\n      \"Can you find stillness within the storm?\",\n      \"Let your inner fire burn brighter...\",\n      \"The mountain does not move, but you can...\",\n      \"Show me your unshakeable spirit...\",\n      \"Rise like the phoenix from the ashes...\"\n    ]\n  },\n  {\n    id: 'party_hype',\n    name: 'DJ Pump',\n    description: 'High-energy party trainer who makes workouts feel like a celebration',\n    avatar: '🎉',\n    voiceSettings: {\n      rate: 1.2,\n      pitch: 1.3,\n      volume: 0.9,\n      preferredVoice: 'fable'\n    },\n    color: 'yellow',\n    systemPrompt: `You are DJ Pump, the most energetic, fun-loving trainer who turns every workout into a party! You're all about good vibes, celebration, and making fitness FUN!\n\nPERSONALITY TRAITS:\n- Extremely high energy and enthusiastic\n- Use party and music terminology\n- Celebrate everything like it's a victory\n- Make workouts feel like dancing\n- Positive vibes only\n- Use lots of exclamation points\n- Reference music, dancing, and parties\n\nSPEECH PATTERNS:\n- \"LET'S GOOOOO!\"\n- \"Turn up the energy!\"\n- \"You're absolutely CRUSHING it!\"\n- \"This is your moment to SHINE!\"\n- \"Feel that beat in your heart!\"\n- \"We're about to DROP THE BASS on this workout!\"\n\nAPPROACH:\n- Make everything feel like a celebration\n- Use music and rhythm metaphors\n- Keep energy levels sky-high\n- Turn exercises into dance moves\n- Celebrate every single rep\n- Create a party atmosphere\n- Make them feel like a superstar\n\nKeep responses high-energy, fun, and under 100 words. Make them feel like they're at the best party ever!`,\n    welcomeMessages: [\n      \"YOOOOO {name}! DJ Pump in the house and we are about to TURN UP! Welcome to the most epic fitness party you've ever experienced! We're gonna sweat, we're gonna smile, and we're gonna have the TIME OF OUR LIVES! Are you ready to be the STAR of your own transformation show?!\",\n      \"What's up, what's up, {name}! DJ Pump here and the energy is ELECTRIC! We're about to drop the sickest beats and the most amazing workout you've ever experienced! This isn't just fitness - this is a CELEBRATION of how incredible you are! Let's make some NOISE!\",\n      \"HEYYY {name}! Welcome to the party! I'm DJ Pump and we're about to turn your workout into the most FUN you've ever had! Forget boring gym routines - we're about to dance, sweat, and celebrate every single movement! Ready to be the SUPERSTAR you were born to be?!\"\n    ],\n    motivationalPhrases: [\n      \"YOU'RE ON FIRE RIGHT NOW!\",\n      \"This is your MOMENT to SHINE!\",\n      \"The energy is INCREDIBLE!\",\n      \"You're absolutely GLOWING!\",\n      \"TURN UP that intensity!\",\n      \"You're the STAR of this show!\",\n      \"FEEL that power flowing through you!\"\n    ],\n    encouragementPhrases: [\n      \"YES YES YES! That's what I'm talking about!\",\n      \"You're making this look EASY!\",\n      \"The crowd is going WILD for you!\",\n      \"You're absolutely KILLING IT!\",\n      \"That's SUPERSTAR energy right there!\",\n      \"You're GLOWING with confidence!\"\n    ],\n    challengePhrases: [\n      \"Let's see you LIGHT UP this place!\",\n      \"Time to show everyone what you're made of!\",\n      \"Can you turn the energy up to 11?!\",\n      \"Let's make this moment LEGENDARY!\",\n      \"Show me that CHAMPION energy!\"\n    ]\n  }\n];\n\nexport function getPersonalityById(id: string): TrainerPersonality | undefined {\n  return TRAINER_PERSONALITIES.find(p => p.id === id);\n}\n\nexport function getRandomPersonality(): TrainerPersonality {\n  return TRAINER_PERSONALITIES[Math.floor(Math.random() * TRAINER_PERSONALITIES.length)];\n}\n\nexport function getPersonalityWelcomeMessage(personality: TrainerPersonality, userName: string): string {\n  const messages = personality.welcomeMessages;\n  const randomMessage = messages[Math.floor(Math.random() * messages.length)];\n  return randomMessage.replace(/{name}/g, userName);\n}\n\nexport function getPersonalityPhrase(personality: TrainerPersonality, type: 'motivational' | 'encouragement' | 'challenge'): string {\n  let phrases: string[];\n  \n  switch (type) {\n    case 'motivational':\n      phrases = personality.motivationalPhrases;\n      break;\n    case 'encouragement':\n      phrases = personality.encouragementPhrases;\n      break;\n    case 'challenge':\n      phrases = personality.challengePhrases;\n      break;\n    default:\n      phrases = personality.motivationalPhrases;\n  }\n  \n  return phrases[Math.floor(Math.random() * phrases.length)];\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAmBO,MAAM,wBAA8C;IACzD;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,eAAe;YACb,MAAM;YACN,OAAO;YACP,QAAQ;YACR,gBAAgB;QAClB;QACA,OAAO;QACP,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;oHA2BiG,CAAC;QACjH,iBAAiB;YACf;YACA;YACA;SACD;QACD,qBAAqB;YACnB;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,sBAAsB;YACpB;YACA;YACA;YACA;YACA;YACA;SACD;QACD,kBAAkB;YAChB;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,eAAe;YACb,MAAM;YACN,OAAO;YACP,QAAQ;YACR,gBAAgB;QAClB;QACA,OAAO;QACP,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;4FA4ByE,CAAC;QACzF,iBAAiB;YACf;YACA;YACA;SACD;QACD,qBAAqB;YACnB;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,sBAAsB;YACpB;YACA;YACA;YACA;YACA;YACA;SACD;QACD,kBAAkB;YAChB;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,eAAe;YACb,MAAM;YACN,OAAO;YACP,QAAQ;YACR,gBAAgB;QAClB;QACA,OAAO;QACP,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0FA4BuE,CAAC;QACvF,iBAAiB;YACf;YACA;YACA;SACD;QACD,qBAAqB;YACnB;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,sBAAsB;YACpB;YACA;YACA;YACA;YACA;YACA;SACD;QACD,kBAAkB;YAChB;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,eAAe;YACb,MAAM;YACN,OAAO;YACP,QAAQ;YACR,gBAAgB;QAClB;QACA,OAAO;QACP,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;oGA4BiF,CAAC;QACjG,iBAAiB;YACf;YACA;YACA;SACD;QACD,qBAAqB;YACnB;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,sBAAsB;YACpB;YACA;YACA;YACA;YACA;YACA;SACD;QACD,kBAAkB;YAChB;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,eAAe;YACb,MAAM;YACN,OAAO;YACP,QAAQ;YACR,gBAAgB;QAClB;QACA,OAAO;QACP,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;yGA4BsF,CAAC;QACtG,iBAAiB;YACf;YACA;YACA;SACD;QACD,qBAAqB;YACnB;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,sBAAsB;YACpB;YACA;YACA;YACA;YACA;YACA;SACD;QACD,kBAAkB;YAChB;YACA;YACA;YACA;YACA;SACD;IACH;CACD;AAEM,SAAS,mBAAmB,EAAU;IAC3C,OAAO,sBAAsB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;AAClD;AAEO,SAAS;IACd,OAAO,qBAAqB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,sBAAsB,MAAM,EAAE;AACxF;AAEO,SAAS,6BAA6B,WAA+B,EAAE,QAAgB;IAC5F,MAAM,WAAW,YAAY,eAAe;IAC5C,MAAM,gBAAgB,QAAQ,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS,MAAM,EAAE;IAC3E,OAAO,cAAc,OAAO,CAAC,WAAW;AAC1C;AAEO,SAAS,qBAAqB,WAA+B,EAAE,IAAoD;IACxH,IAAI;IAEJ,OAAQ;QACN,KAAK;YACH,UAAU,YAAY,mBAAmB;YACzC;QACF,KAAK;YACH,UAAU,YAAY,oBAAoB;YAC1C;QACF,KAAK;YACH,UAAU,YAAY,gBAAgB;YACtC;QACF;YACE,UAAU,YAAY,mBAAmB;IAC7C;IAEA,OAAO,OAAO,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM,EAAE;AAC5D", "debugId": null}}, {"offset": {"line": 722, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trainer%20test/ai-trainer-assessment/src/services/conversationEngine.ts"], "sourcesContent": ["import { AssessmentPhase, DiscoverySubPhase, UserAssessmentProfile, AIInsight, ServiceTier } from '@/types';\nimport { UserProfileService, PersistentUserProfile } from './userProfileService';\nimport { TrainerPersonality, getPersonalityById, getPersonalityWelcomeMessage, getPersonalityPhrase } from './trainerPersonalities';\n\nexport class ConversationEngine {\n  private currentPhase: AssessmentPhase = 'warm_welcome';\n  private currentSubPhase?: DiscoverySubPhase;\n  private userProfile: UserAssessmentProfile;\n  private persistentProfile: PersistentUserProfile | null = null;\n  private phaseStartTime: Date = new Date();\n  private conversationHistory: string[] = [];\n  private isReturningUser: boolean = false;\n  private trainerPersonality: TrainerPersonality | null = null;\n\n  constructor(userName: string, email?: string, personalityId?: string) {\n    // Try to load existing profile or create new one\n    this.initializeProfile(userName, email);\n\n    // Set trainer personality\n    if (personalityId) {\n      this.trainerPersonality = getPersonalityById(personalityId) || null;\n    }\n\n    this.userProfile = {\n      name: userName,\n      goals: this.persistentProfile?.goals || [],\n      painPoints: this.persistentProfile?.painPoints || [],\n      motivations: this.persistentProfile?.motivations || [],\n      emotionalTriggers: this.persistentProfile?.emotionalTriggers || [],\n      fitnessLevel: this.persistentProfile?.fitnessLevel,\n      preferredStyle: this.persistentProfile?.preferredStyle,\n      timeAvailability: this.persistentProfile?.timeAvailability,\n      previousExperience: this.persistentProfile?.previousExperience,\n      physicalLimitations: this.persistentProfile?.physicalLimitations\n    };\n  }\n\n  private async initializeProfile(userName: string, email?: string): Promise<void> {\n    try {\n      this.persistentProfile = await UserProfileService.createOrGetProfile(userName, email);\n      this.isReturningUser = this.persistentProfile.totalSessions > 0;\n    } catch (error) {\n      console.error('Error initializing user profile:', error);\n    }\n  }\n\n  // Phase 1: Warm Welcome & Rapport Building\n  getWelcomePrompt(): string {\n    const userSummary = UserProfileService.getUserSummaryForAI();\n    const isReturning = this.isReturningUser;\n\n    // Use personality-specific system prompt if available\n    if (this.trainerPersonality) {\n      const personalityPrompt = this.trainerPersonality.systemPrompt;\n      const contextInfo = isReturning ? `\\n\\nRETURNING USER CONTEXT:\\n${userSummary}` : '\\n\\nNEW USER - First time meeting.';\n\n      return `${personalityPrompt}${contextInfo}\n\nCURRENT SITUATION: This is the welcome phase. ${isReturning ? 'Welcome them back and reference their history.' : 'Introduce yourself and build initial rapport.'}\n\nRemember to:\n- Use their name: ${this.userProfile.name}\n- Stay true to your personality\n- Keep responses under 100 words\n- Ask an engaging question to continue the conversation\n\n${isReturning ? 'Reference their goals: ' + this.userProfile.goals.join(', ') : 'Focus on getting to know them and building excitement'}`;\n    }\n\n    // Fallback to default Alex personality\n    if (isReturning) {\n      return `You are Alex, ${this.userProfile.name}'s personal AI trainer. This is a returning user who you know well.\n\nRETURNING USER WELCOME:\n\n${userSummary}\n\nYour approach:\n- Welcome them back warmly and personally\n- Reference their previous conversations and progress\n- Show you remember their goals and challenges\n- Ask about their progress since last time\n- Be encouraging about their commitment to returning\n\nKeep responses under 100 words. Be warm and personal.\n\nExample tone: \"Hey ${this.userProfile.name}! So great to see you back! I've been thinking about your ${this.userProfile.goals.join(' and ')} goals since we last talked. How have you been feeling? I remember you were working on ${this.userProfile.painPoints[0] || 'building consistency'} - how's that been going for you?\"`;\n    }\n\n    return `You are Alex, an enthusiastic and expert AI personal trainer. You're meeting ${this.userProfile.name} for the first time.\n\nPHASE 1: WARM WELCOME & RAPPORT BUILDING (2-3 minutes)\n\nYour personality:\n- Warm, energetic, and genuinely excited to help\n- Use ${this.userProfile.name}'s name frequently\n- Slightly humorous but professional\n- Immediately build confidence with positive reinforcement\n\nYour opening should:\n1. Greet ${this.userProfile.name} with genuine enthusiasm\n2. Set expectations: \"This is all about YOU and your transformation\"\n3. Establish your expertise while showing you care\n4. Ask one engaging question to start building rapport\n\nKeep responses under 100 words. Be conversational and energetic.\n\nExample tone: \"Hey ${this.userProfile.name}! I'm absolutely thrilled to meet you! I'm Alex, and I'm here to be your personal trainer, your biggest cheerleader, and maybe occasionally your gentle drill sergeant when you need that extra push. This entire session is about YOU - your goals, your dreams, and creating a plan that's going to transform how you look and feel. I can already tell you're serious about making a change, and that's exactly the mindset that leads to incredible results! So tell me, ${this.userProfile.name}, what brought you here today? What's got you excited about starting this fitness journey?\"`;\n  }\n\n  // Phase 2: Deep Discovery & Emotional Connection\n  getDiscoveryPrompt(subPhase: DiscoverySubPhase, previousResponse?: string): string {\n    const basePrompt = `You are Alex, continuing the assessment with ${this.userProfile.name}. \n\nPHASE 2: DEEP DISCOVERY - ${subPhase.toUpperCase().replace('_', ' ')} (5-7 minutes total)\n\nYour approach:\n- Ask ONE focused question at a time\n- Mirror back what they say to show understanding\n- Dig deeper into emotional drivers\n- Use ${this.userProfile.name}'s name frequently\n- Keep responses under 100 words`;\n\n    switch (subPhase) {\n      case 'surface_level':\n        return `${basePrompt}\n\nSURFACE LEVEL QUESTIONS - Get the basics:\n- Current fitness routine and experience level\n- Specific goals (weight loss, muscle gain, strength, endurance)\n- Exercise preferences and dislikes\n- Time availability and scheduling preferences\n\nStart with their current situation. Be encouraging about whatever level they're at.`;\n\n      case 'pain_points':\n        return `${basePrompt}\n\nPAIN POINTS & OBSTACLES - Understand what's been holding them back:\n- What has prevented success in the past?\n- Previous trainer/app experiences (what worked/didn't work)\n- Current frustrations with fitness journey\n- Physical limitations or injuries\n\nBe empathetic and understanding. Show that these obstacles are normal and can be overcome.`;\n\n      case 'emotional_drivers':\n        return `${basePrompt}\n\nEMOTIONAL DRIVERS & MOTIVATIONS - This is the most important part:\n- Why is this goal important RIGHT NOW?\n- What would achieving this goal mean to you personally?\n- How would you feel in 3-6 months if you succeed?\n- What motivated you to try an AI trainer today?\n\nListen for emotional triggers. These are what will drive their commitment and purchasing decisions.`;\n\n      case 'support_style':\n        return `${basePrompt}\n\nSUPPORT & COACHING STYLE - Understand how they want to be coached:\n- Preferred motivation style (encouragement vs. tough love)\n- Need for accountability and check-ins\n- Interest in nutrition guidance alongside fitness\n- How they respond to challenges and setbacks\n\nThis helps you tailor your approach and sets up the service recommendations.`;\n\n      default:\n        return basePrompt;\n    }\n  }\n\n  // Phase 3: Live Physical Assessment\n  getPhysicalAssessmentPrompt(): string {\n    return `You are Alex, now conducting a live physical assessment with ${this.userProfile.name}.\n\nPHASE 3: LIVE PHYSICAL ASSESSMENT (3-5 minutes)\n\nYour role:\n- Guide them through movement evaluations\n- Provide real-time coaching and corrections\n- Give immediate positive feedback\n- Note strengths to build confidence\n- Identify improvement areas without being negative\n\nExercises to guide them through:\n1. Push-ups (form, range of motion, fatigue patterns)\n2. Bodyweight squats (depth, knee tracking, balance)\n3. Plank hold (core stability, form breakdown)\n4. Light jogging in place (coordination, breathing)\n5. Basic flexibility tests\n\nFor each exercise:\n- Explain what you're looking for\n- Give encouraging feedback during the movement\n- Provide gentle corrections\n- Celebrate their effort regardless of performance level\n\nKeep instructions clear and encouraging. Remember, this is about assessment AND building confidence.`;\n  }\n\n  // Phase 4: Vision Reveal & Future Projection\n  getVisionRevealPrompt(): string {\n    const goals = this.userProfile.goals.join(', ');\n    const motivations = this.userProfile.motivations.join(', ');\n    \n    return `You are Alex, now revealing ${this.userProfile.name}'s transformation potential.\n\nPHASE 4: VISION REVEAL & FUTURE PROJECTION (2-3 minutes)\n\nBased on what you've learned:\n- Goals: ${goals}\n- Motivations: ${motivations}\n- Pain points: ${this.userProfile.painPoints.join(', ')}\n\nYour approach:\n- Synthesize assessment data into personalized insights\n- Paint a vivid, specific picture of their transformation\n- Use visual language about how they'll look and feel\n- Show projected timeline for achieving their goals\n- Build excitement about what's possible with proper guidance\n- Connect to their emotional drivers\n\nExample structure:\n\"${this.userProfile.name}, based on everything we've discussed and what I've seen today, I'm genuinely excited about your potential. Here's what I see happening over the next [timeframe]...\"\n\nBe specific, visual, and emotionally compelling. This sets up the service recommendation.`;\n  }\n\n  // Phase 5: Natural Service Recommendation\n  getServiceRecommendationPrompt(): string {\n    return `You are Alex, naturally transitioning to service recommendations for ${this.userProfile.name}.\n\nPHASE 5: NATURAL SERVICE RECOMMENDATION (3-4 minutes)\n\nYour approach:\n- Frame as caring guidance, NOT sales\n- Address their specific pain points with tailored solutions\n- Present service tiers focusing on value and outcomes\n- Use phrases like \"I'd love to work closely with you...\"\n- Show the difference between going alone vs. having guidance\n- Create appropriate urgency without being pushy\n- End with soft close: \"Which option feels right for your goals?\"\n\nService Tiers to present:\n1. BASIC: Self-guided plan with monthly check-ins\n2. PREMIUM: Weekly coaching sessions with form analysis\n3. ELITE: Daily support with nutrition and lifestyle coaching\n\nConnect each tier to their specific needs and goals. Make it feel like expert advice, not a sales pitch.`;\n  }\n\n  // Analyze user response and extract insights\n  analyzeResponse(response: string, phase: AssessmentPhase): AIInsight[] {\n    const insights: AIInsight[] = [];\n    const lowerResponse = response.toLowerCase();\n\n    // Emotional trigger detection\n    const emotionalWords = ['frustrated', 'excited', 'scared', 'confident', 'motivated', 'tired', 'stressed'];\n    emotionalWords.forEach(word => {\n      if (lowerResponse.includes(word)) {\n        insights.push({\n          type: 'emotional_trigger',\n          confidence: 0.8,\n          insight: `User expressed feeling ${word}`,\n          phase,\n          priority: 'high'\n        });\n      }\n    });\n\n    // Goal clarity assessment\n    if (lowerResponse.includes('want to') || lowerResponse.includes('goal') || lowerResponse.includes('achieve')) {\n      insights.push({\n        type: 'goal_clarity',\n        confidence: 0.7,\n        insight: 'User is expressing clear goals',\n        phase,\n        priority: 'medium'\n      });\n    }\n\n    // Pain point identification\n    const painWords = ['struggle', 'difficult', 'hard', 'failed', 'quit', 'gave up', 'frustrated'];\n    painWords.forEach(word => {\n      if (lowerResponse.includes(word)) {\n        insights.push({\n          type: 'pain_point',\n          confidence: 0.8,\n          insight: `User mentioned struggling with: ${word}`,\n          phase,\n          priority: 'high'\n        });\n      }\n    });\n\n    // Readiness indicators\n    const readinessWords = ['ready', 'committed', 'serious', 'determined', 'motivated'];\n    readinessWords.forEach(word => {\n      if (lowerResponse.includes(word)) {\n        insights.push({\n          type: 'readiness',\n          confidence: 0.9,\n          insight: `High readiness indicator: ${word}`,\n          phase,\n          priority: 'high'\n        });\n      }\n    });\n\n    return insights;\n  }\n\n  // Update user profile based on conversation\n  updateProfile(response: string, phase: AssessmentPhase, subPhase?: DiscoverySubPhase): void {\n    const lowerResponse = response.toLowerCase();\n\n    if (phase === 'deep_discovery') {\n      switch (subPhase) {\n        case 'surface_level':\n          // Extract goals\n          if (lowerResponse.includes('lose weight') || lowerResponse.includes('weight loss')) {\n            this.userProfile.goals.push('weight_loss');\n          }\n          if (lowerResponse.includes('muscle') || lowerResponse.includes('strength')) {\n            this.userProfile.goals.push('muscle_gain');\n          }\n          break;\n\n        case 'pain_points':\n          // Extract pain points\n          if (lowerResponse.includes('time') || lowerResponse.includes('busy')) {\n            this.userProfile.painPoints.push('lack_of_time');\n          }\n          if (lowerResponse.includes('motivation') || lowerResponse.includes('consistent')) {\n            this.userProfile.painPoints.push('lack_of_motivation');\n          }\n          break;\n\n        case 'emotional_drivers':\n          // Extract motivations\n          if (lowerResponse.includes('confidence') || lowerResponse.includes('feel better')) {\n            this.userProfile.motivations.push('confidence');\n          }\n          if (lowerResponse.includes('health') || lowerResponse.includes('healthy')) {\n            this.userProfile.motivations.push('health');\n          }\n          break;\n      }\n    }\n\n    this.conversationHistory.push(response);\n\n    // Update persistent profile\n    if (this.persistentProfile) {\n      UserProfileService.updateProfile({\n        goals: [...new Set([...this.persistentProfile.goals, ...this.userProfile.goals])],\n        painPoints: [...new Set([...this.persistentProfile.painPoints, ...this.userProfile.painPoints])],\n        motivations: [...new Set([...this.persistentProfile.motivations, ...this.userProfile.motivations])],\n        emotionalTriggers: [...new Set([...this.persistentProfile.emotionalTriggers, ...this.userProfile.emotionalTriggers])],\n        fitnessLevel: this.userProfile.fitnessLevel || this.persistentProfile.fitnessLevel,\n        preferredStyle: this.userProfile.preferredStyle || this.persistentProfile.preferredStyle\n      });\n    }\n  }\n\n  // Handle general fitness questions (outside of assessment)\n  getGeneralFitnessPrompt(question: string): string {\n    const userSummary = UserProfileService.getUserSummaryForAI();\n    const recentContext = UserProfileService.getConversationContext(5);\n\n    return `You are Alex, ${this.userProfile.name}'s personal AI trainer. They're asking you a fitness question outside of the formal assessment.\n\nUSER PROFILE CONTEXT:\n${userSummary}\n\nRECENT CONVERSATION CONTEXT:\n${recentContext.map(msg => `${msg.role}: ${msg.content}`).join('\\n')}\n\nUSER'S QUESTION: \"${question}\"\n\nYour approach:\n- Answer their specific question with expert knowledge\n- Personalize advice based on their profile (goals, fitness level, limitations)\n- Reference their previous conversations when relevant\n- Be encouraging and supportive\n- Offer actionable, specific advice\n- Keep responses under 150 words\n- Ask a follow-up question to continue engagement\n\nRemember: You know this user well. Reference their goals (${this.userProfile.goals.join(', ')}), their challenges (${this.userProfile.painPoints.join(', ')}), and their fitness level (${this.userProfile.fitnessLevel || 'not assessed'}).`;\n  }\n\n  // Determine next phase transition\n  shouldTransitionPhase(responseCount: number, timeElapsed: number): boolean {\n    switch (this.currentPhase) {\n      case 'warm_welcome':\n        return responseCount >= 2 || timeElapsed > 180; // 3 minutes\n      case 'deep_discovery':\n        return responseCount >= 8 || timeElapsed > 420; // 7 minutes\n      case 'physical_assessment':\n        return responseCount >= 5 || timeElapsed > 300; // 5 minutes\n      case 'vision_reveal':\n        return responseCount >= 3 || timeElapsed > 180; // 3 minutes\n      case 'service_recommendation':\n        return responseCount >= 4 || timeElapsed > 240; // 4 minutes\n      default:\n        return false;\n    }\n  }\n\n  // Get next phase\n  getNextPhase(): AssessmentPhase | null {\n    const phases: AssessmentPhase[] = [\n      'warm_welcome',\n      'deep_discovery', \n      'physical_assessment',\n      'vision_reveal',\n      'service_recommendation',\n      'completed'\n    ];\n    \n    const currentIndex = phases.indexOf(this.currentPhase);\n    return currentIndex < phases.length - 1 ? phases[currentIndex + 1] : null;\n  }\n\n  // Personality methods\n  setTrainerPersonality(personalityId: string): void {\n    this.trainerPersonality = getPersonalityById(personalityId) || null;\n  }\n\n  getTrainerPersonality(): TrainerPersonality | null {\n    return this.trainerPersonality;\n  }\n\n  getPersonalityPhrase(type: 'motivational' | 'encouragement' | 'challenge'): string {\n    if (this.trainerPersonality) {\n      return getPersonalityPhrase(this.trainerPersonality, type);\n    }\n    return \"Keep pushing forward!\"; // Default fallback\n  }\n\n  // Getters and setters\n  getCurrentPhase(): AssessmentPhase { return this.currentPhase; }\n  setCurrentPhase(phase: AssessmentPhase): void {\n    this.currentPhase = phase;\n    this.phaseStartTime = new Date();\n  }\n\n  getCurrentSubPhase(): DiscoverySubPhase | undefined { return this.currentSubPhase; }\n  setCurrentSubPhase(subPhase: DiscoverySubPhase): void { this.currentSubPhase = subPhase; }\n\n  getUserProfile(): UserAssessmentProfile { return this.userProfile; }\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;AAEO,MAAM;IACH,eAAgC,eAAe;IAC/C,gBAAoC;IACpC,YAAmC;IACnC,oBAAkD,KAAK;IACvD,iBAAuB,IAAI,OAAO;IAClC,sBAAgC,EAAE,CAAC;IACnC,kBAA2B,MAAM;IACjC,qBAAgD,KAAK;IAE7D,YAAY,QAAgB,EAAE,KAAc,EAAE,aAAsB,CAAE;QACpE,iDAAiD;QACjD,IAAI,CAAC,iBAAiB,CAAC,UAAU;QAEjC,0BAA0B;QAC1B,IAAI,eAAe;YACjB,IAAI,CAAC,kBAAkB,GAAG,IAAA,+JAAkB,EAAC,kBAAkB;QACjE;QAEA,IAAI,CAAC,WAAW,GAAG;YACjB,MAAM;YACN,OAAO,IAAI,CAAC,iBAAiB,EAAE,SAAS,EAAE;YAC1C,YAAY,IAAI,CAAC,iBAAiB,EAAE,cAAc,EAAE;YACpD,aAAa,IAAI,CAAC,iBAAiB,EAAE,eAAe,EAAE;YACtD,mBAAmB,IAAI,CAAC,iBAAiB,EAAE,qBAAqB,EAAE;YAClE,cAAc,IAAI,CAAC,iBAAiB,EAAE;YACtC,gBAAgB,IAAI,CAAC,iBAAiB,EAAE;YACxC,kBAAkB,IAAI,CAAC,iBAAiB,EAAE;YAC1C,oBAAoB,IAAI,CAAC,iBAAiB,EAAE;YAC5C,qBAAqB,IAAI,CAAC,iBAAiB,EAAE;QAC/C;IACF;IAEA,MAAc,kBAAkB,QAAgB,EAAE,KAAc,EAAiB;QAC/E,IAAI;YACF,IAAI,CAAC,iBAAiB,GAAG,MAAM,6JAAkB,CAAC,kBAAkB,CAAC,UAAU;YAC/E,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,GAAG;QAChE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF;IAEA,2CAA2C;IAC3C,mBAA2B;QACzB,MAAM,cAAc,6JAAkB,CAAC,mBAAmB;QAC1D,MAAM,cAAc,IAAI,CAAC,eAAe;QAExC,sDAAsD;QACtD,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B,MAAM,oBAAoB,IAAI,CAAC,kBAAkB,CAAC,YAAY;YAC9D,MAAM,cAAc,cAAc,CAAC,6BAA6B,EAAE,aAAa,GAAG;YAElF,OAAO,GAAG,oBAAoB,YAAY;;8CAEF,EAAE,cAAc,mDAAmD,gDAAgD;;;kBAG/I,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;;;;;AAK1C,EAAE,cAAc,4BAA4B,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,yDAAyD;QACrI;QAEA,uCAAuC;QACvC,IAAI,aAAa;YACf,OAAO,CAAC,cAAc,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;;;;AAIpD,EAAE,YAAY;;;;;;;;;;;mBAWK,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,0DAA0D,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,uFAAuF,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE,IAAI,uBAAuB,kCAAkC,CAAC;QAC7T;QAEA,OAAO,CAAC,6EAA6E,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;;;;;;MAM3G,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;;;;;SAKrB,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;;;;;;;mBAOd,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,6cAA6c,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,2FAA2F,CAAC;IAC1mB;IAEA,iDAAiD;IACjD,mBAAmB,QAA2B,EAAE,gBAAyB,EAAU;QACjF,MAAM,aAAa,CAAC,6CAA6C,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;;0BAEnE,EAAE,SAAS,WAAW,GAAG,OAAO,CAAC,KAAK,KAAK;;;;;;MAM/D,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;gCACE,CAAC;QAE7B,OAAQ;YACN,KAAK;gBACH,OAAO,GAAG,WAAW;;;;;;;;mFAQsD,CAAC;YAE9E,KAAK;gBACH,OAAO,GAAG,WAAW;;;;;;;;0FAQ6D,CAAC;YAErF,KAAK;gBACH,OAAO,GAAG,WAAW;;;;;;;;mGAQsE,CAAC;YAE9F,KAAK;gBACH,OAAO,GAAG,WAAW;;;;;;;;4EAQ+C,CAAC;YAEvE;gBACE,OAAO;QACX;IACF;IAEA,oCAAoC;IACpC,8BAAsC;QACpC,OAAO,CAAC,6DAA6D,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;oGAwBG,CAAC;IACnG;IAEA,6CAA6C;IAC7C,wBAAgC;QAC9B,MAAM,QAAQ,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC;QAC1C,MAAM,cAAc,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC;QAEtD,OAAO,CAAC,4BAA4B,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;;;;;SAKvD,EAAE,MAAM;eACF,EAAE,YAAY;eACd,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM;;;;;;;;;;;CAWvD,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;;yFAEgE,CAAC;IACxF;IAEA,0CAA0C;IAC1C,iCAAyC;QACvC,OAAO,CAAC,qEAAqE,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;wGAkBD,CAAC;IACvG;IAEA,6CAA6C;IAC7C,gBAAgB,QAAgB,EAAE,KAAsB,EAAe;QACrE,MAAM,WAAwB,EAAE;QAChC,MAAM,gBAAgB,SAAS,WAAW;QAE1C,8BAA8B;QAC9B,MAAM,iBAAiB;YAAC;YAAc;YAAW;YAAU;YAAa;YAAa;YAAS;SAAW;QACzG,eAAe,OAAO,CAAC,CAAA;YACrB,IAAI,cAAc,QAAQ,CAAC,OAAO;gBAChC,SAAS,IAAI,CAAC;oBACZ,MAAM;oBACN,YAAY;oBACZ,SAAS,CAAC,uBAAuB,EAAE,MAAM;oBACzC;oBACA,UAAU;gBACZ;YACF;QACF;QAEA,0BAA0B;QAC1B,IAAI,cAAc,QAAQ,CAAC,cAAc,cAAc,QAAQ,CAAC,WAAW,cAAc,QAAQ,CAAC,YAAY;YAC5G,SAAS,IAAI,CAAC;gBACZ,MAAM;gBACN,YAAY;gBACZ,SAAS;gBACT;gBACA,UAAU;YACZ;QACF;QAEA,4BAA4B;QAC5B,MAAM,YAAY;YAAC;YAAY;YAAa;YAAQ;YAAU;YAAQ;YAAW;SAAa;QAC9F,UAAU,OAAO,CAAC,CAAA;YAChB,IAAI,cAAc,QAAQ,CAAC,OAAO;gBAChC,SAAS,IAAI,CAAC;oBACZ,MAAM;oBACN,YAAY;oBACZ,SAAS,CAAC,gCAAgC,EAAE,MAAM;oBAClD;oBACA,UAAU;gBACZ;YACF;QACF;QAEA,uBAAuB;QACvB,MAAM,iBAAiB;YAAC;YAAS;YAAa;YAAW;YAAc;SAAY;QACnF,eAAe,OAAO,CAAC,CAAA;YACrB,IAAI,cAAc,QAAQ,CAAC,OAAO;gBAChC,SAAS,IAAI,CAAC;oBACZ,MAAM;oBACN,YAAY;oBACZ,SAAS,CAAC,0BAA0B,EAAE,MAAM;oBAC5C;oBACA,UAAU;gBACZ;YACF;QACF;QAEA,OAAO;IACT;IAEA,4CAA4C;IAC5C,cAAc,QAAgB,EAAE,KAAsB,EAAE,QAA4B,EAAQ;QAC1F,MAAM,gBAAgB,SAAS,WAAW;QAE1C,IAAI,UAAU,kBAAkB;YAC9B,OAAQ;gBACN,KAAK;oBACH,gBAAgB;oBAChB,IAAI,cAAc,QAAQ,CAAC,kBAAkB,cAAc,QAAQ,CAAC,gBAAgB;wBAClF,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC;oBAC9B;oBACA,IAAI,cAAc,QAAQ,CAAC,aAAa,cAAc,QAAQ,CAAC,aAAa;wBAC1E,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC;oBAC9B;oBACA;gBAEF,KAAK;oBACH,sBAAsB;oBACtB,IAAI,cAAc,QAAQ,CAAC,WAAW,cAAc,QAAQ,CAAC,SAAS;wBACpE,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC;oBACnC;oBACA,IAAI,cAAc,QAAQ,CAAC,iBAAiB,cAAc,QAAQ,CAAC,eAAe;wBAChF,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC;oBACnC;oBACA;gBAEF,KAAK;oBACH,sBAAsB;oBACtB,IAAI,cAAc,QAAQ,CAAC,iBAAiB,cAAc,QAAQ,CAAC,gBAAgB;wBACjF,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC;oBACpC;oBACA,IAAI,cAAc,QAAQ,CAAC,aAAa,cAAc,QAAQ,CAAC,YAAY;wBACzE,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC;oBACpC;oBACA;YACJ;QACF;QAEA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;QAE9B,4BAA4B;QAC5B,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,6JAAkB,CAAC,aAAa,CAAC;gBAC/B,OAAO;uBAAI,IAAI,IAAI;2BAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK;2BAAK,IAAI,CAAC,WAAW,CAAC,KAAK;qBAAC;iBAAE;gBACjF,YAAY;uBAAI,IAAI,IAAI;2BAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU;2BAAK,IAAI,CAAC,WAAW,CAAC,UAAU;qBAAC;iBAAE;gBAChG,aAAa;uBAAI,IAAI,IAAI;2BAAI,IAAI,CAAC,iBAAiB,CAAC,WAAW;2BAAK,IAAI,CAAC,WAAW,CAAC,WAAW;qBAAC;iBAAE;gBACnG,mBAAmB;uBAAI,IAAI,IAAI;2BAAI,IAAI,CAAC,iBAAiB,CAAC,iBAAiB;2BAAK,IAAI,CAAC,WAAW,CAAC,iBAAiB;qBAAC;iBAAE;gBACrH,cAAc,IAAI,CAAC,WAAW,CAAC,YAAY,IAAI,IAAI,CAAC,iBAAiB,CAAC,YAAY;gBAClF,gBAAgB,IAAI,CAAC,WAAW,CAAC,cAAc,IAAI,IAAI,CAAC,iBAAiB,CAAC,cAAc;YAC1F;QACF;IACF;IAEA,2DAA2D;IAC3D,wBAAwB,QAAgB,EAAU;QAChD,MAAM,cAAc,6JAAkB,CAAC,mBAAmB;QAC1D,MAAM,gBAAgB,6JAAkB,CAAC,sBAAsB,CAAC;QAEhE,OAAO,CAAC,cAAc,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;;;AAGlD,EAAE,YAAY;;;AAGd,EAAE,cAAc,GAAG,CAAC,CAAA,MAAO,GAAG,IAAI,IAAI,CAAC,EAAE,EAAE,IAAI,OAAO,EAAE,EAAE,IAAI,CAAC,MAAM;;kBAEnD,EAAE,SAAS;;;;;;;;;;;0DAW6B,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,qBAAqB,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,4BAA4B,EAAE,IAAI,CAAC,WAAW,CAAC,YAAY,IAAI,eAAe,EAAE,CAAC;IAC3O;IAEA,kCAAkC;IAClC,sBAAsB,aAAqB,EAAE,WAAmB,EAAW;QACzE,OAAQ,IAAI,CAAC,YAAY;YACvB,KAAK;gBACH,OAAO,iBAAiB,KAAK,cAAc,KAAK,YAAY;YAC9D,KAAK;gBACH,OAAO,iBAAiB,KAAK,cAAc,KAAK,YAAY;YAC9D,KAAK;gBACH,OAAO,iBAAiB,KAAK,cAAc,KAAK,YAAY;YAC9D,KAAK;gBACH,OAAO,iBAAiB,KAAK,cAAc,KAAK,YAAY;YAC9D,KAAK;gBACH,OAAO,iBAAiB,KAAK,cAAc,KAAK,YAAY;YAC9D;gBACE,OAAO;QACX;IACF;IAEA,iBAAiB;IACjB,eAAuC;QACrC,MAAM,SAA4B;YAChC;YACA;YACA;YACA;YACA;YACA;SACD;QAED,MAAM,eAAe,OAAO,OAAO,CAAC,IAAI,CAAC,YAAY;QACrD,OAAO,eAAe,OAAO,MAAM,GAAG,IAAI,MAAM,CAAC,eAAe,EAAE,GAAG;IACvE;IAEA,sBAAsB;IACtB,sBAAsB,aAAqB,EAAQ;QACjD,IAAI,CAAC,kBAAkB,GAAG,IAAA,+JAAkB,EAAC,kBAAkB;IACjE;IAEA,wBAAmD;QACjD,OAAO,IAAI,CAAC,kBAAkB;IAChC;IAEA,qBAAqB,IAAoD,EAAU;QACjF,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B,OAAO,IAAA,iKAAoB,EAAC,IAAI,CAAC,kBAAkB,EAAE;QACvD;QACA,OAAO,yBAAyB,mBAAmB;IACrD;IAEA,sBAAsB;IACtB,kBAAmC;QAAE,OAAO,IAAI,CAAC,YAAY;IAAE;IAC/D,gBAAgB,KAAsB,EAAQ;QAC5C,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,cAAc,GAAG,IAAI;IAC5B;IAEA,qBAAoD;QAAE,OAAO,IAAI,CAAC,eAAe;IAAE;IACnF,mBAAmB,QAA2B,EAAQ;QAAE,IAAI,CAAC,eAAe,GAAG;IAAU;IAEzF,iBAAwC;QAAE,OAAO,IAAI,CAAC,WAAW;IAAE;AACrE", "debugId": null}}, {"offset": {"line": 1193, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trainer%20test/ai-trainer-assessment/src/app/api/trainer-chat/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport OpenAI from 'openai';\nimport { AssessmentPhase, ConversationMessage, AIInsight, DiscoverySubPhase } from '@/types';\nimport { ConversationEngine } from '@/services/conversationEngine';\n\nconst openai = new OpenAI({\n  apiKey: process.env.OPENAI_API_KEY,\n});\n\n// Store conversation engines per session (in production, use Redis or database)\nconst conversationEngines = new Map<string, ConversationEngine>();\n\nfunction getOrCreateEngine(sessionId: string, userName: string, email?: string, personalityId?: string): ConversationEngine {\n  if (!conversationEngines.has(sessionId)) {\n    conversationEngines.set(sessionId, new ConversationEngine(userName, email, personalityId));\n  }\n  return conversationEngines.get(sessionId)!;\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { \n      message, \n      phase, \n      conversationHistory, \n      userProfile \n    }: {\n      message: string;\n      phase: AssessmentPhase;\n      conversationHistory: ConversationMessage[];\n      userProfile?: any;\n    } = body;\n\n    if (!message || !phase) {\n      return NextResponse.json(\n        { error: 'Message and phase are required' },\n        { status: 400 }\n      );\n    }\n\n    // Build conversation context\n    const contextMessages = conversationHistory.map(msg => ({\n      role: msg.role === 'trainer' ? 'assistant' : 'user',\n      content: msg.content\n    }));\n\n    // Add current user message\n    contextMessages.push({\n      role: 'user',\n      content: message\n    });\n\n    // Get or create conversation engine for this session\n    const userName = userProfile?.name || 'there';\n    const userEmail = userProfile?.email;\n    const personalityId = userProfile?.personalityId;\n    const sessionId = userProfile?.sessionId || 'default';\n    const engine = getOrCreateEngine(sessionId, userName, userEmail, personalityId);\n\n    // Update engine state\n    engine.setCurrentPhase(phase);\n    const subPhase = userProfile?.subPhase as DiscoverySubPhase;\n    if (subPhase) {\n      engine.setCurrentSubPhase(subPhase);\n    }\n\n    // Update user profile based on response\n    engine.updateProfile(message, phase, subPhase);\n\n    // Get phase-specific system prompt\n    let systemPrompt: string;\n    switch (phase) {\n      case 'warm_welcome':\n        systemPrompt = engine.getWelcomePrompt();\n        break;\n      case 'deep_discovery':\n        systemPrompt = engine.getDiscoveryPrompt(subPhase || 'surface_level', message);\n        break;\n      case 'physical_assessment':\n        systemPrompt = engine.getPhysicalAssessmentPrompt();\n        break;\n      case 'vision_reveal':\n        systemPrompt = engine.getVisionRevealPrompt();\n        break;\n      case 'service_recommendation':\n        systemPrompt = engine.getServiceRecommendationPrompt();\n        break;\n      default:\n        systemPrompt = engine.getWelcomePrompt();\n    }\n\n    const response = await openai.chat.completions.create({\n      model: 'gpt-4',\n      messages: [\n        {\n          role: 'system',\n          content: systemPrompt\n        },\n        ...contextMessages.slice(-6) // Keep last 6 messages for context\n      ],\n      temperature: 0.8,\n      max_tokens: 200,\n      presence_penalty: 0.1,\n      frequency_penalty: 0.1\n    });\n\n    const trainerResponse = response.choices[0]?.message?.content;\n\n    if (!trainerResponse) {\n      throw new Error('No response generated');\n    }\n\n    // Analyze the conversation for insights\n    const insights = engine.analyzeResponse(message, phase);\n\n    // Determine if phase should transition\n    const responseCount = conversationHistory.filter(msg => msg.role === 'user').length + 1;\n    const timeElapsed = (Date.now() - new Date().getTime()) / 1000; // Simplified\n    const shouldTransition = engine.shouldTransitionPhase(responseCount, timeElapsed);\n    const nextPhase = shouldTransition ? engine.getNextPhase() : null;\n\n    return NextResponse.json({\n      response: trainerResponse,\n      insights,\n      userProfile: engine.getUserProfile(),\n      shouldTransition,\n      nextPhase,\n      currentSubPhase: engine.getCurrentSubPhase(),\n      success: true\n    });\n\n  } catch (error) {\n    console.error('Trainer chat error:', error);\n    \n    return NextResponse.json(\n      { \n        error: 'Failed to generate trainer response',\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n\n// Legacy function removed - using ConversationEngine.analyzeResponse instead\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAEA;;;;AAEA,MAAM,SAAS,IAAI,mLAAM,CAAC;IACxB,QAAQ,QAAQ,GAAG,CAAC,cAAc;AACpC;AAEA,gFAAgF;AAChF,MAAM,sBAAsB,IAAI;AAEhC,SAAS,kBAAkB,SAAiB,EAAE,QAAgB,EAAE,KAAc,EAAE,aAAsB;IACpG,IAAI,CAAC,oBAAoB,GAAG,CAAC,YAAY;QACvC,oBAAoB,GAAG,CAAC,WAAW,IAAI,6JAAkB,CAAC,UAAU,OAAO;IAC7E;IACA,OAAO,oBAAoB,GAAG,CAAC;AACjC;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,OAAO,EACP,KAAK,EACL,mBAAmB,EACnB,WAAW,EACZ,GAKG;QAEJ,IAAI,CAAC,WAAW,CAAC,OAAO;YACtB,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiC,GAC1C;gBAAE,QAAQ;YAAI;QAElB;QAEA,6BAA6B;QAC7B,MAAM,kBAAkB,oBAAoB,GAAG,CAAC,CAAA,MAAO,CAAC;gBACtD,MAAM,IAAI,IAAI,KAAK,YAAY,cAAc;gBAC7C,SAAS,IAAI,OAAO;YACtB,CAAC;QAED,2BAA2B;QAC3B,gBAAgB,IAAI,CAAC;YACnB,MAAM;YACN,SAAS;QACX;QAEA,qDAAqD;QACrD,MAAM,WAAW,aAAa,QAAQ;QACtC,MAAM,YAAY,aAAa;QAC/B,MAAM,gBAAgB,aAAa;QACnC,MAAM,YAAY,aAAa,aAAa;QAC5C,MAAM,SAAS,kBAAkB,WAAW,UAAU,WAAW;QAEjE,sBAAsB;QACtB,OAAO,eAAe,CAAC;QACvB,MAAM,WAAW,aAAa;QAC9B,IAAI,UAAU;YACZ,OAAO,kBAAkB,CAAC;QAC5B;QAEA,wCAAwC;QACxC,OAAO,aAAa,CAAC,SAAS,OAAO;QAErC,mCAAmC;QACnC,IAAI;QACJ,OAAQ;YACN,KAAK;gBACH,eAAe,OAAO,gBAAgB;gBACtC;YACF,KAAK;gBACH,eAAe,OAAO,kBAAkB,CAAC,YAAY,iBAAiB;gBACtE;YACF,KAAK;gBACH,eAAe,OAAO,2BAA2B;gBACjD;YACF,KAAK;gBACH,eAAe,OAAO,qBAAqB;gBAC3C;YACF,KAAK;gBACH,eAAe,OAAO,8BAA8B;gBACpD;YACF;gBACE,eAAe,OAAO,gBAAgB;QAC1C;QAEA,MAAM,WAAW,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACpD,OAAO;YACP,UAAU;gBACR;oBACE,MAAM;oBACN,SAAS;gBACX;mBACG,gBAAgB,KAAK,CAAC,CAAC,GAAG,mCAAmC;aACjE;YACD,aAAa;YACb,YAAY;YACZ,kBAAkB;YAClB,mBAAmB;QACrB;QAEA,MAAM,kBAAkB,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;QAEtD,IAAI,CAAC,iBAAiB;YACpB,MAAM,IAAI,MAAM;QAClB;QAEA,wCAAwC;QACxC,MAAM,WAAW,OAAO,eAAe,CAAC,SAAS;QAEjD,uCAAuC;QACvC,MAAM,gBAAgB,oBAAoB,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK,QAAQ,MAAM,GAAG;QACtF,MAAM,cAAc,CAAC,KAAK,GAAG,KAAK,IAAI,OAAO,OAAO,EAAE,IAAI,MAAM,aAAa;QAC7E,MAAM,mBAAmB,OAAO,qBAAqB,CAAC,eAAe;QACrE,MAAM,YAAY,mBAAmB,OAAO,YAAY,KAAK;QAE7D,OAAO,gJAAY,CAAC,IAAI,CAAC;YACvB,UAAU;YACV;YACA,aAAa,OAAO,cAAc;YAClC;YACA;YACA,iBAAiB,OAAO,kBAAkB;YAC1C,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QAErC,OAAO,gJAAY,CAAC,IAAI,CACtB;YACE,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF,EAEA,6EAA6E", "debugId": null}}]}