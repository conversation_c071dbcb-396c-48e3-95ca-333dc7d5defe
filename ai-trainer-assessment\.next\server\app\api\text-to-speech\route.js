var R=require("../../../chunks/[turbopack]_runtime.js")("server/app/api/text-to-speech/route.js")
R.c("server/chunks/node_modules_9313e2a2._.js")
R.c("server/chunks/[root-of-the-server]__140a1c01._.js")
R.m("[project]/.next-internal/server/app/api/text-to-speech/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/text-to-speech/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/text-to-speech/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
