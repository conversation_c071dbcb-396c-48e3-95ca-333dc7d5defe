{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trainer%20test/ai-trainer-assessment/src/components/Avatar.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useRef, useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { AvatarState, MouthSyncData } from '@/types';\n\ninterface HologramProps {\n  state: AvatarState;\n  className?: string;\n  size?: 'small' | 'medium' | 'large';\n}\n\nexport const Hologram: React.FC<HologramProps> = ({\n  state,\n  className = '',\n  size = 'medium'\n}) => {\n  const [currentMouthShape, setCurrentMouthShape] = useState<'closed' | 'mid' | 'open'>('closed');\n  const animationRef = useRef<number>();\n\n  // Size configurations\n  const sizeConfig = {\n    small: { width: 120, height: 120, scale: 0.8 },\n    medium: { width: 200, height: 200, scale: 1 },\n    large: { width: 300, height: 300, scale: 1.2 }\n  };\n\n  const config = sizeConfig[size];\n\n  // Animate mouth based on speech data\n  useEffect(() => {\n    if (state.mouthSyncData && state.currentAnimation === 'talking') {\n      const animateMouth = () => {\n        const currentTime = Date.now() - (state.mouthSyncData?.currentTime || 0);\n        const currentPhoneme = state.mouthSyncData?.phonemes.find(\n          p => currentTime >= p.start * 1000 && currentTime <= p.end * 1000\n        );\n\n        if (currentPhoneme) {\n          setCurrentMouthShape(currentPhoneme.phoneme as 'closed' | 'mid' | 'open');\n        } else {\n          setCurrentMouthShape('closed');\n        }\n\n        if (currentTime < (state.mouthSyncData?.duration || 0) * 1000) {\n          animationRef.current = requestAnimationFrame(animateMouth);\n        }\n      };\n\n      animationRef.current = requestAnimationFrame(animateMouth);\n    } else {\n      setCurrentMouthShape('closed');\n    }\n\n    return () => {\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n    };\n  }, [state.mouthSyncData, state.currentAnimation]);\n\n  // Animation variants for the orb\n  const containerVariants = {\n    idle: {\n      y: [0, -10, 0],\n      rotate: [0, 2, -2, 0],\n      transition: { duration: 4, repeat: Infinity, ease: 'easeInOut' }\n    },\n    talking: {\n      y: [0, -5, 0],\n      scale: [1, 1.03, 1],\n      transition: { duration: 0.6, repeat: Infinity, ease: 'easeInOut' }\n    },\n    listening: {\n      y: [0, -8, 0],\n      rotate: [0, 3, -3, 0],\n      transition: { duration: 2.5, repeat: Infinity, ease: 'easeInOut' }\n    },\n    thinking: {\n      y: [0, -12, 0],\n      rotate: [0, 10, -10, 0],\n      transition: { duration: 3, repeat: Infinity, ease: 'easeInOut' }\n    }\n  };\n\n  return (\n    <div className={`flex items-center justify-center ${className}`}>\n      <motion.div\n        className=\"relative\"\n        style={{ width: config.width, height: config.height }}\n        variants={containerVariants}\n        animate={state.currentAnimation}\n        initial=\"idle\"\n      >\n        {/* Main Orb - Translucent Core */}\n        <motion.div\n          className=\"absolute inset-0 rounded-full\"\n          style={{\n            background: 'radial-gradient(circle at 30% 30%, rgba(34, 211, 238, 0.4), rgba(59, 130, 246, 0.3), rgba(147, 51, 234, 0.2), transparent)',\n            backdropFilter: 'blur(2px)',\n            border: '1px solid rgba(34, 211, 238, 0.3)',\n            boxShadow: '0 0 60px rgba(34, 211, 238, 0.4), inset 0 0 60px rgba(59, 130, 246, 0.2)'\n          }}\n          animate={{\n            opacity: [0.6, 0.9, 0.6],\n            scale: state.currentAnimation === 'talking'\n              ? [1, 1.05, 1]\n              : [1, 1.02, 1]\n          }}\n          transition={{\n            duration: state.currentAnimation === 'talking' ? 0.5 : 2,\n            repeat: Infinity,\n            ease: 'easeInOut'\n          }}\n        />\n\n        {/* Inner Energy Core */}\n        <motion.div\n          className=\"absolute inset-8 rounded-full\"\n          style={{\n            background: 'radial-gradient(circle, rgba(34, 211, 238, 0.6), rgba(59, 130, 246, 0.4), transparent)',\n            filter: 'blur(1px)'\n          }}\n          animate={{\n            opacity: [0.4, 0.8, 0.4],\n            scale: [0.8, 1.2, 0.8],\n            rotate: [0, 360]\n          }}\n          transition={{\n            duration: 4,\n            repeat: Infinity,\n            ease: 'linear'\n          }}\n        />\n\n        {/* Floating Particles */}\n        {[...Array(6)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-2 h-2 bg-cyan-400 rounded-full\"\n            style={{\n              left: `${20 + (i * 10)}%`,\n              top: `${30 + (i * 8)}%`,\n              boxShadow: '0 0 10px rgba(34, 211, 238, 0.8)'\n            }}\n            animate={{\n              y: [-10, 10, -10],\n              x: [-5, 5, -5],\n              opacity: [0.3, 1, 0.3],\n              scale: [0.5, 1, 0.5]\n            }}\n            transition={{\n              duration: 3 + (i * 0.5),\n              repeat: Infinity,\n              ease: 'easeInOut',\n              delay: i * 0.3\n            }}\n          />\n        ))}\n\n        {/* Hologram Scan Lines */}\n        <motion.div\n          className=\"absolute inset-0 overflow-hidden rounded-full\"\n          style={{\n            background: 'repeating-linear-gradient(0deg, transparent, transparent 3px, rgba(34, 211, 238, 0.1) 3px, rgba(34, 211, 238, 0.1) 6px)'\n          }}\n          animate={{\n            y: [-30, 30, -30]\n          }}\n          transition={{\n            duration: 4,\n            repeat: Infinity,\n            ease: 'linear'\n          }}\n        />\n\n        {/* Outer Glow Ring */}\n        <motion.div\n          className=\"absolute -inset-4 rounded-full border border-cyan-400 opacity-30\"\n          animate={{\n            scale: [1, 1.1, 1],\n            opacity: [0.2, 0.5, 0.2],\n            rotate: [0, 360]\n          }}\n          transition={{\n            duration: 6,\n            repeat: Infinity,\n            ease: 'linear'\n          }}\n        />\n\n        {/* Speaking - Intense Energy Pulses */}\n        <AnimatePresence>\n          {state.currentAnimation === 'talking' && (\n            <>\n              {[...Array(4)].map((_, i) => (\n                <motion.div\n                  key={i}\n                  className=\"absolute rounded-full border border-cyan-400\"\n                  style={{\n                    inset: `${-8 - (i * 8)}px`,\n                    boxShadow: '0 0 30px rgba(34, 211, 238, 0.4)'\n                  }}\n                  initial={{ opacity: 0, scale: 0.8 }}\n                  animate={{\n                    opacity: [0, 0.8, 0],\n                    scale: [0.8, 1.4, 1.8]\n                  }}\n                  exit={{ opacity: 0 }}\n                  transition={{\n                    duration: 1.2,\n                    repeat: Infinity,\n                    delay: i * 0.15,\n                    ease: 'easeOut'\n                  }}\n                />\n              ))}\n              {/* Central energy burst */}\n              <motion.div\n                className=\"absolute inset-4 rounded-full bg-cyan-400\"\n                style={{\n                  filter: 'blur(2px)',\n                  opacity: 0.3\n                }}\n                animate={{\n                  scale: [0.5, 1.5, 0.5],\n                  opacity: [0.1, 0.4, 0.1]\n                }}\n                transition={{\n                  duration: 0.8,\n                  repeat: Infinity,\n                  ease: 'easeInOut'\n                }}\n              />\n            </>\n          )}\n        </AnimatePresence>\n\n        {/* Listening - Gentle Ripples */}\n        <AnimatePresence>\n          {state.currentAnimation === 'listening' && (\n            <>\n              {[...Array(5)].map((_, i) => (\n                <motion.div\n                  key={i}\n                  className=\"absolute rounded-full border border-green-400\"\n                  style={{\n                    inset: `${-12 - (i * 6)}px`,\n                    boxShadow: '0 0 20px rgba(34, 197, 94, 0.3)'\n                  }}\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{\n                    opacity: [0, 0.6, 0],\n                    scale: [0.9, 1.6, 2.2]\n                  }}\n                  exit={{ opacity: 0 }}\n                  transition={{\n                    duration: 3,\n                    repeat: Infinity,\n                    delay: i * 0.4,\n                    ease: 'easeOut'\n                  }}\n                />\n              ))}\n              {/* Listening indicator dots */}\n              {[...Array(3)].map((_, i) => (\n                <motion.div\n                  key={`dot-${i}`}\n                  className=\"absolute w-3 h-3 bg-green-400 rounded-full\"\n                  style={{\n                    left: `${40 + (i * 8)}%`,\n                    top: '45%',\n                    boxShadow: '0 0 15px rgba(34, 197, 94, 0.8)'\n                  }}\n                  animate={{\n                    opacity: [0.3, 1, 0.3],\n                    scale: [0.8, 1.2, 0.8]\n                  }}\n                  transition={{\n                    duration: 1.5,\n                    repeat: Infinity,\n                    delay: i * 0.2,\n                    ease: 'easeInOut'\n                  }}\n                />\n              ))}\n            </>\n          )}\n        </AnimatePresence>\n\n        {/* Thinking - Swirling Energy */}\n        <AnimatePresence>\n          {state.currentAnimation === 'thinking' && (\n            <>\n              {/* Orbital thinking particles */}\n              {[...Array(8)].map((_, i) => (\n                <motion.div\n                  key={i}\n                  className=\"absolute w-2 h-2 bg-purple-400 rounded-full\"\n                  style={{\n                    boxShadow: '0 0 12px rgba(147, 51, 234, 0.8)'\n                  }}\n                  animate={{\n                    rotate: [0, 360],\n                    scale: [0.5, 1.2, 0.5],\n                    opacity: [0.4, 1, 0.4]\n                  }}\n                  transition={{\n                    duration: 4,\n                    repeat: Infinity,\n                    delay: i * 0.2,\n                    ease: 'linear'\n                  }}\n                  initial={{\n                    left: '50%',\n                    top: '50%',\n                    x: `${Math.cos((i * 45) * Math.PI / 180) * 60}px`,\n                    y: `${Math.sin((i * 45) * Math.PI / 180) * 60}px`\n                  }}\n                />\n              ))}\n              {/* Central thinking core */}\n              <motion.div\n                className=\"absolute inset-6 rounded-full bg-purple-400\"\n                style={{\n                  filter: 'blur(3px)',\n                  opacity: 0.2\n                }}\n                animate={{\n                  scale: [0.8, 1.3, 0.8],\n                  opacity: [0.1, 0.3, 0.1],\n                  rotate: [0, 180, 360]\n                }}\n                transition={{\n                  duration: 2,\n                  repeat: Infinity,\n                  ease: 'easeInOut'\n                }}\n              />\n            </>\n          )}\n        </AnimatePresence>\n\n        {/* Subtle Glitch Effect */}\n        <motion.div\n          className=\"absolute inset-0 rounded-full\"\n          style={{\n            background: 'linear-gradient(90deg, transparent 0%, rgba(34, 211, 238, 0.05) 50%, transparent 100%)'\n          }}\n          animate={{\n            x: [-150, 150, -150],\n            opacity: [0, 0.2, 0]\n          }}\n          transition={{\n            duration: 6,\n            repeat: Infinity,\n            ease: 'linear'\n          }}\n        />\n      </motion.div>\n\n      {/* Holographic Status Display */}\n      <motion.div\n        className=\"absolute -bottom-16 text-center\"\n        initial={{ opacity: 0, y: 10 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.5 }}\n      >\n        <div className=\"bg-black bg-opacity-60 px-6 py-3 rounded-lg border border-cyan-400 backdrop-blur-sm\">\n          <motion.p\n            className=\"text-sm text-cyan-300 font-mono\"\n            animate={{\n              textShadow: state.currentAnimation === 'talking'\n                ? ['0 0 5px rgba(34, 211, 238, 0.8)', '0 0 15px rgba(34, 211, 238, 1)', '0 0 5px rgba(34, 211, 238, 0.8)']\n                : '0 0 5px rgba(34, 211, 238, 0.6)'\n            }}\n            transition={{ duration: 0.8, repeat: Infinity }}\n          >\n            {state.currentAnimation === 'idle' && '◦ ALEX ONLINE ◦'}\n            {state.currentAnimation === 'talking' && '◦ TRANSMITTING ◦'}\n            {state.currentAnimation === 'listening' && '◦ RECEIVING ◦'}\n            {state.currentAnimation === 'thinking' && '◦ PROCESSING ◦'}\n          </motion.p>\n        </div>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default Hologram;\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAAA;;;AAHA;;;AAYO,MAAM,WAAoC;QAAC,EAChD,KAAK,EACL,YAAY,EAAE,EACd,OAAO,QAAQ,EAChB;;IACC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,IAAA,yKAAQ,EAA4B;IACtF,MAAM,eAAe,IAAA,uKAAM;IAE3B,sBAAsB;IACtB,MAAM,aAAa;QACjB,OAAO;YAAE,OAAO;YAAK,QAAQ;YAAK,OAAO;QAAI;QAC7C,QAAQ;YAAE,OAAO;YAAK,QAAQ;YAAK,OAAO;QAAE;QAC5C,OAAO;YAAE,OAAO;YAAK,QAAQ;YAAK,OAAO;QAAI;IAC/C;IAEA,MAAM,SAAS,UAAU,CAAC,KAAK;IAE/B,qCAAqC;IACrC,IAAA,0KAAS;8BAAC;YACR,IAAI,MAAM,aAAa,IAAI,MAAM,gBAAgB,KAAK,WAAW;gBAC/D,MAAM;uDAAe;4BACe,sBACX,uBAUJ;wBAXnB,MAAM,cAAc,KAAK,GAAG,KAAK,CAAC,EAAA,uBAAA,MAAM,aAAa,cAAnB,2CAAA,qBAAqB,WAAW,KAAI,CAAC;wBACvE,MAAM,kBAAiB,wBAAA,MAAM,aAAa,cAAnB,4CAAA,sBAAqB,QAAQ,CAAC,IAAI;+DACvD,CAAA,IAAK,eAAe,EAAE,KAAK,GAAG,QAAQ,eAAe,EAAE,GAAG,GAAG;;wBAG/D,IAAI,gBAAgB;4BAClB,qBAAqB,eAAe,OAAO;wBAC7C,OAAO;4BACL,qBAAqB;wBACvB;wBAEA,IAAI,cAAc,CAAC,EAAA,wBAAA,MAAM,aAAa,cAAnB,4CAAA,sBAAqB,QAAQ,KAAI,CAAC,IAAI,MAAM;4BAC7D,aAAa,OAAO,GAAG,sBAAsB;wBAC/C;oBACF;;gBAEA,aAAa,OAAO,GAAG,sBAAsB;YAC/C,OAAO;gBACL,qBAAqB;YACvB;YAEA;sCAAO;oBACL,IAAI,aAAa,OAAO,EAAE;wBACxB,qBAAqB,aAAa,OAAO;oBAC3C;gBACF;;QACF;6BAAG;QAAC,MAAM,aAAa;QAAE,MAAM,gBAAgB;KAAC;IAEhD,iCAAiC;IACjC,MAAM,oBAAoB;QACxB,MAAM;YACJ,GAAG;gBAAC;gBAAG,CAAC;gBAAI;aAAE;YACd,QAAQ;gBAAC;gBAAG;gBAAG,CAAC;gBAAG;aAAE;YACrB,YAAY;gBAAE,UAAU;gBAAG,QAAQ;gBAAU,MAAM;YAAY;QACjE;QACA,SAAS;YACP,GAAG;gBAAC;gBAAG,CAAC;gBAAG;aAAE;YACb,OAAO;gBAAC;gBAAG;gBAAM;aAAE;YACnB,YAAY;gBAAE,UAAU;gBAAK,QAAQ;gBAAU,MAAM;YAAY;QACnE;QACA,WAAW;YACT,GAAG;gBAAC;gBAAG,CAAC;gBAAG;aAAE;YACb,QAAQ;gBAAC;gBAAG;gBAAG,CAAC;gBAAG;aAAE;YACrB,YAAY;gBAAE,UAAU;gBAAK,QAAQ;gBAAU,MAAM;YAAY;QACnE;QACA,UAAU;YACR,GAAG;gBAAC;gBAAG,CAAC;gBAAI;aAAE;YACd,QAAQ;gBAAC;gBAAG;gBAAI,CAAC;gBAAI;aAAE;YACvB,YAAY;gBAAE,UAAU;gBAAG,QAAQ;gBAAU,MAAM;YAAY;QACjE;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,oCAA6C,OAAV;;0BAClD,6LAAC,uMAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBAAE,OAAO,OAAO,KAAK;oBAAE,QAAQ,OAAO,MAAM;gBAAC;gBACpD,UAAU;gBACV,SAAS,MAAM,gBAAgB;gBAC/B,SAAQ;;kCAGR,6LAAC,uMAAM,CAAC,GAAG;wBACT,WAAU;wBACV,OAAO;4BACL,YAAY;4BACZ,gBAAgB;4BAChB,QAAQ;4BACR,WAAW;wBACb;wBACA,SAAS;4BACP,SAAS;gCAAC;gCAAK;gCAAK;6BAAI;4BACxB,OAAO,MAAM,gBAAgB,KAAK,YAC9B;gCAAC;gCAAG;gCAAM;6BAAE,GACZ;gCAAC;gCAAG;gCAAM;6BAAE;wBAClB;wBACA,YAAY;4BACV,UAAU,MAAM,gBAAgB,KAAK,YAAY,MAAM;4BACvD,QAAQ;4BACR,MAAM;wBACR;;;;;;kCAIF,6LAAC,uMAAM,CAAC,GAAG;wBACT,WAAU;wBACV,OAAO;4BACL,YAAY;4BACZ,QAAQ;wBACV;wBACA,SAAS;4BACP,SAAS;gCAAC;gCAAK;gCAAK;6BAAI;4BACxB,OAAO;gCAAC;gCAAK;gCAAK;6BAAI;4BACtB,QAAQ;gCAAC;gCAAG;6BAAI;wBAClB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;oBAID;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,uMAAM,CAAC,GAAG;4BAET,WAAU;4BACV,OAAO;gCACL,MAAM,AAAC,GAAgB,OAAd,KAAM,IAAI,IAAI;gCACvB,KAAK,AAAC,GAAe,OAAb,KAAM,IAAI,GAAG;gCACrB,WAAW;4BACb;4BACA,SAAS;gCACP,GAAG;oCAAC,CAAC;oCAAI;oCAAI,CAAC;iCAAG;gCACjB,GAAG;oCAAC,CAAC;oCAAG;oCAAG,CAAC;iCAAE;gCACd,SAAS;oCAAC;oCAAK;oCAAG;iCAAI;gCACtB,OAAO;oCAAC;oCAAK;oCAAG;iCAAI;4BACtB;4BACA,YAAY;gCACV,UAAU,IAAK,IAAI;gCACnB,QAAQ;gCACR,MAAM;gCACN,OAAO,IAAI;4BACb;2BAlBK;;;;;kCAuBT,6LAAC,uMAAM,CAAC,GAAG;wBACT,WAAU;wBACV,OAAO;4BACL,YAAY;wBACd;wBACA,SAAS;4BACP,GAAG;gCAAC,CAAC;gCAAI;gCAAI,CAAC;6BAAG;wBACnB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;kCAIF,6LAAC,uMAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;4BAClB,SAAS;gCAAC;gCAAK;gCAAK;6BAAI;4BACxB,QAAQ;gCAAC;gCAAG;6BAAI;wBAClB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;kCAIF,6LAAC,+MAAe;kCACb,MAAM,gBAAgB,KAAK,2BAC1B;;gCACG;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,uMAAM,CAAC,GAAG;wCAET,WAAU;wCACV,OAAO;4CACL,OAAO,AAAC,GAAe,OAAb,CAAC,IAAK,IAAI,GAAG;4CACvB,WAAW;wCACb;wCACA,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,SAAS;4CACP,SAAS;gDAAC;gDAAG;gDAAK;6CAAE;4CACpB,OAAO;gDAAC;gDAAK;gDAAK;6CAAI;wCACxB;wCACA,MAAM;4CAAE,SAAS;wCAAE;wCACnB,YAAY;4CACV,UAAU;4CACV,QAAQ;4CACR,OAAO,IAAI;4CACX,MAAM;wCACR;uCAjBK;;;;;8CAqBT,6LAAC,uMAAM,CAAC,GAAG;oCACT,WAAU;oCACV,OAAO;wCACL,QAAQ;wCACR,SAAS;oCACX;oCACA,SAAS;wCACP,OAAO;4CAAC;4CAAK;4CAAK;yCAAI;wCACtB,SAAS;4CAAC;4CAAK;4CAAK;yCAAI;oCAC1B;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,MAAM;oCACR;;;;;;;;;;;;;kCAOR,6LAAC,+MAAe;kCACb,MAAM,gBAAgB,KAAK,6BAC1B;;gCACG;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,uMAAM,CAAC,GAAG;wCAET,WAAU;wCACV,OAAO;4CACL,OAAO,AAAC,GAAgB,OAAd,CAAC,KAAM,IAAI,GAAG;4CACxB,WAAW;wCACb;wCACA,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,SAAS;4CACP,SAAS;gDAAC;gDAAG;gDAAK;6CAAE;4CACpB,OAAO;gDAAC;gDAAK;gDAAK;6CAAI;wCACxB;wCACA,MAAM;4CAAE,SAAS;wCAAE;wCACnB,YAAY;4CACV,UAAU;4CACV,QAAQ;4CACR,OAAO,IAAI;4CACX,MAAM;wCACR;uCAjBK;;;;;gCAqBR;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,uMAAM,CAAC,GAAG;wCAET,WAAU;wCACV,OAAO;4CACL,MAAM,AAAC,GAAe,OAAb,KAAM,IAAI,GAAG;4CACtB,KAAK;4CACL,WAAW;wCACb;wCACA,SAAS;4CACP,SAAS;gDAAC;gDAAK;gDAAG;6CAAI;4CACtB,OAAO;gDAAC;gDAAK;gDAAK;6CAAI;wCACxB;wCACA,YAAY;4CACV,UAAU;4CACV,QAAQ;4CACR,OAAO,IAAI;4CACX,MAAM;wCACR;uCAhBK,AAAC,OAAQ,OAAF;;;;;;;;;;;;kCAwBtB,6LAAC,+MAAe;kCACb,MAAM,gBAAgB,KAAK,4BAC1B;;gCAEG;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,uMAAM,CAAC,GAAG;wCAET,WAAU;wCACV,OAAO;4CACL,WAAW;wCACb;wCACA,SAAS;4CACP,QAAQ;gDAAC;gDAAG;6CAAI;4CAChB,OAAO;gDAAC;gDAAK;gDAAK;6CAAI;4CACtB,SAAS;gDAAC;gDAAK;gDAAG;6CAAI;wCACxB;wCACA,YAAY;4CACV,UAAU;4CACV,QAAQ;4CACR,OAAO,IAAI;4CACX,MAAM;wCACR;wCACA,SAAS;4CACP,MAAM;4CACN,KAAK;4CACL,GAAG,AAAC,GAA0C,OAAxC,KAAK,GAAG,CAAC,AAAC,IAAI,KAAM,KAAK,EAAE,GAAG,OAAO,IAAG;4CAC9C,GAAG,AAAC,GAA0C,OAAxC,KAAK,GAAG,CAAC,AAAC,IAAI,KAAM,KAAK,EAAE,GAAG,OAAO,IAAG;wCAChD;uCArBK;;;;;8CAyBT,6LAAC,uMAAM,CAAC,GAAG;oCACT,WAAU;oCACV,OAAO;wCACL,QAAQ;wCACR,SAAS;oCACX;oCACA,SAAS;wCACP,OAAO;4CAAC;4CAAK;4CAAK;yCAAI;wCACtB,SAAS;4CAAC;4CAAK;4CAAK;yCAAI;wCACxB,QAAQ;4CAAC;4CAAG;4CAAK;yCAAI;oCACvB;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,MAAM;oCACR;;;;;;;;;;;;;kCAOR,6LAAC,uMAAM,CAAC,GAAG;wBACT,WAAU;wBACV,OAAO;4BACL,YAAY;wBACd;wBACA,SAAS;4BACP,GAAG;gCAAC,CAAC;gCAAK;gCAAK,CAAC;6BAAI;4BACpB,SAAS;gCAAC;gCAAG;gCAAK;6BAAE;wBACtB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;;;;;;;0BAKJ,6LAAC,uMAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;0BAEzB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,uMAAM,CAAC,CAAC;wBACP,WAAU;wBACV,SAAS;4BACP,YAAY,MAAM,gBAAgB,KAAK,YACnC;gCAAC;gCAAmC;gCAAkC;6BAAkC,GACxG;wBACN;wBACA,YAAY;4BAAE,UAAU;4BAAK,QAAQ;wBAAS;;4BAE7C,MAAM,gBAAgB,KAAK,UAAU;4BACrC,MAAM,gBAAgB,KAAK,aAAa;4BACxC,MAAM,gBAAgB,KAAK,eAAe;4BAC1C,MAAM,gBAAgB,KAAK,cAAc;;;;;;;;;;;;;;;;;;;;;;;AAMtD;GAvXa;KAAA;uCAyXE", "debugId": null}}, {"offset": {"line": 665, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trainer%20test/ai-trainer-assessment/src/services/speechService.ts"], "sourcesContent": ["import { SpeechState, Phoneme, MouthSyncData } from '@/types';\n\nexport class SpeechService {\n  private mediaRecorder: MediaRecorder | null = null;\n  private audioChunks: Blob[] = [];\n  private recognition: SpeechRecognition | null = null;\n  private synthesis: SpeechSynthesis;\n  private currentUtterance: SpeechSynthesisUtterance | null = null;\n  private currentAudio: HTMLAudioElement | null = null;\n\n  constructor() {\n    // Only initialize in browser environment\n    if (typeof window !== 'undefined') {\n      this.synthesis = window.speechSynthesis;\n      this.initializeSpeechRecognition();\n    }\n  }\n\n  private initializeSpeechRecognition() {\n    if (typeof window !== 'undefined' && ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {\n      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n      this.recognition = new SpeechRecognition();\n\n      this.recognition.continuous = true;\n      this.recognition.interimResults = true;\n      this.recognition.lang = 'en-US';\n    }\n  }\n\n  // Start listening for speech\n  async startListening(\n    onTranscript: (transcript: string, isFinal: boolean) => void,\n    onError: (error: string) => void\n  ): Promise<void> {\n    if (!this.recognition) {\n      onError('Speech recognition not supported');\n      return;\n    }\n\n    try {\n      this.recognition.onresult = (event) => {\n        let transcript = '';\n        let isFinal = false;\n\n        for (let i = event.resultIndex; i < event.results.length; i++) {\n          const result = event.results[i];\n          transcript += result[0].transcript;\n          if (result.isFinal) {\n            isFinal = true;\n          }\n        }\n\n        onTranscript(transcript, isFinal);\n      };\n\n      this.recognition.onerror = (event) => {\n        onError(`Speech recognition error: ${event.error}`);\n      };\n\n      this.recognition.start();\n    } catch (error) {\n      onError(`Failed to start speech recognition: ${error}`);\n    }\n  }\n\n  // Stop listening\n  stopListening(): void {\n    if (this.recognition) {\n      this.recognition.stop();\n    }\n  }\n\n  // Convert speech to text using Whisper API\n  async transcribeAudio(audioBlob: Blob): Promise<string> {\n    try {\n      const formData = new FormData();\n      formData.append('file', audioBlob, 'audio.wav');\n      formData.append('model', 'whisper-1');\n\n      const response = await fetch('/api/transcribe', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        throw new Error(`Transcription failed: ${response.statusText}`);\n      }\n\n      const result = await response.json();\n      return result.text;\n    } catch (error) {\n      console.error('Transcription error:', error);\n      throw error;\n    }\n  }\n\n  // Text-to-speech using OpenAI TTS API\n  async speak(\n    text: string,\n    onStart?: () => void,\n    onEnd?: () => void,\n    onMouthSync?: (data: MouthSyncData) => void\n  ): Promise<void> {\n    try {\n      onStart?.();\n\n      // Generate mouth sync data immediately\n      const mouthSyncData = this.generateMouthSyncData(text);\n      onMouthSync?.(mouthSyncData);\n\n      // Call OpenAI TTS API\n      const response = await fetch('/api/text-to-speech', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ text })\n      });\n\n      if (!response.ok) {\n        throw new Error(`TTS API failed: ${response.statusText}`);\n      }\n\n      // Get audio blob from response\n      const audioBlob = await response.blob();\n\n      // Create audio element and play\n      this.currentAudio = new Audio();\n      const audioUrl = URL.createObjectURL(audioBlob);\n      this.currentAudio.src = audioUrl;\n\n      return new Promise((resolve, reject) => {\n        if (!this.currentAudio) return reject(new Error('Audio creation failed'));\n\n        this.currentAudio.onended = () => {\n          URL.revokeObjectURL(audioUrl);\n          this.currentAudio = null;\n          onEnd?.();\n          resolve();\n        };\n\n        this.currentAudio.onerror = () => {\n          URL.revokeObjectURL(audioUrl);\n          this.currentAudio = null;\n          reject(new Error('Audio playback failed'));\n        };\n\n        this.currentAudio.play().catch(reject);\n      });\n\n    } catch (error) {\n      console.error('TTS error:', error);\n      // Fallback to browser speech synthesis\n      return this.fallbackSpeak(text, onStart, onEnd, onMouthSync);\n    }\n  }\n\n  // Fallback to browser speech synthesis\n  private async fallbackSpeak(\n    text: string,\n    onStart?: () => void,\n    onEnd?: () => void,\n    onMouthSync?: (data: MouthSyncData) => void\n  ): Promise<void> {\n    return new Promise((resolve, reject) => {\n      if (!this.synthesis) {\n        reject(new Error('Speech synthesis not supported'));\n        return;\n      }\n\n      // Cancel any ongoing speech\n      this.synthesis.cancel();\n\n      this.currentUtterance = new SpeechSynthesisUtterance(text);\n\n      // Configure voice settings for a warm, professional trainer\n      this.currentUtterance.rate = 0.9;\n      this.currentUtterance.pitch = 1.1;\n      this.currentUtterance.volume = 0.8;\n\n      // Try to find a good voice\n      const voices = this.synthesis.getVoices();\n      const preferredVoice = voices.find(voice =>\n        voice.name.includes('Google') && voice.lang.startsWith('en')\n      ) || voices.find(voice => voice.lang.startsWith('en'));\n\n      if (preferredVoice) {\n        this.currentUtterance.voice = preferredVoice;\n      }\n\n      this.currentUtterance.onstart = () => {\n        onStart?.();\n        // Generate mouth sync data\n        const mouthSyncData = this.generateMouthSyncData(text);\n        onMouthSync?.(mouthSyncData);\n      };\n\n      this.currentUtterance.onend = () => {\n        onEnd?.();\n        resolve();\n      };\n\n      this.currentUtterance.onerror = (event) => {\n        reject(new Error(`Speech synthesis error: ${event.error}`));\n      };\n\n      this.synthesis.speak(this.currentUtterance);\n    });\n  }\n\n  // Stop current speech\n  stopSpeaking(): void {\n    if (this.synthesis) {\n      this.synthesis.cancel();\n    }\n  }\n\n  // Generate mouth sync data for avatar animation\n  private generateMouthSyncData(text: string): MouthSyncData {\n    const words = text.split(' ');\n    const phonemes: Phoneme[] = [];\n    let currentTime = 0;\n    const averageWordDuration = 0.6; // seconds per word\n\n    words.forEach((word, index) => {\n      const wordDuration = averageWordDuration * (word.length / 5); // Adjust based on word length\n      \n      // Simple phoneme mapping (in a real app, you'd use a proper phoneme library)\n      const wordPhonemes = this.mapWordToPhonemes(word);\n      const phonemeDuration = wordDuration / wordPhonemes.length;\n\n      wordPhonemes.forEach((phoneme, pIndex) => {\n        phonemes.push({\n          phoneme,\n          start: currentTime + (pIndex * phonemeDuration),\n          end: currentTime + ((pIndex + 1) * phonemeDuration),\n          intensity: this.getPhonemeIntensity(phoneme)\n        });\n      });\n\n      currentTime += wordDuration + 0.1; // Small pause between words\n    });\n\n    return {\n      phonemes,\n      duration: currentTime,\n      currentTime: 0\n    };\n  }\n\n  // Simple phoneme mapping (simplified for demo)\n  private mapWordToPhonemes(word: string): string[] {\n    // This is a very simplified mapping. In production, use a proper phoneme library\n    const vowels = ['a', 'e', 'i', 'o', 'u'];\n    const phonemes: string[] = [];\n    \n    for (let i = 0; i < word.length; i++) {\n      const char = word[i].toLowerCase();\n      if (vowels.includes(char)) {\n        phonemes.push('open'); // Open mouth for vowels\n      } else if (char === 'm' || char === 'p' || char === 'b') {\n        phonemes.push('closed'); // Closed mouth for bilabials\n      } else {\n        phonemes.push('mid'); // Mid position for other consonants\n      }\n    }\n\n    return phonemes.length > 0 ? phonemes : ['mid'];\n  }\n\n  // Get intensity for mouth animation\n  private getPhonemeIntensity(phoneme: string): number {\n    switch (phoneme) {\n      case 'open': return 0.8;\n      case 'closed': return 0.1;\n      case 'mid': return 0.5;\n      default: return 0.5;\n    }\n  }\n\n  // Record audio for Whisper transcription\n  async startRecording(): Promise<void> {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\n      this.mediaRecorder = new MediaRecorder(stream);\n      this.audioChunks = [];\n\n      this.mediaRecorder.ondataavailable = (event) => {\n        this.audioChunks.push(event.data);\n      };\n\n      this.mediaRecorder.start();\n    } catch (error) {\n      throw new Error(`Failed to start recording: ${error}`);\n    }\n  }\n\n  async stopRecording(): Promise<Blob> {\n    return new Promise((resolve, reject) => {\n      if (!this.mediaRecorder) {\n        reject(new Error('No active recording'));\n        return;\n      }\n\n      this.mediaRecorder.onstop = () => {\n        const audioBlob = new Blob(this.audioChunks, { type: 'audio/wav' });\n        resolve(audioBlob);\n      };\n\n      this.mediaRecorder.stop();\n      \n      // Stop all tracks to release microphone\n      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n    });\n  }\n\n  // Check if speech services are available\n  isAvailable(): boolean {\n    return typeof window !== 'undefined' && !!(this.recognition && this.synthesis);\n  }\n}\n\n// Global speech service instance (only in browser)\nexport const speechService = typeof window !== 'undefined' ? new SpeechService() : null;\n"], "names": [], "mappings": ";;;;;;;;AAEO,MAAM;IAgBH,8BAA8B;QACpC,IAAI,aAAkB,eAAe,CAAC,6BAA6B,UAAU,uBAAuB,MAAM,GAAG;YAC3G,MAAM,oBAAoB,OAAO,iBAAiB,IAAI,OAAO,uBAAuB;YACpF,IAAI,CAAC,WAAW,GAAG,IAAI;YAEvB,IAAI,CAAC,WAAW,CAAC,UAAU,GAAG;YAC9B,IAAI,CAAC,WAAW,CAAC,cAAc,GAAG;YAClC,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG;QAC1B;IACF;IAEA,6BAA6B;IAC7B,MAAM,eACJ,YAA4D,EAC5D,OAAgC,EACjB;QACf,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,QAAQ;YACR;QACF;QAEA,IAAI;YACF,IAAI,CAAC,WAAW,CAAC,QAAQ,GAAG,CAAC;gBAC3B,IAAI,aAAa;gBACjB,IAAI,UAAU;gBAEd,IAAK,IAAI,IAAI,MAAM,WAAW,EAAE,IAAI,MAAM,OAAO,CAAC,MAAM,EAAE,IAAK;oBAC7D,MAAM,SAAS,MAAM,OAAO,CAAC,EAAE;oBAC/B,cAAc,MAAM,CAAC,EAAE,CAAC,UAAU;oBAClC,IAAI,OAAO,OAAO,EAAE;wBAClB,UAAU;oBACZ;gBACF;gBAEA,aAAa,YAAY;YAC3B;YAEA,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG,CAAC;gBAC1B,QAAQ,AAAC,6BAAwC,OAAZ,MAAM,KAAK;YAClD;YAEA,IAAI,CAAC,WAAW,CAAC,KAAK;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,AAAC,uCAA4C,OAAN;QACjD;IACF;IAEA,iBAAiB;IACjB,gBAAsB;QACpB,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,WAAW,CAAC,IAAI;QACvB;IACF;IAEA,2CAA2C;IAC3C,MAAM,gBAAgB,SAAe,EAAmB;QACtD,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ,WAAW;YACnC,SAAS,MAAM,CAAC,SAAS;YAEzB,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,AAAC,yBAA4C,OAApB,SAAS,UAAU;YAC9D;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,OAAO,OAAO,IAAI;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR;IACF;IAEA,sCAAsC;IACtC,MAAM,MACJ,IAAY,EACZ,OAAoB,EACpB,KAAkB,EAClB,WAA2C,EAC5B;QACf,IAAI;YACF,oBAAA,8BAAA;YAEA,uCAAuC;YACvC,MAAM,gBAAgB,IAAI,CAAC,qBAAqB,CAAC;YACjD,wBAAA,kCAAA,YAAc;YAEd,sBAAsB;YACtB,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAK;YAC9B;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,AAAC,mBAAsC,OAApB,SAAS,UAAU;YACxD;YAEA,+BAA+B;YAC/B,MAAM,YAAY,MAAM,SAAS,IAAI;YAErC,gCAAgC;YAChC,IAAI,CAAC,YAAY,GAAG,IAAI;YACxB,MAAM,WAAW,IAAI,eAAe,CAAC;YACrC,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG;YAExB,OAAO,IAAI,QAAQ,CAAC,SAAS;gBAC3B,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,OAAO,IAAI,MAAM;gBAEhD,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG;oBAC1B,IAAI,eAAe,CAAC;oBACpB,IAAI,CAAC,YAAY,GAAG;oBACpB,kBAAA,4BAAA;oBACA;gBACF;gBAEA,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG;oBAC1B,IAAI,eAAe,CAAC;oBACpB,IAAI,CAAC,YAAY,GAAG;oBACpB,OAAO,IAAI,MAAM;gBACnB;gBAEA,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,KAAK,CAAC;YACjC;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAC5B,uCAAuC;YACvC,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,SAAS,OAAO;QAClD;IACF;IAEA,uCAAuC;IACvC,MAAc,cACZ,IAAY,EACZ,OAAoB,EACpB,KAAkB,EAClB,WAA2C,EAC5B;QACf,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACnB,OAAO,IAAI,MAAM;gBACjB;YACF;YAEA,4BAA4B;YAC5B,IAAI,CAAC,SAAS,CAAC,MAAM;YAErB,IAAI,CAAC,gBAAgB,GAAG,IAAI,yBAAyB;YAErD,4DAA4D;YAC5D,IAAI,CAAC,gBAAgB,CAAC,IAAI,GAAG;YAC7B,IAAI,CAAC,gBAAgB,CAAC,KAAK,GAAG;YAC9B,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG;YAE/B,2BAA2B;YAC3B,MAAM,SAAS,IAAI,CAAC,SAAS,CAAC,SAAS;YACvC,MAAM,iBAAiB,OAAO,IAAI,CAAC,CAAA,QACjC,MAAM,IAAI,CAAC,QAAQ,CAAC,aAAa,MAAM,IAAI,CAAC,UAAU,CAAC,UACpD,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,IAAI,CAAC,UAAU,CAAC;YAEhD,IAAI,gBAAgB;gBAClB,IAAI,CAAC,gBAAgB,CAAC,KAAK,GAAG;YAChC;YAEA,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG;gBAC9B,oBAAA,8BAAA;gBACA,2BAA2B;gBAC3B,MAAM,gBAAgB,IAAI,CAAC,qBAAqB,CAAC;gBACjD,wBAAA,kCAAA,YAAc;YAChB;YAEA,IAAI,CAAC,gBAAgB,CAAC,KAAK,GAAG;gBAC5B,kBAAA,4BAAA;gBACA;YACF;YAEA,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG,CAAC;gBAC/B,OAAO,IAAI,MAAM,AAAC,2BAAsC,OAAZ,MAAM,KAAK;YACzD;YAEA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB;QAC5C;IACF;IAEA,sBAAsB;IACtB,eAAqB;QACnB,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,SAAS,CAAC,MAAM;QACvB;IACF;IAEA,gDAAgD;IACxC,sBAAsB,IAAY,EAAiB;QACzD,MAAM,QAAQ,KAAK,KAAK,CAAC;QACzB,MAAM,WAAsB,EAAE;QAC9B,IAAI,cAAc;QAClB,MAAM,sBAAsB,KAAK,mBAAmB;QAEpD,MAAM,OAAO,CAAC,CAAC,MAAM;YACnB,MAAM,eAAe,sBAAsB,CAAC,KAAK,MAAM,GAAG,CAAC,GAAG,8BAA8B;YAE5F,6EAA6E;YAC7E,MAAM,eAAe,IAAI,CAAC,iBAAiB,CAAC;YAC5C,MAAM,kBAAkB,eAAe,aAAa,MAAM;YAE1D,aAAa,OAAO,CAAC,CAAC,SAAS;gBAC7B,SAAS,IAAI,CAAC;oBACZ;oBACA,OAAO,cAAe,SAAS;oBAC/B,KAAK,cAAe,CAAC,SAAS,CAAC,IAAI;oBACnC,WAAW,IAAI,CAAC,mBAAmB,CAAC;gBACtC;YACF;YAEA,eAAe,eAAe,KAAK,4BAA4B;QACjE;QAEA,OAAO;YACL;YACA,UAAU;YACV,aAAa;QACf;IACF;IAEA,+CAA+C;IACvC,kBAAkB,IAAY,EAAY;QAChD,iFAAiF;QACjF,MAAM,SAAS;YAAC;YAAK;YAAK;YAAK;YAAK;SAAI;QACxC,MAAM,WAAqB,EAAE;QAE7B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YACpC,MAAM,OAAO,IAAI,CAAC,EAAE,CAAC,WAAW;YAChC,IAAI,OAAO,QAAQ,CAAC,OAAO;gBACzB,SAAS,IAAI,CAAC,SAAS,wBAAwB;YACjD,OAAO,IAAI,SAAS,OAAO,SAAS,OAAO,SAAS,KAAK;gBACvD,SAAS,IAAI,CAAC,WAAW,6BAA6B;YACxD,OAAO;gBACL,SAAS,IAAI,CAAC,QAAQ,oCAAoC;YAC5D;QACF;QAEA,OAAO,SAAS,MAAM,GAAG,IAAI,WAAW;YAAC;SAAM;IACjD;IAEA,oCAAoC;IAC5B,oBAAoB,OAAe,EAAU;QACnD,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,yCAAyC;IACzC,MAAM,iBAAgC;QACpC,IAAI;YACF,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBAAE,OAAO;YAAK;YACvE,IAAI,CAAC,aAAa,GAAG,IAAI,cAAc;YACvC,IAAI,CAAC,WAAW,GAAG,EAAE;YAErB,IAAI,CAAC,aAAa,CAAC,eAAe,GAAG,CAAC;gBACpC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,IAAI;YAClC;YAEA,IAAI,CAAC,aAAa,CAAC,KAAK;QAC1B,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM,AAAC,8BAAmC,OAAN;QAChD;IACF;IAEA,MAAM,gBAA+B;QACnC,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;gBACvB,OAAO,IAAI,MAAM;gBACjB;YACF;YAEA,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG;gBAC1B,MAAM,YAAY,IAAI,KAAK,IAAI,CAAC,WAAW,EAAE;oBAAE,MAAM;gBAAY;gBACjE,QAAQ;YACV;YAEA,IAAI,CAAC,aAAa,CAAC,IAAI;YAEvB,wCAAwC;YACxC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,GAAG,OAAO,CAAC,CAAA,QAAS,MAAM,IAAI;QACnE;IACF;IAEA,yCAAyC;IACzC,cAAuB;QACrB,OAAO,aAAkB,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,SAAS;IAC/E;IAnTA,aAAc;QAPd,+KAAQ,iBAAsC;QAC9C,+KAAQ,eAAsB,EAAE;QAChC,+KAAQ,eAAwC;QAChD,+KAAQ,aAAR,KAAA;QACA,+KAAQ,oBAAoD;QAC5D,+KAAQ,gBAAwC;QAG9C,yCAAyC;QACzC,wCAAmC;YACjC,IAAI,CAAC,SAAS,GAAG,OAAO,eAAe;YACvC,IAAI,CAAC,2BAA2B;QAClC;IACF;AA8SF;AAGO,MAAM,gBAAgB,uCAAgC,IAAI,kBAAkB", "debugId": null}}, {"offset": {"line": 949, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trainer%20test/ai-trainer-assessment/src/components/AssessmentInterface.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Mi<PERSON>, MicOff, Volume2, VolumeX } from 'lucide-react';\nimport Hologram from './Avatar';\nimport { speechService } from '@/services/speechService';\nimport { \n  AssessmentPhase, \n  ConversationMessage, \n  SpeechState, \n  AvatarState,\n  AIInsight \n} from '@/types';\n\ninterface AssessmentInterfaceProps {\n  userName?: string;\n  onPhaseChange?: (phase: AssessmentPhase) => void;\n  onInsights?: (insights: AIInsight[]) => void;\n}\n\nexport const AssessmentInterface: React.FC<AssessmentInterfaceProps> = ({\n  userName = 'there',\n  onPhaseChange,\n  onInsights\n}) => {\n  // State management\n  const [currentPhase, setCurrentPhase] = useState<AssessmentPhase>('welcome');\n  const [conversation, setConversation] = useState<ConversationMessage[]>([]);\n  const [speechState, setSpeechState] = useState<SpeechState>({\n    isListening: false,\n    isProcessing: false,\n    isSpeaking: false,\n    transcript: '',\n    confidence: 0\n  });\n  const [avatarState, setAvatarState] = useState<AvatarState>({\n    isAnimating: false,\n    currentAnimation: 'idle'\n  });\n  const [isAudioEnabled, setIsAudioEnabled] = useState(true);\n  const [currentTranscript, setCurrentTranscript] = useState('');\n\n  // Initialize with welcome message\n  useEffect(() => {\n    const welcomeMessage: ConversationMessage = {\n      id: '1',\n      role: 'trainer',\n      content: `Hey ${userName}! I'm Alex, your personal trainer. I'm so excited to work with you today! This session is all about YOU - where you're at right now and where you want to go. I'll be your coach, your cheerleader, and maybe a little bit of a drill sergeant when you need it. Sound good?`,\n      timestamp: new Date()\n    };\n\n    setConversation([welcomeMessage]);\n    \n    // Speak the welcome message\n    if (isAudioEnabled) {\n      speakMessage(welcomeMessage.content);\n    }\n  }, [userName, isAudioEnabled]);\n\n  // Handle speech synthesis\n  const speakMessage = useCallback(async (text: string) => {\n    if (!isAudioEnabled || !speechService) return;\n\n    setAvatarState(prev => ({ ...prev, currentAnimation: 'talking' }));\n    setSpeechState(prev => ({ ...prev, isSpeaking: true }));\n\n    try {\n      await speechService.speak(\n        text,\n        () => {\n          // On start\n          setAvatarState(prev => ({ ...prev, currentAnimation: 'talking' }));\n        },\n        () => {\n          // On end\n          setAvatarState(prev => ({ ...prev, currentAnimation: 'idle' }));\n          setSpeechState(prev => ({ ...prev, isSpeaking: false }));\n        },\n        (mouthSyncData) => {\n          // On mouth sync\n          setAvatarState(prev => ({ \n            ...prev, \n            mouthSyncData: { ...mouthSyncData, currentTime: Date.now() }\n          }));\n        }\n      );\n    } catch (error) {\n      console.error('Speech synthesis error:', error);\n      setAvatarState(prev => ({ ...prev, currentAnimation: 'idle' }));\n      setSpeechState(prev => ({ ...prev, isSpeaking: false }));\n    }\n  }, [isAudioEnabled]);\n\n  // Handle voice input\n  const startListening = useCallback(async () => {\n    if (speechState.isListening || speechState.isSpeaking || !speechService) return;\n\n    setAvatarState(prev => ({ ...prev, currentAnimation: 'listening' }));\n    setSpeechState(prev => ({ ...prev, isListening: true, transcript: '' }));\n    setCurrentTranscript('');\n\n    try {\n      await speechService.startListening(\n        (transcript, isFinal) => {\n          setCurrentTranscript(transcript);\n          setSpeechState(prev => ({ \n            ...prev, \n            transcript,\n            confidence: isFinal ? 1 : 0.5\n          }));\n\n          if (isFinal && transcript.trim()) {\n            handleUserMessage(transcript.trim());\n          }\n        },\n        (error) => {\n          console.error('Speech recognition error:', error);\n          stopListening();\n        }\n      );\n    } catch (error) {\n      console.error('Failed to start listening:', error);\n      stopListening();\n    }\n  }, [speechState.isListening, speechState.isSpeaking]);\n\n  const stopListening = useCallback(() => {\n    if (speechService) {\n      speechService.stopListening();\n    }\n    setAvatarState(prev => ({ ...prev, currentAnimation: 'idle' }));\n    setSpeechState(prev => ({\n      ...prev,\n      isListening: false,\n      isProcessing: false\n    }));\n  }, []);\n\n  // Handle user message\n  const handleUserMessage = useCallback(async (message: string) => {\n    if (!message.trim()) return;\n\n    stopListening();\n    setSpeechState(prev => ({ ...prev, isProcessing: true }));\n    setAvatarState(prev => ({ ...prev, currentAnimation: 'thinking' }));\n\n    // Add user message to conversation\n    const userMessage: ConversationMessage = {\n      id: Date.now().toString(),\n      role: 'user',\n      content: message,\n      timestamp: new Date()\n    };\n\n    setConversation(prev => [...prev, userMessage]);\n\n    try {\n      // Send to AI trainer\n      const response = await fetch('/api/trainer-chat', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          message,\n          phase: currentPhase,\n          conversationHistory: conversation,\n          userProfile: { name: userName }\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to get trainer response');\n      }\n\n      const data = await response.json();\n      \n      // Add trainer response to conversation\n      const trainerMessage: ConversationMessage = {\n        id: (Date.now() + 1).toString(),\n        role: 'trainer',\n        content: data.response,\n        timestamp: new Date()\n      };\n\n      setConversation(prev => [...prev, trainerMessage]);\n\n      // Handle insights\n      if (data.insights && onInsights) {\n        onInsights(data.insights);\n      }\n\n      // Speak the response\n      await speakMessage(data.response);\n\n      // Check for phase transitions (simplified logic)\n      checkPhaseTransition(data.response, data.insights);\n\n    } catch (error) {\n      console.error('Error getting trainer response:', error);\n      \n      const errorMessage: ConversationMessage = {\n        id: (Date.now() + 1).toString(),\n        role: 'trainer',\n        content: \"I'm sorry, I had a technical hiccup there. Could you repeat that?\",\n        timestamp: new Date()\n      };\n\n      setConversation(prev => [...prev, errorMessage]);\n      await speakMessage(errorMessage.content);\n    } finally {\n      setSpeechState(prev => ({ ...prev, isProcessing: false }));\n      setAvatarState(prev => ({ ...prev, currentAnimation: 'idle' }));\n    }\n  }, [conversation, currentPhase, userName, onInsights, speakMessage, stopListening]);\n\n  // Simple phase transition logic\n  const checkPhaseTransition = (response: string, insights: AIInsight[]) => {\n    // This is simplified - in a real app, you'd have more sophisticated logic\n    const responseWords = response.toLowerCase();\n    \n    if (currentPhase === 'welcome' && conversation.length > 4) {\n      setCurrentPhase('discovery');\n      onPhaseChange?.('discovery');\n    } else if (currentPhase === 'discovery' && conversation.length > 10) {\n      setCurrentPhase('physical_assessment');\n      onPhaseChange?.('physical_assessment');\n    }\n    // Add more phase transition logic as needed\n  };\n\n  // Toggle audio\n  const toggleAudio = () => {\n    setIsAudioEnabled(!isAudioEnabled);\n    if (speechState.isSpeaking && speechService) {\n      speechService.stopSpeaking();\n      setAvatarState(prev => ({ ...prev, currentAnimation: 'idle' }));\n      setSpeechState(prev => ({ ...prev, isSpeaking: false }));\n    }\n  };\n\n  return (\n    <div className=\"flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-4\">\n      {/* Hologram */}\n      <div className=\"mb-8\">\n        <Hologram state={avatarState} size=\"large\" />\n      </div>\n\n      {/* Conversation Display */}\n      <div className=\"w-full max-w-2xl mb-6\">\n        <div className=\"bg-black bg-opacity-50 rounded-lg shadow-lg border border-cyan-400 p-6 max-h-60 overflow-y-auto backdrop-blur-sm\">\n          <AnimatePresence>\n            {conversation.slice(-3).map((message) => (\n              <motion.div\n                key={message.id}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                className={`mb-4 ${\n                  message.role === 'trainer' ? 'text-left' : 'text-right'\n                }`}\n              >\n                <div\n                  className={`inline-block p-3 rounded-lg max-w-xs border ${\n                    message.role === 'trainer'\n                      ? 'bg-cyan-900 bg-opacity-50 text-cyan-100 border-cyan-400'\n                      : 'bg-purple-900 bg-opacity-50 text-purple-100 border-purple-400'\n                  }`}\n                >\n                  <p className=\"text-sm font-medium mb-1 font-mono\">\n                    {message.role === 'trainer' ? '> ALEX' : '> YOU'}\n                  </p>\n                  <p className=\"text-sm\">{message.content}</p>\n                </div>\n              </motion.div>\n            ))}\n          </AnimatePresence>\n        </div>\n      </div>\n\n      {/* Current Transcript */}\n      {currentTranscript && (\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          className=\"mb-4 p-3 bg-green-900 bg-opacity-50 rounded-lg border border-green-400 backdrop-blur-sm\"\n        >\n          <p className=\"text-sm text-green-300 font-mono\">\n            {`> RECEIVING: \"${currentTranscript}\"`}\n          </p>\n        </motion.div>\n      )}\n\n      {/* Controls */}\n      <div className=\"flex space-x-4\">\n        {/* Microphone Button */}\n        <motion.button\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n          onClick={speechState.isListening ? stopListening : startListening}\n          disabled={speechState.isSpeaking || speechState.isProcessing}\n          className={`p-4 rounded-full shadow-lg transition-colors border-2 ${\n            speechState.isListening\n              ? 'bg-red-500 text-white border-red-400 shadow-red-400/50'\n              : speechState.isSpeaking || speechState.isProcessing\n              ? 'bg-gray-600 text-gray-400 cursor-not-allowed border-gray-500'\n              : 'bg-cyan-500 text-white hover:bg-cyan-600 border-cyan-400 shadow-cyan-400/50'\n          }`}\n          style={{\n            boxShadow: speechState.isListening\n              ? '0 0 20px rgba(239, 68, 68, 0.5)'\n              : '0 0 20px rgba(34, 211, 238, 0.5)'\n          }}\n        >\n          {speechState.isListening ? <MicOff size={24} /> : <Mic size={24} />}\n        </motion.button>\n\n        {/* Audio Toggle */}\n        <motion.button\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n          onClick={toggleAudio}\n          className={`p-4 rounded-full shadow-lg transition-colors border-2 ${\n            isAudioEnabled\n              ? 'bg-green-500 text-white hover:bg-green-600 border-green-400 shadow-green-400/50'\n              : 'bg-gray-600 text-white hover:bg-gray-700 border-gray-500 shadow-gray-500/50'\n          }`}\n          style={{\n            boxShadow: isAudioEnabled\n              ? '0 0 20px rgba(34, 197, 94, 0.5)'\n              : '0 0 20px rgba(107, 114, 128, 0.5)'\n          }}\n        >\n          {isAudioEnabled ? <Volume2 size={24} /> : <VolumeX size={24} />}\n        </motion.button>\n      </div>\n\n      {/* Status */}\n      <div className=\"mt-4 text-center\">\n        <div className=\"bg-black bg-opacity-50 px-4 py-2 rounded-lg border border-cyan-400 backdrop-blur-sm\">\n          <p className=\"text-sm text-cyan-300 font-mono\">\n            {speechState.isSpeaking && '> ALEX TRANSMITTING...'}\n            {speechState.isListening && '> LISTENING FOR INPUT...'}\n            {speechState.isProcessing && '> PROCESSING MESSAGE...'}\n            {!speechState.isSpeaking && !speechState.isListening && !speechState.isProcessing &&\n              '> CLICK MICROPHONE TO RESPOND'}\n          </p>\n          <p className=\"text-xs text-purple-300 mt-1 font-mono\">\n            PHASE: {currentPhase.replace('_', ' ').toUpperCase()}\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AssessmentInterface;\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;;;AANA;;;;;;AAqBO,MAAM,sBAA0D;QAAC,EACtE,WAAW,OAAO,EAClB,aAAa,EACb,UAAU,EACX;;IACC,mBAAmB;IACnB,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAAkB;IAClE,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAAwB,EAAE;IAC1E,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAc;QAC1D,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,YAAY;IACd;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAc;QAC1D,aAAa;QACb,kBAAkB;IACpB;IACA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,yKAAQ,EAAC;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,IAAA,yKAAQ,EAAC;IAE3D,kCAAkC;IAClC,IAAA,0KAAS;yCAAC;YACR,MAAM,iBAAsC;gBAC1C,IAAI;gBACJ,MAAM;gBACN,SAAS,AAAC,OAAe,OAAT,UAAS;gBACzB,WAAW,IAAI;YACjB;YAEA,gBAAgB;gBAAC;aAAe;YAEhC,4BAA4B;YAC5B,IAAI,gBAAgB;gBAClB,aAAa,eAAe,OAAO;YACrC;QACF;wCAAG;QAAC;QAAU;KAAe;IAE7B,0BAA0B;IAC1B,MAAM,eAAe,IAAA,4KAAW;yDAAC,OAAO;YACtC,IAAI,CAAC,kBAAkB,CAAC,oJAAa,EAAE;YAEvC;iEAAe,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,kBAAkB;oBAAU,CAAC;;YAChE;iEAAe,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,YAAY;oBAAK,CAAC;;YAErD,IAAI;gBACF,MAAM,oJAAa,CAAC,KAAK,CACvB;qEACA;wBACE,WAAW;wBACX;6EAAe,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE,kBAAkB;gCAAU,CAAC;;oBAClE;;qEACA;wBACE,SAAS;wBACT;6EAAe,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE,kBAAkB;gCAAO,CAAC;;wBAC7D;6EAAe,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE,YAAY;gCAAM,CAAC;;oBACxD;;qEACA,CAAC;wBACC,gBAAgB;wBAChB;6EAAe,CAAA,OAAQ,CAAC;oCACtB,GAAG,IAAI;oCACP,eAAe;wCAAE,GAAG,aAAa;wCAAE,aAAa,KAAK,GAAG;oCAAG;gCAC7D,CAAC;;oBACH;;YAEJ,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;gBACzC;qEAAe,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,kBAAkB;wBAAO,CAAC;;gBAC7D;qEAAe,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,YAAY;wBAAM,CAAC;;YACxD;QACF;wDAAG;QAAC;KAAe;IAEnB,qBAAqB;IACrB,MAAM,iBAAiB,IAAA,4KAAW;2DAAC;YACjC,IAAI,YAAY,WAAW,IAAI,YAAY,UAAU,IAAI,CAAC,oJAAa,EAAE;YAEzE;mEAAe,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,kBAAkB;oBAAY,CAAC;;YAClE;mEAAe,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,aAAa;wBAAM,YAAY;oBAAG,CAAC;;YACtE,qBAAqB;YAErB,IAAI;gBACF,MAAM,oJAAa,CAAC,cAAc;uEAChC,CAAC,YAAY;wBACX,qBAAqB;wBACrB;+EAAe,CAAA,OAAQ,CAAC;oCACtB,GAAG,IAAI;oCACP;oCACA,YAAY,UAAU,IAAI;gCAC5B,CAAC;;wBAED,IAAI,WAAW,WAAW,IAAI,IAAI;4BAChC,kBAAkB,WAAW,IAAI;wBACnC;oBACF;;uEACA,CAAC;wBACC,QAAQ,KAAK,CAAC,6BAA6B;wBAC3C;oBACF;;YAEJ,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C;YACF;QACF;0DAAG;QAAC,YAAY,WAAW;QAAE,YAAY,UAAU;KAAC;IAEpD,MAAM,gBAAgB,IAAA,4KAAW;0DAAC;YAChC,IAAI,oJAAa,EAAE;gBACjB,oJAAa,CAAC,aAAa;YAC7B;YACA;kEAAe,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,kBAAkB;oBAAO,CAAC;;YAC7D;kEAAe,CAAA,OAAQ,CAAC;wBACtB,GAAG,IAAI;wBACP,aAAa;wBACb,cAAc;oBAChB,CAAC;;QACH;yDAAG,EAAE;IAEL,sBAAsB;IACtB,MAAM,oBAAoB,IAAA,4KAAW;8DAAC,OAAO;YAC3C,IAAI,CAAC,QAAQ,IAAI,IAAI;YAErB;YACA;sEAAe,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,cAAc;oBAAK,CAAC;;YACvD;sEAAe,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,kBAAkB;oBAAW,CAAC;;YAEjE,mCAAmC;YACnC,MAAM,cAAmC;gBACvC,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,MAAM;gBACN,SAAS;gBACT,WAAW,IAAI;YACjB;YAEA;sEAAgB,CAAA,OAAQ;2BAAI;wBAAM;qBAAY;;YAE9C,IAAI;gBACF,qBAAqB;gBACrB,MAAM,WAAW,MAAM,MAAM,qBAAqB;oBAChD,QAAQ;oBACR,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,MAAM,KAAK,SAAS,CAAC;wBACnB;wBACA,OAAO;wBACP,qBAAqB;wBACrB,aAAa;4BAAE,MAAM;wBAAS;oBAChC;gBACF;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,uCAAuC;gBACvC,MAAM,iBAAsC;oBAC1C,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;oBAC7B,MAAM;oBACN,SAAS,KAAK,QAAQ;oBACtB,WAAW,IAAI;gBACjB;gBAEA;0EAAgB,CAAA,OAAQ;+BAAI;4BAAM;yBAAe;;gBAEjD,kBAAkB;gBAClB,IAAI,KAAK,QAAQ,IAAI,YAAY;oBAC/B,WAAW,KAAK,QAAQ;gBAC1B;gBAEA,qBAAqB;gBACrB,MAAM,aAAa,KAAK,QAAQ;gBAEhC,iDAAiD;gBACjD,qBAAqB,KAAK,QAAQ,EAAE,KAAK,QAAQ;YAEnD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;gBAEjD,MAAM,eAAoC;oBACxC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;oBAC7B,MAAM;oBACN,SAAS;oBACT,WAAW,IAAI;gBACjB;gBAEA;0EAAgB,CAAA,OAAQ;+BAAI;4BAAM;yBAAa;;gBAC/C,MAAM,aAAa,aAAa,OAAO;YACzC,SAAU;gBACR;0EAAe,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,cAAc;wBAAM,CAAC;;gBACxD;0EAAe,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,kBAAkB;wBAAO,CAAC;;YAC/D;QACF;6DAAG;QAAC;QAAc;QAAc;QAAU;QAAY;QAAc;KAAc;IAElF,gCAAgC;IAChC,MAAM,uBAAuB,CAAC,UAAkB;QAC9C,0EAA0E;QAC1E,MAAM,gBAAgB,SAAS,WAAW;QAE1C,IAAI,iBAAiB,aAAa,aAAa,MAAM,GAAG,GAAG;YACzD,gBAAgB;YAChB,0BAAA,oCAAA,cAAgB;QAClB,OAAO,IAAI,iBAAiB,eAAe,aAAa,MAAM,GAAG,IAAI;YACnE,gBAAgB;YAChB,0BAAA,oCAAA,cAAgB;QAClB;IACA,4CAA4C;IAC9C;IAEA,eAAe;IACf,MAAM,cAAc;QAClB,kBAAkB,CAAC;QACnB,IAAI,YAAY,UAAU,IAAI,oJAAa,EAAE;YAC3C,oJAAa,CAAC,YAAY;YAC1B,eAAe,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,kBAAkB;gBAAO,CAAC;YAC7D,eAAe,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,YAAY;gBAAM,CAAC;QACxD;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,0IAAQ;oBAAC,OAAO;oBAAa,MAAK;;;;;;;;;;;0BAIrC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+MAAe;kCACb,aAAa,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,wBAC3B,6LAAC,uMAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,MAAM;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC3B,WAAW,AAAC,QAEX,OADC,QAAQ,IAAI,KAAK,YAAY,cAAc;0CAG7C,cAAA,6LAAC;oCACC,WAAW,AAAC,+CAIX,OAHC,QAAQ,IAAI,KAAK,YACb,4DACA;;sDAGN,6LAAC;4CAAE,WAAU;sDACV,QAAQ,IAAI,KAAK,YAAY,WAAW;;;;;;sDAE3C,6LAAC;4CAAE,WAAU;sDAAW,QAAQ,OAAO;;;;;;;;;;;;+BAlBpC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;YA2BxB,mCACC,6LAAC,uMAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,WAAU;0BAEV,cAAA,6LAAC;oBAAE,WAAU;8BACV,AAAC,iBAAkC,OAAlB,mBAAkB;;;;;;;;;;;0BAM1C,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,uMAAM,CAAC,MAAM;wBACZ,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;wBACxB,SAAS,YAAY,WAAW,GAAG,gBAAgB;wBACnD,UAAU,YAAY,UAAU,IAAI,YAAY,YAAY;wBAC5D,WAAW,AAAC,yDAMX,OALC,YAAY,WAAW,GACnB,2DACA,YAAY,UAAU,IAAI,YAAY,YAAY,GAClD,iEACA;wBAEN,OAAO;4BACL,WAAW,YAAY,WAAW,GAC9B,oCACA;wBACN;kCAEC,YAAY,WAAW,iBAAG,6LAAC,uNAAM;4BAAC,MAAM;;;;;qFAAS,6LAAC,0MAAG;4BAAC,MAAM;;;;;;;;;;;kCAI/D,6LAAC,uMAAM,CAAC,MAAM;wBACZ,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;wBACxB,SAAS;wBACT,WAAW,AAAC,yDAIX,OAHC,iBACI,oFACA;wBAEN,OAAO;4BACL,WAAW,iBACP,oCACA;wBACN;kCAEC,+BAAiB,6LAAC,0NAAO;4BAAC,MAAM;;;;;qFAAS,6LAAC,0NAAO;4BAAC,MAAM;;;;;;;;;;;;;;;;;0BAK7D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;;gCACV,YAAY,UAAU,IAAI;gCAC1B,YAAY,WAAW,IAAI;gCAC3B,YAAY,YAAY,IAAI;gCAC5B,CAAC,YAAY,UAAU,IAAI,CAAC,YAAY,WAAW,IAAI,CAAC,YAAY,YAAY,IAC/E;;;;;;;sCAEJ,6LAAC;4BAAE,WAAU;;gCAAyC;gCAC5C,aAAa,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;;;;;;;;;;;;;;;;;;;AAM9D;GA5Ua;KAAA;uCA8UE", "debugId": null}}, {"offset": {"line": 1526, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trainer%20test/ai-trainer-assessment/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport AssessmentInterface from '@/components/AssessmentInterface';\nimport { AssessmentPhase, AIInsight } from '@/types';\n\nexport default function Home() {\n  const [currentPhase, setCurrentPhase] = useState<AssessmentPhase>('welcome');\n  const [insights, setInsights] = useState<AIInsight[]>([]);\n  const [userName, setUserName] = useState('');\n  const [showAssessment, setShowAssessment] = useState(false);\n\n  const handleStartAssessment = () => {\n    if (userName.trim()) {\n      setShowAssessment(true);\n    }\n  };\n\n  const handlePhaseChange = (phase: AssessmentPhase) => {\n    setCurrentPhase(phase);\n  };\n\n  const handleInsights = (newInsights: AIInsight[]) => {\n    setInsights(prev => [...prev, ...newInsights]);\n  };\n\n  if (showAssessment) {\n    return (\n      <AssessmentInterface\n        userName={userName}\n        onPhaseChange={handlePhaseChange}\n        onInsights={handleInsights}\n      />\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center p-4\">\n      <div className=\"max-w-md w-full bg-black bg-opacity-50 rounded-lg shadow-xl border border-cyan-400 p-8 backdrop-blur-sm\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-cyan-300 mb-2 font-mono\">\n            AI HOLOGRAPHIC TRAINER\n          </h1>\n          <p className=\"text-cyan-100 text-sm\">\n            Experience the future of fitness with Alex, your AI holographic personal trainer\n          </p>\n        </div>\n\n        <div className=\"space-y-6\">\n          <div>\n            <label htmlFor=\"name\" className=\"block text-sm font-medium text-cyan-300 mb-2 font-mono\">\n              ENTER YOUR NAME:\n            </label>\n            <input\n              type=\"text\"\n              id=\"name\"\n              value={userName}\n              onChange={(e) => setUserName(e.target.value)}\n              placeholder=\"Your name here...\"\n              className=\"w-full px-3 py-2 bg-black bg-opacity-50 border border-cyan-400 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 text-cyan-100 placeholder-cyan-400 font-mono\"\n              onKeyPress={(e) => e.key === 'Enter' && handleStartAssessment()}\n            />\n          </div>\n\n          <button\n            onClick={handleStartAssessment}\n            disabled={!userName.trim()}\n            className=\"w-full bg-cyan-600 text-white py-3 px-4 rounded-md hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-offset-2 disabled:bg-gray-600 disabled:cursor-not-allowed transition-colors border border-cyan-400 font-mono\"\n            style={{\n              boxShadow: '0 0 20px rgba(34, 211, 238, 0.3)'\n            }}\n          >\n            INITIALIZE ASSESSMENT\n          </button>\n        </div>\n\n        <div className=\"mt-8 text-center text-sm text-cyan-400 space-y-2\">\n          <p className=\"font-mono\">🎤 MICROPHONE ACCESS REQUIRED</p>\n          <p className=\"font-mono\">🔊 AUDIO OUTPUT RECOMMENDED</p>\n          <p className=\"font-mono\">🤖 AI POWERED EXPERIENCE</p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAMe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAAkB;IAClE,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,yKAAQ,EAAc,EAAE;IACxD,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,yKAAQ,EAAC;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,yKAAQ,EAAC;IAErD,MAAM,wBAAwB;QAC5B,IAAI,SAAS,IAAI,IAAI;YACnB,kBAAkB;QACpB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;IAClB;IAEA,MAAM,iBAAiB,CAAC;QACtB,YAAY,CAAA,OAAQ;mBAAI;mBAAS;aAAY;IAC/C;IAEA,IAAI,gBAAgB;QAClB,qBACE,6LAAC,uJAAmB;YAClB,UAAU;YACV,eAAe;YACf,YAAY;;;;;;IAGlB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAkD;;;;;;sCAGhE,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAKvC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAO,WAAU;8CAAyD;;;;;;8CAGzF,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,OAAO;oCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC3C,aAAY;oCACZ,WAAU;oCACV,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;;;;;;;sCAI5C,6LAAC;4BACC,SAAS;4BACT,UAAU,CAAC,SAAS,IAAI;4BACxB,WAAU;4BACV,OAAO;gCACL,WAAW;4BACb;sCACD;;;;;;;;;;;;8BAKH,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAAY;;;;;;sCACzB,6LAAC;4BAAE,WAAU;sCAAY;;;;;;sCACzB,6LAAC;4BAAE,WAAU;sCAAY;;;;;;;;;;;;;;;;;;;;;;;AAKnC;GA9EwB;KAAA", "debugId": null}}]}