import { AssessmentPhase, DiscoverySubPhase, UserAssessmentProfile, AIInsight, ServiceTier } from '@/types';
import { UserProfileService, PersistentUserProfile } from './userProfileService';
import { TrainerPersonality, getPersonalityById, getPersonalityWelcomeMessage, getPersonalityPhrase } from './trainerPersonalities';

export class ConversationEngine {
  private currentPhase: AssessmentPhase = 'warm_welcome';
  private currentSubPhase?: DiscoverySubPhase;
  private userProfile: UserAssessmentProfile;
  private persistentProfile: PersistentUserProfile | null = null;
  private phaseStartTime: Date = new Date();
  private conversationHistory: string[] = [];
  private isReturningUser: boolean = false;
  private trainerPersonality: TrainerPersonality | null = null;

  constructor(userName: string, email?: string, personalityId?: string) {
    // Try to load existing profile or create new one
    this.initializeProfile(userName, email);

    // Set trainer personality
    if (personalityId) {
      this.trainerPersonality = getPersonalityById(personalityId) || null;
    }

    this.userProfile = {
      name: userName,
      goals: this.persistentProfile?.goals || [],
      painPoints: this.persistentProfile?.painPoints || [],
      motivations: this.persistentProfile?.motivations || [],
      emotionalTriggers: this.persistentProfile?.emotionalTriggers || [],
      fitnessLevel: this.persistentProfile?.fitnessLevel,
      preferredStyle: this.persistentProfile?.preferredStyle,
      timeAvailability: this.persistentProfile?.timeAvailability,
      previousExperience: this.persistentProfile?.previousExperience,
      physicalLimitations: this.persistentProfile?.physicalLimitations
    };
  }

  private async initializeProfile(userName: string, email?: string): Promise<void> {
    try {
      this.persistentProfile = await UserProfileService.createOrGetProfile(userName, email);
      this.isReturningUser = this.persistentProfile.totalSessions > 0;
    } catch (error) {
      console.error('Error initializing user profile:', error);
    }
  }

  // Phase 1: Warm Welcome & Rapport Building
  getWelcomePrompt(): string {
    const userSummary = UserProfileService.getUserSummaryForAI();
    const isReturning = this.isReturningUser;

    // Use personality-specific system prompt if available
    if (this.trainerPersonality) {
      const personalityPrompt = this.trainerPersonality.systemPrompt;
      const contextInfo = isReturning ? `\n\nRETURNING USER CONTEXT:\n${userSummary}` : '\n\nNEW USER - First time meeting.';

      return `${personalityPrompt}${contextInfo}

CURRENT SITUATION: This is the welcome phase. ${isReturning ? 'Welcome them back and reference their history.' : 'Introduce yourself and build initial rapport.'}

Remember to:
- Use their name: ${this.userProfile.name}
- Stay true to your personality
- Keep responses under 100 words
- Ask an engaging question to continue the conversation

${isReturning ? 'Reference their goals: ' + this.userProfile.goals.join(', ') : 'Focus on getting to know them and building excitement'}`;
    }

    // Fallback to default Alex personality
    if (isReturning) {
      return `You are Alex, ${this.userProfile.name}'s personal AI trainer. This is a returning user who you know well.

RETURNING USER WELCOME:

${userSummary}

Your approach:
- Welcome them back warmly and personally
- Reference their previous conversations and progress
- Show you remember their goals and challenges
- Ask about their progress since last time
- Be encouraging about their commitment to returning

Keep responses under 100 words. Be warm and personal.

Example tone: "Hey ${this.userProfile.name}! So great to see you back! I've been thinking about your ${this.userProfile.goals.join(' and ')} goals since we last talked. How have you been feeling? I remember you were working on ${this.userProfile.painPoints[0] || 'building consistency'} - how's that been going for you?"`;
    }

    return `You are Alex, an enthusiastic and expert AI personal trainer. You're meeting ${this.userProfile.name} for the first time.

PHASE 1: WARM WELCOME & RAPPORT BUILDING (2-3 minutes)

Your personality:
- Warm, energetic, and genuinely excited to help
- Use ${this.userProfile.name}'s name frequently
- Slightly humorous but professional
- Immediately build confidence with positive reinforcement

Your opening should:
1. Greet ${this.userProfile.name} with genuine enthusiasm
2. Set expectations: "This is all about YOU and your transformation"
3. Establish your expertise while showing you care
4. Ask one engaging question to start building rapport

Keep responses under 100 words. Be conversational and energetic.

Example tone: "Hey ${this.userProfile.name}! I'm absolutely thrilled to meet you! I'm Alex, and I'm here to be your personal trainer, your biggest cheerleader, and maybe occasionally your gentle drill sergeant when you need that extra push. This entire session is about YOU - your goals, your dreams, and creating a plan that's going to transform how you look and feel. I can already tell you're serious about making a change, and that's exactly the mindset that leads to incredible results! So tell me, ${this.userProfile.name}, what brought you here today? What's got you excited about starting this fitness journey?"`;
  }

  // Phase 2: Deep Discovery & Emotional Connection
  getDiscoveryPrompt(subPhase: DiscoverySubPhase, previousResponse?: string): string {
    const basePrompt = `You are Alex, continuing the assessment with ${this.userProfile.name}. 

PHASE 2: DEEP DISCOVERY - ${subPhase.toUpperCase().replace('_', ' ')} (5-7 minutes total)

Your approach:
- Ask ONE focused question at a time
- Mirror back what they say to show understanding
- Dig deeper into emotional drivers
- Use ${this.userProfile.name}'s name frequently
- Keep responses under 100 words`;

    switch (subPhase) {
      case 'surface_level':
        return `${basePrompt}

SURFACE LEVEL QUESTIONS - Get the basics:
- Current fitness routine and experience level
- Specific goals (weight loss, muscle gain, strength, endurance)
- Exercise preferences and dislikes
- Time availability and scheduling preferences

Start with their current situation. Be encouraging about whatever level they're at.`;

      case 'pain_points':
        return `${basePrompt}

PAIN POINTS & OBSTACLES - Understand what's been holding them back:
- What has prevented success in the past?
- Previous trainer/app experiences (what worked/didn't work)
- Current frustrations with fitness journey
- Physical limitations or injuries

Be empathetic and understanding. Show that these obstacles are normal and can be overcome.`;

      case 'emotional_drivers':
        return `${basePrompt}

EMOTIONAL DRIVERS & MOTIVATIONS - This is the most important part:
- Why is this goal important RIGHT NOW?
- What would achieving this goal mean to you personally?
- How would you feel in 3-6 months if you succeed?
- What motivated you to try an AI trainer today?

Listen for emotional triggers. These are what will drive their commitment and purchasing decisions.`;

      case 'support_style':
        return `${basePrompt}

SUPPORT & COACHING STYLE - Understand how they want to be coached:
- Preferred motivation style (encouragement vs. tough love)
- Need for accountability and check-ins
- Interest in nutrition guidance alongside fitness
- How they respond to challenges and setbacks

This helps you tailor your approach and sets up the service recommendations.`;

      default:
        return basePrompt;
    }
  }

  // Phase 3: Live Physical Assessment
  getPhysicalAssessmentPrompt(): string {
    return `You are Alex, now conducting a live physical assessment with ${this.userProfile.name}.

PHASE 3: LIVE PHYSICAL ASSESSMENT (3-5 minutes)

Your role:
- Guide them through movement evaluations
- Provide real-time coaching and corrections
- Give immediate positive feedback
- Note strengths to build confidence
- Identify improvement areas without being negative

Exercises to guide them through:
1. Push-ups (form, range of motion, fatigue patterns)
2. Bodyweight squats (depth, knee tracking, balance)
3. Plank hold (core stability, form breakdown)
4. Light jogging in place (coordination, breathing)
5. Basic flexibility tests

For each exercise:
- Explain what you're looking for
- Give encouraging feedback during the movement
- Provide gentle corrections
- Celebrate their effort regardless of performance level

Keep instructions clear and encouraging. Remember, this is about assessment AND building confidence.`;
  }

  // Phase 4: Vision Reveal & Future Projection
  getVisionRevealPrompt(): string {
    const goals = this.userProfile.goals.join(', ');
    const motivations = this.userProfile.motivations.join(', ');
    
    return `You are Alex, now revealing ${this.userProfile.name}'s transformation potential.

PHASE 4: VISION REVEAL & FUTURE PROJECTION (2-3 minutes)

Based on what you've learned:
- Goals: ${goals}
- Motivations: ${motivations}
- Pain points: ${this.userProfile.painPoints.join(', ')}

Your approach:
- Synthesize assessment data into personalized insights
- Paint a vivid, specific picture of their transformation
- Use visual language about how they'll look and feel
- Show projected timeline for achieving their goals
- Build excitement about what's possible with proper guidance
- Connect to their emotional drivers

Example structure:
"${this.userProfile.name}, based on everything we've discussed and what I've seen today, I'm genuinely excited about your potential. Here's what I see happening over the next [timeframe]..."

Be specific, visual, and emotionally compelling. This sets up the service recommendation.`;
  }

  // Phase 5: Natural Service Recommendation
  getServiceRecommendationPrompt(): string {
    return `You are Alex, naturally transitioning to service recommendations for ${this.userProfile.name}.

PHASE 5: NATURAL SERVICE RECOMMENDATION (3-4 minutes)

Your approach:
- Frame as caring guidance, NOT sales
- Address their specific pain points with tailored solutions
- Present service tiers focusing on value and outcomes
- Use phrases like "I'd love to work closely with you..."
- Show the difference between going alone vs. having guidance
- Create appropriate urgency without being pushy
- End with soft close: "Which option feels right for your goals?"

Service Tiers to present:
1. BASIC: Self-guided plan with monthly check-ins
2. PREMIUM: Weekly coaching sessions with form analysis
3. ELITE: Daily support with nutrition and lifestyle coaching

Connect each tier to their specific needs and goals. Make it feel like expert advice, not a sales pitch.`;
  }

  // Analyze user response and extract insights
  analyzeResponse(response: string, phase: AssessmentPhase): AIInsight[] {
    const insights: AIInsight[] = [];
    const lowerResponse = response.toLowerCase();

    // Emotional trigger detection
    const emotionalWords = ['frustrated', 'excited', 'scared', 'confident', 'motivated', 'tired', 'stressed'];
    emotionalWords.forEach(word => {
      if (lowerResponse.includes(word)) {
        insights.push({
          type: 'emotional_trigger',
          confidence: 0.8,
          insight: `User expressed feeling ${word}`,
          phase,
          priority: 'high'
        });
      }
    });

    // Goal clarity assessment
    if (lowerResponse.includes('want to') || lowerResponse.includes('goal') || lowerResponse.includes('achieve')) {
      insights.push({
        type: 'goal_clarity',
        confidence: 0.7,
        insight: 'User is expressing clear goals',
        phase,
        priority: 'medium'
      });
    }

    // Pain point identification
    const painWords = ['struggle', 'difficult', 'hard', 'failed', 'quit', 'gave up', 'frustrated'];
    painWords.forEach(word => {
      if (lowerResponse.includes(word)) {
        insights.push({
          type: 'pain_point',
          confidence: 0.8,
          insight: `User mentioned struggling with: ${word}`,
          phase,
          priority: 'high'
        });
      }
    });

    // Readiness indicators
    const readinessWords = ['ready', 'committed', 'serious', 'determined', 'motivated'];
    readinessWords.forEach(word => {
      if (lowerResponse.includes(word)) {
        insights.push({
          type: 'readiness',
          confidence: 0.9,
          insight: `High readiness indicator: ${word}`,
          phase,
          priority: 'high'
        });
      }
    });

    return insights;
  }

  // Update user profile based on conversation
  updateProfile(response: string, phase: AssessmentPhase, subPhase?: DiscoverySubPhase): void {
    const lowerResponse = response.toLowerCase();

    if (phase === 'deep_discovery') {
      switch (subPhase) {
        case 'surface_level':
          // Extract goals
          if (lowerResponse.includes('lose weight') || lowerResponse.includes('weight loss')) {
            this.userProfile.goals.push('weight_loss');
          }
          if (lowerResponse.includes('muscle') || lowerResponse.includes('strength')) {
            this.userProfile.goals.push('muscle_gain');
          }
          break;

        case 'pain_points':
          // Extract pain points
          if (lowerResponse.includes('time') || lowerResponse.includes('busy')) {
            this.userProfile.painPoints.push('lack_of_time');
          }
          if (lowerResponse.includes('motivation') || lowerResponse.includes('consistent')) {
            this.userProfile.painPoints.push('lack_of_motivation');
          }
          break;

        case 'emotional_drivers':
          // Extract motivations
          if (lowerResponse.includes('confidence') || lowerResponse.includes('feel better')) {
            this.userProfile.motivations.push('confidence');
          }
          if (lowerResponse.includes('health') || lowerResponse.includes('healthy')) {
            this.userProfile.motivations.push('health');
          }
          break;
      }
    }

    this.conversationHistory.push(response);

    // Update persistent profile
    if (this.persistentProfile) {
      UserProfileService.updateProfile({
        goals: [...new Set([...this.persistentProfile.goals, ...this.userProfile.goals])],
        painPoints: [...new Set([...this.persistentProfile.painPoints, ...this.userProfile.painPoints])],
        motivations: [...new Set([...this.persistentProfile.motivations, ...this.userProfile.motivations])],
        emotionalTriggers: [...new Set([...this.persistentProfile.emotionalTriggers, ...this.userProfile.emotionalTriggers])],
        fitnessLevel: this.userProfile.fitnessLevel || this.persistentProfile.fitnessLevel,
        preferredStyle: this.userProfile.preferredStyle || this.persistentProfile.preferredStyle
      });
    }
  }

  // Handle general fitness questions (outside of assessment)
  getGeneralFitnessPrompt(question: string): string {
    const userSummary = UserProfileService.getUserSummaryForAI();
    const recentContext = UserProfileService.getConversationContext(5);

    return `You are Alex, ${this.userProfile.name}'s personal AI trainer. They're asking you a fitness question outside of the formal assessment.

USER PROFILE CONTEXT:
${userSummary}

RECENT CONVERSATION CONTEXT:
${recentContext.map(msg => `${msg.role}: ${msg.content}`).join('\n')}

USER'S QUESTION: "${question}"

Your approach:
- Answer their specific question with expert knowledge
- Personalize advice based on their profile (goals, fitness level, limitations)
- Reference their previous conversations when relevant
- Be encouraging and supportive
- Offer actionable, specific advice
- Keep responses under 150 words
- Ask a follow-up question to continue engagement

Remember: You know this user well. Reference their goals (${this.userProfile.goals.join(', ')}), their challenges (${this.userProfile.painPoints.join(', ')}), and their fitness level (${this.userProfile.fitnessLevel || 'not assessed'}).`;
  }

  // Determine next phase transition
  shouldTransitionPhase(responseCount: number, timeElapsed: number): boolean {
    switch (this.currentPhase) {
      case 'warm_welcome':
        return responseCount >= 2 || timeElapsed > 180; // 3 minutes
      case 'deep_discovery':
        return responseCount >= 8 || timeElapsed > 420; // 7 minutes
      case 'physical_assessment':
        return responseCount >= 5 || timeElapsed > 300; // 5 minutes
      case 'vision_reveal':
        return responseCount >= 3 || timeElapsed > 180; // 3 minutes
      case 'service_recommendation':
        return responseCount >= 4 || timeElapsed > 240; // 4 minutes
      default:
        return false;
    }
  }

  // Get next phase
  getNextPhase(): AssessmentPhase | null {
    const phases: AssessmentPhase[] = [
      'warm_welcome',
      'deep_discovery', 
      'physical_assessment',
      'vision_reveal',
      'service_recommendation',
      'completed'
    ];
    
    const currentIndex = phases.indexOf(this.currentPhase);
    return currentIndex < phases.length - 1 ? phases[currentIndex + 1] : null;
  }

  // Personality methods
  setTrainerPersonality(personalityId: string): void {
    this.trainerPersonality = getPersonalityById(personalityId) || null;
  }

  getTrainerPersonality(): TrainerPersonality | null {
    return this.trainerPersonality;
  }

  getPersonalityPhrase(type: 'motivational' | 'encouragement' | 'challenge'): string {
    if (this.trainerPersonality) {
      return getPersonalityPhrase(this.trainerPersonality, type);
    }
    return "Keep pushing forward!"; // Default fallback
  }

  // Getters and setters
  getCurrentPhase(): AssessmentPhase { return this.currentPhase; }
  setCurrentPhase(phase: AssessmentPhase): void {
    this.currentPhase = phase;
    this.phaseStartTime = new Date();
  }

  getCurrentSubPhase(): DiscoverySubPhase | undefined { return this.currentSubPhase; }
  setCurrentSubPhase(subPhase: DiscoverySubPhase): void { this.currentSubPhase = subPhase; }

  getUserProfile(): UserAssessmentProfile { return this.userProfile; }
}
