module.exports = [
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[project]/src/components/Avatar.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "Hologram",
    ()=>Hologram,
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs [app-ssr] (ecmascript)");
'use client';
;
;
;
const Hologram = ({ state, className = '', size = 'medium' })=>{
    const [currentMouthShape, setCurrentMouthShape] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('closed');
    const animationRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])();
    // Size configurations
    const sizeConfig = {
        small: {
            width: 120,
            height: 120,
            scale: 0.8
        },
        medium: {
            width: 200,
            height: 200,
            scale: 1
        },
        large: {
            width: 300,
            height: 300,
            scale: 1.2
        }
    };
    const config = sizeConfig[size];
    // Animate mouth based on speech data
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (state.mouthSyncData && state.currentAnimation === 'talking') {
            const animateMouth = ()=>{
                const currentTime = Date.now() - (state.mouthSyncData?.currentTime || 0);
                const currentPhoneme = state.mouthSyncData?.phonemes.find((p)=>currentTime >= p.start * 1000 && currentTime <= p.end * 1000);
                if (currentPhoneme) {
                    setCurrentMouthShape(currentPhoneme.phoneme);
                } else {
                    setCurrentMouthShape('closed');
                }
                if (currentTime < (state.mouthSyncData?.duration || 0) * 1000) {
                    animationRef.current = requestAnimationFrame(animateMouth);
                }
            };
            animationRef.current = requestAnimationFrame(animateMouth);
        } else {
            setCurrentMouthShape('closed');
        }
        return ()=>{
            if (animationRef.current) {
                cancelAnimationFrame(animationRef.current);
            }
        };
    }, [
        state.mouthSyncData,
        state.currentAnimation
    ]);
    // Animation variants for the orb
    const containerVariants = {
        idle: {
            y: [
                0,
                -10,
                0
            ],
            rotate: [
                0,
                2,
                -2,
                0
            ],
            transition: {
                duration: 4,
                repeat: Infinity,
                ease: 'easeInOut'
            }
        },
        talking: {
            y: [
                0,
                -5,
                0
            ],
            scale: [
                1,
                1.03,
                1
            ],
            transition: {
                duration: 0.6,
                repeat: Infinity,
                ease: 'easeInOut'
            }
        },
        listening: {
            y: [
                0,
                -8,
                0
            ],
            rotate: [
                0,
                3,
                -3,
                0
            ],
            transition: {
                duration: 2.5,
                repeat: Infinity,
                ease: 'easeInOut'
            }
        },
        thinking: {
            y: [
                0,
                -12,
                0
            ],
            rotate: [
                0,
                10,
                -10,
                0
            ],
            transition: {
                duration: 3,
                repeat: Infinity,
                ease: 'easeInOut'
            }
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `flex items-center justify-center ${className}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                className: "relative",
                style: {
                    width: config.width,
                    height: config.height
                },
                variants: containerVariants,
                animate: state.currentAnimation,
                initial: "idle",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                        className: "absolute inset-0 rounded-full",
                        style: {
                            background: 'radial-gradient(circle at 30% 30%, rgba(34, 211, 238, 0.4), rgba(59, 130, 246, 0.3), rgba(147, 51, 234, 0.2), transparent)',
                            backdropFilter: 'blur(2px)',
                            border: '1px solid rgba(34, 211, 238, 0.3)',
                            boxShadow: '0 0 60px rgba(34, 211, 238, 0.4), inset 0 0 60px rgba(59, 130, 246, 0.2)'
                        },
                        animate: {
                            opacity: [
                                0.6,
                                0.9,
                                0.6
                            ],
                            scale: state.currentAnimation === 'talking' ? [
                                1,
                                1.05,
                                1
                            ] : [
                                1,
                                1.02,
                                1
                            ]
                        },
                        transition: {
                            duration: state.currentAnimation === 'talking' ? 0.5 : 2,
                            repeat: Infinity,
                            ease: 'easeInOut'
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/Avatar.tsx",
                        lineNumber: 96,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                        className: "absolute inset-8 rounded-full",
                        style: {
                            background: 'radial-gradient(circle, rgba(34, 211, 238, 0.6), rgba(59, 130, 246, 0.4), transparent)',
                            filter: 'blur(1px)'
                        },
                        animate: {
                            opacity: [
                                0.4,
                                0.8,
                                0.4
                            ],
                            scale: [
                                0.8,
                                1.2,
                                0.8
                            ],
                            rotate: [
                                0,
                                360
                            ]
                        },
                        transition: {
                            duration: 4,
                            repeat: Infinity,
                            ease: 'linear'
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/Avatar.tsx",
                        lineNumber: 118,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    [
                        ...Array(6)
                    ].map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                            className: "absolute w-2 h-2 bg-cyan-400 rounded-full",
                            style: {
                                left: `${20 + i * 10}%`,
                                top: `${30 + i * 8}%`,
                                boxShadow: '0 0 10px rgba(34, 211, 238, 0.8)'
                            },
                            animate: {
                                y: [
                                    -10,
                                    10,
                                    -10
                                ],
                                x: [
                                    -5,
                                    5,
                                    -5
                                ],
                                opacity: [
                                    0.3,
                                    1,
                                    0.3
                                ],
                                scale: [
                                    0.5,
                                    1,
                                    0.5
                                ]
                            },
                            transition: {
                                duration: 3 + i * 0.5,
                                repeat: Infinity,
                                ease: 'easeInOut',
                                delay: i * 0.3
                            }
                        }, i, false, {
                            fileName: "[project]/src/components/Avatar.tsx",
                            lineNumber: 138,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                        className: "absolute inset-0 overflow-hidden rounded-full",
                        style: {
                            background: 'repeating-linear-gradient(0deg, transparent, transparent 3px, rgba(34, 211, 238, 0.1) 3px, rgba(34, 211, 238, 0.1) 6px)'
                        },
                        animate: {
                            y: [
                                -30,
                                30,
                                -30
                            ]
                        },
                        transition: {
                            duration: 4,
                            repeat: Infinity,
                            ease: 'linear'
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/Avatar.tsx",
                        lineNumber: 162,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                        className: "absolute -inset-4 rounded-full border border-cyan-400 opacity-30",
                        animate: {
                            scale: [
                                1,
                                1.1,
                                1
                            ],
                            opacity: [
                                0.2,
                                0.5,
                                0.2
                            ],
                            rotate: [
                                0,
                                360
                            ]
                        },
                        transition: {
                            duration: 6,
                            repeat: Infinity,
                            ease: 'linear'
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/Avatar.tsx",
                        lineNumber: 178,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AnimatePresence"], {
                        children: state.currentAnimation === 'talking' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                            children: [
                                [
                                    ...Array(4)
                                ].map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                                        className: "absolute rounded-full border border-cyan-400",
                                        style: {
                                            inset: `${-8 - i * 8}px`,
                                            boxShadow: '0 0 30px rgba(34, 211, 238, 0.4)'
                                        },
                                        initial: {
                                            opacity: 0,
                                            scale: 0.8
                                        },
                                        animate: {
                                            opacity: [
                                                0,
                                                0.8,
                                                0
                                            ],
                                            scale: [
                                                0.8,
                                                1.4,
                                                1.8
                                            ]
                                        },
                                        exit: {
                                            opacity: 0
                                        },
                                        transition: {
                                            duration: 1.2,
                                            repeat: Infinity,
                                            delay: i * 0.15,
                                            ease: 'easeOut'
                                        }
                                    }, i, false, {
                                        fileName: "[project]/src/components/Avatar.tsx",
                                        lineNumber: 197,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0))),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                                    className: "absolute inset-4 rounded-full bg-cyan-400",
                                    style: {
                                        filter: 'blur(2px)',
                                        opacity: 0.3
                                    },
                                    animate: {
                                        scale: [
                                            0.5,
                                            1.5,
                                            0.5
                                        ],
                                        opacity: [
                                            0.1,
                                            0.4,
                                            0.1
                                        ]
                                    },
                                    transition: {
                                        duration: 0.8,
                                        repeat: Infinity,
                                        ease: 'easeInOut'
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/Avatar.tsx",
                                    lineNumber: 219,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true)
                    }, void 0, false, {
                        fileName: "[project]/src/components/Avatar.tsx",
                        lineNumber: 193,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AnimatePresence"], {
                        children: state.currentAnimation === 'listening' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                            children: [
                                [
                                    ...Array(5)
                                ].map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                                        className: "absolute rounded-full border border-green-400",
                                        style: {
                                            inset: `${-12 - i * 6}px`,
                                            boxShadow: '0 0 20px rgba(34, 197, 94, 0.3)'
                                        },
                                        initial: {
                                            opacity: 0,
                                            scale: 0.9
                                        },
                                        animate: {
                                            opacity: [
                                                0,
                                                0.6,
                                                0
                                            ],
                                            scale: [
                                                0.9,
                                                1.6,
                                                2.2
                                            ]
                                        },
                                        exit: {
                                            opacity: 0
                                        },
                                        transition: {
                                            duration: 3,
                                            repeat: Infinity,
                                            delay: i * 0.4,
                                            ease: 'easeOut'
                                        }
                                    }, i, false, {
                                        fileName: "[project]/src/components/Avatar.tsx",
                                        lineNumber: 244,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0))),
                                [
                                    ...Array(3)
                                ].map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                                        className: "absolute w-3 h-3 bg-green-400 rounded-full",
                                        style: {
                                            left: `${40 + i * 8}%`,
                                            top: '45%',
                                            boxShadow: '0 0 15px rgba(34, 197, 94, 0.8)'
                                        },
                                        animate: {
                                            opacity: [
                                                0.3,
                                                1,
                                                0.3
                                            ],
                                            scale: [
                                                0.8,
                                                1.2,
                                                0.8
                                            ]
                                        },
                                        transition: {
                                            duration: 1.5,
                                            repeat: Infinity,
                                            delay: i * 0.2,
                                            ease: 'easeInOut'
                                        }
                                    }, `dot-${i}`, false, {
                                        fileName: "[project]/src/components/Avatar.tsx",
                                        lineNumber: 267,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0)))
                            ]
                        }, void 0, true)
                    }, void 0, false, {
                        fileName: "[project]/src/components/Avatar.tsx",
                        lineNumber: 240,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AnimatePresence"], {
                        children: state.currentAnimation === 'thinking' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                            children: [
                                [
                                    ...Array(8)
                                ].map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                                        className: "absolute w-2 h-2 bg-purple-400 rounded-full",
                                        style: {
                                            boxShadow: '0 0 12px rgba(147, 51, 234, 0.8)'
                                        },
                                        animate: {
                                            rotate: [
                                                0,
                                                360
                                            ],
                                            scale: [
                                                0.5,
                                                1.2,
                                                0.5
                                            ],
                                            opacity: [
                                                0.4,
                                                1,
                                                0.4
                                            ]
                                        },
                                        transition: {
                                            duration: 4,
                                            repeat: Infinity,
                                            delay: i * 0.2,
                                            ease: 'linear'
                                        },
                                        initial: {
                                            left: '50%',
                                            top: '50%',
                                            x: `${Math.cos(i * 45 * Math.PI / 180) * 60}px`,
                                            y: `${Math.sin(i * 45 * Math.PI / 180) * 60}px`
                                        }
                                    }, i, false, {
                                        fileName: "[project]/src/components/Avatar.tsx",
                                        lineNumber: 297,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0))),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                                    className: "absolute inset-6 rounded-full bg-purple-400",
                                    style: {
                                        filter: 'blur(3px)',
                                        opacity: 0.2
                                    },
                                    animate: {
                                        scale: [
                                            0.8,
                                            1.3,
                                            0.8
                                        ],
                                        opacity: [
                                            0.1,
                                            0.3,
                                            0.1
                                        ],
                                        rotate: [
                                            0,
                                            180,
                                            360
                                        ]
                                    },
                                    transition: {
                                        duration: 2,
                                        repeat: Infinity,
                                        ease: 'easeInOut'
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/Avatar.tsx",
                                    lineNumber: 323,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true)
                    }, void 0, false, {
                        fileName: "[project]/src/components/Avatar.tsx",
                        lineNumber: 292,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                        className: "absolute inset-0 rounded-full",
                        style: {
                            background: 'linear-gradient(90deg, transparent 0%, rgba(34, 211, 238, 0.05) 50%, transparent 100%)'
                        },
                        animate: {
                            x: [
                                -150,
                                150,
                                -150
                            ],
                            opacity: [
                                0,
                                0.2,
                                0
                            ]
                        },
                        transition: {
                            duration: 6,
                            repeat: Infinity,
                            ease: 'linear'
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/Avatar.tsx",
                        lineNumber: 345,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/Avatar.tsx",
                lineNumber: 88,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                className: "absolute -bottom-16 text-center",
                initial: {
                    opacity: 0,
                    y: 10
                },
                animate: {
                    opacity: 1,
                    y: 0
                },
                transition: {
                    delay: 0.5
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-black bg-opacity-60 px-6 py-3 rounded-lg border border-cyan-400 backdrop-blur-sm",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].p, {
                        className: "text-sm text-cyan-300 font-mono",
                        animate: {
                            textShadow: state.currentAnimation === 'talking' ? [
                                '0 0 5px rgba(34, 211, 238, 0.8)',
                                '0 0 15px rgba(34, 211, 238, 1)',
                                '0 0 5px rgba(34, 211, 238, 0.8)'
                            ] : '0 0 5px rgba(34, 211, 238, 0.6)'
                        },
                        transition: {
                            duration: 0.8,
                            repeat: Infinity
                        },
                        children: [
                            state.currentAnimation === 'idle' && '◦ ALEX ONLINE ◦',
                            state.currentAnimation === 'talking' && '◦ TRANSMITTING ◦',
                            state.currentAnimation === 'listening' && '◦ RECEIVING ◦',
                            state.currentAnimation === 'thinking' && '◦ PROCESSING ◦'
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/Avatar.tsx",
                        lineNumber: 370,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                }, void 0, false, {
                    fileName: "[project]/src/components/Avatar.tsx",
                    lineNumber: 369,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/Avatar.tsx",
                lineNumber: 363,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/Avatar.tsx",
        lineNumber: 87,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = Hologram;
}),
"[project]/src/services/speechService.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "SpeechService",
    ()=>SpeechService,
    "speechService",
    ()=>speechService
]);
class SpeechService {
    mediaRecorder = null;
    audioChunks = [];
    recognition = null;
    synthesis;
    currentUtterance = null;
    currentAudio = null;
    constructor(){
        // Only initialize in browser environment
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
    }
    initializeSpeechRecognition() {
        if ("undefined" !== 'undefined' && ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) //TURBOPACK unreachable
        ;
    }
    // Start listening for speech
    async startListening(onTranscript, onError) {
        if (!this.recognition) {
            onError('Speech recognition not supported');
            return;
        }
        try {
            this.recognition.onresult = (event)=>{
                let transcript = '';
                let isFinal = false;
                for(let i = event.resultIndex; i < event.results.length; i++){
                    const result = event.results[i];
                    transcript += result[0].transcript;
                    if (result.isFinal) {
                        isFinal = true;
                    }
                }
                onTranscript(transcript, isFinal);
            };
            this.recognition.onerror = (event)=>{
                onError(`Speech recognition error: ${event.error}`);
            };
            this.recognition.start();
        } catch (error) {
            onError(`Failed to start speech recognition: ${error}`);
        }
    }
    // Stop listening
    stopListening() {
        if (this.recognition) {
            this.recognition.stop();
        }
    }
    // Convert speech to text using Whisper API
    async transcribeAudio(audioBlob) {
        try {
            const formData = new FormData();
            formData.append('file', audioBlob, 'audio.wav');
            formData.append('model', 'whisper-1');
            const response = await fetch('/api/transcribe', {
                method: 'POST',
                body: formData
            });
            if (!response.ok) {
                throw new Error(`Transcription failed: ${response.statusText}`);
            }
            const result = await response.json();
            return result.text;
        } catch (error) {
            console.error('Transcription error:', error);
            throw error;
        }
    }
    // Text-to-speech using OpenAI TTS API
    async speak(text, onStart, onEnd, onMouthSync) {
        try {
            onStart?.();
            // Generate mouth sync data immediately
            const mouthSyncData = this.generateMouthSyncData(text);
            onMouthSync?.(mouthSyncData);
            // Call OpenAI TTS API
            const response = await fetch('/api/text-to-speech', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    text
                })
            });
            if (!response.ok) {
                throw new Error(`TTS API failed: ${response.statusText}`);
            }
            // Get audio blob from response
            const audioBlob = await response.blob();
            // Create audio element and play
            this.currentAudio = new Audio();
            const audioUrl = URL.createObjectURL(audioBlob);
            this.currentAudio.src = audioUrl;
            return new Promise((resolve, reject)=>{
                if (!this.currentAudio) return reject(new Error('Audio creation failed'));
                this.currentAudio.onended = ()=>{
                    URL.revokeObjectURL(audioUrl);
                    this.currentAudio = null;
                    onEnd?.();
                    resolve();
                };
                this.currentAudio.onerror = ()=>{
                    URL.revokeObjectURL(audioUrl);
                    this.currentAudio = null;
                    reject(new Error('Audio playback failed'));
                };
                this.currentAudio.play().catch(reject);
            });
        } catch (error) {
            console.error('TTS error:', error);
            // Fallback to browser speech synthesis
            return this.fallbackSpeak(text, onStart, onEnd, onMouthSync);
        }
    }
    // Fallback to browser speech synthesis
    async fallbackSpeak(text, onStart, onEnd, onMouthSync) {
        return new Promise((resolve, reject)=>{
            if (!this.synthesis) {
                reject(new Error('Speech synthesis not supported'));
                return;
            }
            // Cancel any ongoing speech
            this.synthesis.cancel();
            this.currentUtterance = new SpeechSynthesisUtterance(text);
            // Configure voice settings for a warm, professional trainer
            this.currentUtterance.rate = 0.9;
            this.currentUtterance.pitch = 1.1;
            this.currentUtterance.volume = 0.8;
            // Try to find a good voice
            const voices = this.synthesis.getVoices();
            const preferredVoice = voices.find((voice)=>voice.name.includes('Google') && voice.lang.startsWith('en')) || voices.find((voice)=>voice.lang.startsWith('en'));
            if (preferredVoice) {
                this.currentUtterance.voice = preferredVoice;
            }
            this.currentUtterance.onstart = ()=>{
                onStart?.();
                // Generate mouth sync data
                const mouthSyncData = this.generateMouthSyncData(text);
                onMouthSync?.(mouthSyncData);
            };
            this.currentUtterance.onend = ()=>{
                onEnd?.();
                resolve();
            };
            this.currentUtterance.onerror = (event)=>{
                reject(new Error(`Speech synthesis error: ${event.error}`));
            };
            this.synthesis.speak(this.currentUtterance);
        });
    }
    // Stop current speech
    stopSpeaking() {
        if (this.synthesis) {
            this.synthesis.cancel();
        }
    }
    // Generate mouth sync data for avatar animation
    generateMouthSyncData(text) {
        const words = text.split(' ');
        const phonemes = [];
        let currentTime = 0;
        const averageWordDuration = 0.6; // seconds per word
        words.forEach((word, index)=>{
            const wordDuration = averageWordDuration * (word.length / 5); // Adjust based on word length
            // Simple phoneme mapping (in a real app, you'd use a proper phoneme library)
            const wordPhonemes = this.mapWordToPhonemes(word);
            const phonemeDuration = wordDuration / wordPhonemes.length;
            wordPhonemes.forEach((phoneme, pIndex)=>{
                phonemes.push({
                    phoneme,
                    start: currentTime + pIndex * phonemeDuration,
                    end: currentTime + (pIndex + 1) * phonemeDuration,
                    intensity: this.getPhonemeIntensity(phoneme)
                });
            });
            currentTime += wordDuration + 0.1; // Small pause between words
        });
        return {
            phonemes,
            duration: currentTime,
            currentTime: 0
        };
    }
    // Simple phoneme mapping (simplified for demo)
    mapWordToPhonemes(word) {
        // This is a very simplified mapping. In production, use a proper phoneme library
        const vowels = [
            'a',
            'e',
            'i',
            'o',
            'u'
        ];
        const phonemes = [];
        for(let i = 0; i < word.length; i++){
            const char = word[i].toLowerCase();
            if (vowels.includes(char)) {
                phonemes.push('open'); // Open mouth for vowels
            } else if (char === 'm' || char === 'p' || char === 'b') {
                phonemes.push('closed'); // Closed mouth for bilabials
            } else {
                phonemes.push('mid'); // Mid position for other consonants
            }
        }
        return phonemes.length > 0 ? phonemes : [
            'mid'
        ];
    }
    // Get intensity for mouth animation
    getPhonemeIntensity(phoneme) {
        switch(phoneme){
            case 'open':
                return 0.8;
            case 'closed':
                return 0.1;
            case 'mid':
                return 0.5;
            default:
                return 0.5;
        }
    }
    // Record audio for Whisper transcription
    async startRecording() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: true
            });
            this.mediaRecorder = new MediaRecorder(stream);
            this.audioChunks = [];
            this.mediaRecorder.ondataavailable = (event)=>{
                this.audioChunks.push(event.data);
            };
            this.mediaRecorder.start();
        } catch (error) {
            throw new Error(`Failed to start recording: ${error}`);
        }
    }
    async stopRecording() {
        return new Promise((resolve, reject)=>{
            if (!this.mediaRecorder) {
                reject(new Error('No active recording'));
                return;
            }
            this.mediaRecorder.onstop = ()=>{
                const audioBlob = new Blob(this.audioChunks, {
                    type: 'audio/wav'
                });
                resolve(audioBlob);
            };
            this.mediaRecorder.stop();
            // Stop all tracks to release microphone
            this.mediaRecorder.stream.getTracks().forEach((track)=>track.stop());
        });
    }
    // Check if speech services are available
    isAvailable() {
        return "undefined" !== 'undefined' && !!(this.recognition && this.synthesis);
    }
}
const speechService = ("TURBOPACK compile-time falsy", 0) ? "TURBOPACK unreachable" : null;
}),
"[project]/src/components/AssessmentInterface.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "AssessmentInterface",
    ()=>AssessmentInterface,
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$mic$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Mic$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/mic.js [app-ssr] (ecmascript) <export default as Mic>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$mic$2d$off$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MicOff$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/mic-off.js [app-ssr] (ecmascript) <export default as MicOff>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$volume$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Volume2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/volume-2.js [app-ssr] (ecmascript) <export default as Volume2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$volume$2d$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__VolumeX$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/volume-x.js [app-ssr] (ecmascript) <export default as VolumeX>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Avatar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/Avatar.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$speechService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/speechService.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
const AssessmentInterface = ({ userName = 'there', onPhaseChange, onInsights })=>{
    // State management
    const [currentPhase, setCurrentPhase] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('welcome');
    const [conversation, setConversation] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [speechState, setSpeechState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        isListening: false,
        isProcessing: false,
        isSpeaking: false,
        transcript: '',
        confidence: 0
    });
    const [avatarState, setAvatarState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        isAnimating: false,
        currentAnimation: 'idle'
    });
    const [isAudioEnabled, setIsAudioEnabled] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [currentTranscript, setCurrentTranscript] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    // Initialize with welcome message
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const welcomeMessage = {
            id: '1',
            role: 'trainer',
            content: `Hey ${userName}! I'm Alex, your personal trainer. I'm so excited to work with you today! This session is all about YOU - where you're at right now and where you want to go. I'll be your coach, your cheerleader, and maybe a little bit of a drill sergeant when you need it. Sound good?`,
            timestamp: new Date()
        };
        setConversation([
            welcomeMessage
        ]);
        // Speak the welcome message
        if (isAudioEnabled) {
            speakMessage(welcomeMessage.content);
        }
    }, [
        userName,
        isAudioEnabled
    ]);
    // Handle speech synthesis
    const speakMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (text)=>{
        if (!isAudioEnabled || !__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$speechService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["speechService"]) return;
        setAvatarState((prev)=>({
                ...prev,
                currentAnimation: 'talking'
            }));
        setSpeechState((prev)=>({
                ...prev,
                isSpeaking: true
            }));
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$speechService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["speechService"].speak(text, ()=>{
                // On start
                setAvatarState((prev)=>({
                        ...prev,
                        currentAnimation: 'talking'
                    }));
            }, ()=>{
                // On end
                setAvatarState((prev)=>({
                        ...prev,
                        currentAnimation: 'idle'
                    }));
                setSpeechState((prev)=>({
                        ...prev,
                        isSpeaking: false
                    }));
            }, (mouthSyncData)=>{
                // On mouth sync
                setAvatarState((prev)=>({
                        ...prev,
                        mouthSyncData: {
                            ...mouthSyncData,
                            currentTime: Date.now()
                        }
                    }));
            });
        } catch (error) {
            console.error('Speech synthesis error:', error);
            setAvatarState((prev)=>({
                    ...prev,
                    currentAnimation: 'idle'
                }));
            setSpeechState((prev)=>({
                    ...prev,
                    isSpeaking: false
                }));
        }
    }, [
        isAudioEnabled
    ]);
    // Handle voice input
    const startListening = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        if (speechState.isListening || speechState.isSpeaking || !__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$speechService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["speechService"]) return;
        setAvatarState((prev)=>({
                ...prev,
                currentAnimation: 'listening'
            }));
        setSpeechState((prev)=>({
                ...prev,
                isListening: true,
                transcript: ''
            }));
        setCurrentTranscript('');
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$speechService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["speechService"].startListening((transcript, isFinal)=>{
                setCurrentTranscript(transcript);
                setSpeechState((prev)=>({
                        ...prev,
                        transcript,
                        confidence: isFinal ? 1 : 0.5
                    }));
                if (isFinal && transcript.trim()) {
                    handleUserMessage(transcript.trim());
                }
            }, (error)=>{
                console.error('Speech recognition error:', error);
                stopListening();
            });
        } catch (error) {
            console.error('Failed to start listening:', error);
            stopListening();
        }
    }, [
        speechState.isListening,
        speechState.isSpeaking
    ]);
    const stopListening = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        if (__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$speechService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["speechService"]) {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$speechService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["speechService"].stopListening();
        }
        setAvatarState((prev)=>({
                ...prev,
                currentAnimation: 'idle'
            }));
        setSpeechState((prev)=>({
                ...prev,
                isListening: false,
                isProcessing: false
            }));
    }, []);
    // Handle user message
    const handleUserMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (message)=>{
        if (!message.trim()) return;
        stopListening();
        setSpeechState((prev)=>({
                ...prev,
                isProcessing: true
            }));
        setAvatarState((prev)=>({
                ...prev,
                currentAnimation: 'thinking'
            }));
        // Add user message to conversation
        const userMessage = {
            id: Date.now().toString(),
            role: 'user',
            content: message,
            timestamp: new Date()
        };
        setConversation((prev)=>[
                ...prev,
                userMessage
            ]);
        try {
            // Send to AI trainer
            const response = await fetch('/api/trainer-chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message,
                    phase: currentPhase,
                    conversationHistory: conversation,
                    userProfile: {
                        name: userName
                    }
                })
            });
            if (!response.ok) {
                throw new Error('Failed to get trainer response');
            }
            const data = await response.json();
            // Add trainer response to conversation
            const trainerMessage = {
                id: (Date.now() + 1).toString(),
                role: 'trainer',
                content: data.response,
                timestamp: new Date()
            };
            setConversation((prev)=>[
                    ...prev,
                    trainerMessage
                ]);
            // Handle insights
            if (data.insights && onInsights) {
                onInsights(data.insights);
            }
            // Speak the response
            await speakMessage(data.response);
            // Check for phase transitions (simplified logic)
            checkPhaseTransition(data.response, data.insights);
        } catch (error) {
            console.error('Error getting trainer response:', error);
            const errorMessage = {
                id: (Date.now() + 1).toString(),
                role: 'trainer',
                content: "I'm sorry, I had a technical hiccup there. Could you repeat that?",
                timestamp: new Date()
            };
            setConversation((prev)=>[
                    ...prev,
                    errorMessage
                ]);
            await speakMessage(errorMessage.content);
        } finally{
            setSpeechState((prev)=>({
                    ...prev,
                    isProcessing: false
                }));
            setAvatarState((prev)=>({
                    ...prev,
                    currentAnimation: 'idle'
                }));
        }
    }, [
        conversation,
        currentPhase,
        userName,
        onInsights,
        speakMessage,
        stopListening
    ]);
    // Simple phase transition logic
    const checkPhaseTransition = (response, insights)=>{
        // This is simplified - in a real app, you'd have more sophisticated logic
        const responseWords = response.toLowerCase();
        if (currentPhase === 'welcome' && conversation.length > 4) {
            setCurrentPhase('discovery');
            onPhaseChange?.('discovery');
        } else if (currentPhase === 'discovery' && conversation.length > 10) {
            setCurrentPhase('physical_assessment');
            onPhaseChange?.('physical_assessment');
        }
    // Add more phase transition logic as needed
    };
    // Toggle audio
    const toggleAudio = ()=>{
        setIsAudioEnabled(!isAudioEnabled);
        if (speechState.isSpeaking && __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$speechService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["speechService"]) {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$speechService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["speechService"].stopSpeaking();
            setAvatarState((prev)=>({
                    ...prev,
                    currentAnimation: 'idle'
                }));
            setSpeechState((prev)=>({
                    ...prev,
                    isSpeaking: false
                }));
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-4",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-8",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Avatar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                    state: avatarState,
                    size: "large"
                }, void 0, false, {
                    fileName: "[project]/src/components/AssessmentInterface.tsx",
                    lineNumber: 245,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/AssessmentInterface.tsx",
                lineNumber: 244,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-full max-w-2xl mb-6",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-black bg-opacity-50 rounded-lg shadow-lg border border-cyan-400 p-6 max-h-60 overflow-y-auto backdrop-blur-sm",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AnimatePresence"], {
                        children: conversation.slice(-3).map((message)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                                initial: {
                                    opacity: 0,
                                    y: 20
                                },
                                animate: {
                                    opacity: 1,
                                    y: 0
                                },
                                exit: {
                                    opacity: 0,
                                    y: -20
                                },
                                className: `mb-4 ${message.role === 'trainer' ? 'text-left' : 'text-right'}`,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: `inline-block p-3 rounded-lg max-w-xs border ${message.role === 'trainer' ? 'bg-cyan-900 bg-opacity-50 text-cyan-100 border-cyan-400' : 'bg-purple-900 bg-opacity-50 text-purple-100 border-purple-400'}`,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm font-medium mb-1 font-mono",
                                            children: message.role === 'trainer' ? '> ALEX' : '> YOU'
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/AssessmentInterface.tsx",
                                            lineNumber: 269,
                                            columnNumber: 19
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm",
                                            children: message.content
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/AssessmentInterface.tsx",
                                            lineNumber: 272,
                                            columnNumber: 19
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/AssessmentInterface.tsx",
                                    lineNumber: 262,
                                    columnNumber: 17
                                }, ("TURBOPACK compile-time value", void 0))
                            }, message.id, false, {
                                fileName: "[project]/src/components/AssessmentInterface.tsx",
                                lineNumber: 253,
                                columnNumber: 15
                            }, ("TURBOPACK compile-time value", void 0)))
                    }, void 0, false, {
                        fileName: "[project]/src/components/AssessmentInterface.tsx",
                        lineNumber: 251,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                }, void 0, false, {
                    fileName: "[project]/src/components/AssessmentInterface.tsx",
                    lineNumber: 250,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/AssessmentInterface.tsx",
                lineNumber: 249,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            currentTranscript && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                initial: {
                    opacity: 0
                },
                animate: {
                    opacity: 1
                },
                className: "mb-4 p-3 bg-green-900 bg-opacity-50 rounded-lg border border-green-400 backdrop-blur-sm",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-sm text-green-300 font-mono",
                    children: `> RECEIVING: "${currentTranscript}"`
                }, void 0, false, {
                    fileName: "[project]/src/components/AssessmentInterface.tsx",
                    lineNumber: 287,
                    columnNumber: 11
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/AssessmentInterface.tsx",
                lineNumber: 282,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex space-x-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].button, {
                        whileHover: {
                            scale: 1.05
                        },
                        whileTap: {
                            scale: 0.95
                        },
                        onClick: speechState.isListening ? stopListening : startListening,
                        disabled: speechState.isSpeaking || speechState.isProcessing,
                        className: `p-4 rounded-full shadow-lg transition-colors border-2 ${speechState.isListening ? 'bg-red-500 text-white border-red-400 shadow-red-400/50' : speechState.isSpeaking || speechState.isProcessing ? 'bg-gray-600 text-gray-400 cursor-not-allowed border-gray-500' : 'bg-cyan-500 text-white hover:bg-cyan-600 border-cyan-400 shadow-cyan-400/50'}`,
                        style: {
                            boxShadow: speechState.isListening ? '0 0 20px rgba(239, 68, 68, 0.5)' : '0 0 20px rgba(34, 211, 238, 0.5)'
                        },
                        children: speechState.isListening ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$mic$2d$off$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MicOff$3e$__["MicOff"], {
                            size: 24
                        }, void 0, false, {
                            fileName: "[project]/src/components/AssessmentInterface.tsx",
                            lineNumber: 314,
                            columnNumber: 38
                        }, ("TURBOPACK compile-time value", void 0)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$mic$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Mic$3e$__["Mic"], {
                            size: 24
                        }, void 0, false, {
                            fileName: "[project]/src/components/AssessmentInterface.tsx",
                            lineNumber: 314,
                            columnNumber: 61
                        }, ("TURBOPACK compile-time value", void 0))
                    }, void 0, false, {
                        fileName: "[project]/src/components/AssessmentInterface.tsx",
                        lineNumber: 296,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].button, {
                        whileHover: {
                            scale: 1.05
                        },
                        whileTap: {
                            scale: 0.95
                        },
                        onClick: toggleAudio,
                        className: `p-4 rounded-full shadow-lg transition-colors border-2 ${isAudioEnabled ? 'bg-green-500 text-white hover:bg-green-600 border-green-400 shadow-green-400/50' : 'bg-gray-600 text-white hover:bg-gray-700 border-gray-500 shadow-gray-500/50'}`,
                        style: {
                            boxShadow: isAudioEnabled ? '0 0 20px rgba(34, 197, 94, 0.5)' : '0 0 20px rgba(107, 114, 128, 0.5)'
                        },
                        children: isAudioEnabled ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$volume$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Volume2$3e$__["Volume2"], {
                            size: 24
                        }, void 0, false, {
                            fileName: "[project]/src/components/AssessmentInterface.tsx",
                            lineNumber: 333,
                            columnNumber: 29
                        }, ("TURBOPACK compile-time value", void 0)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$volume$2d$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__VolumeX$3e$__["VolumeX"], {
                            size: 24
                        }, void 0, false, {
                            fileName: "[project]/src/components/AssessmentInterface.tsx",
                            lineNumber: 333,
                            columnNumber: 53
                        }, ("TURBOPACK compile-time value", void 0))
                    }, void 0, false, {
                        fileName: "[project]/src/components/AssessmentInterface.tsx",
                        lineNumber: 318,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/AssessmentInterface.tsx",
                lineNumber: 294,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-4 text-center",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-black bg-opacity-50 px-4 py-2 rounded-lg border border-cyan-400 backdrop-blur-sm",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-sm text-cyan-300 font-mono",
                            children: [
                                speechState.isSpeaking && '> ALEX TRANSMITTING...',
                                speechState.isListening && '> LISTENING FOR INPUT...',
                                speechState.isProcessing && '> PROCESSING MESSAGE...',
                                !speechState.isSpeaking && !speechState.isListening && !speechState.isProcessing && '> CLICK MICROPHONE TO RESPOND'
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/AssessmentInterface.tsx",
                            lineNumber: 340,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-xs text-purple-300 mt-1 font-mono",
                            children: [
                                "PHASE: ",
                                currentPhase.replace('_', ' ').toUpperCase()
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/AssessmentInterface.tsx",
                            lineNumber: 347,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/AssessmentInterface.tsx",
                    lineNumber: 339,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/AssessmentInterface.tsx",
                lineNumber: 338,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/AssessmentInterface.tsx",
        lineNumber: 242,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = AssessmentInterface;
}),
"[project]/src/app/page.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>Home
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$AssessmentInterface$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/AssessmentInterface.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
function Home() {
    const [currentPhase, setCurrentPhase] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('welcome');
    const [insights, setInsights] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [userName, setUserName] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const [showAssessment, setShowAssessment] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const handleStartAssessment = ()=>{
        if (userName.trim()) {
            setShowAssessment(true);
        }
    };
    const handlePhaseChange = (phase)=>{
        setCurrentPhase(phase);
    };
    const handleInsights = (newInsights)=>{
        setInsights((prev)=>[
                ...prev,
                ...newInsights
            ]);
    };
    if (showAssessment) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$AssessmentInterface$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
            userName: userName,
            onPhaseChange: handlePhaseChange,
            onInsights: handleInsights
        }, void 0, false, {
            fileName: "[project]/src/app/page.tsx",
            lineNumber: 29,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center p-4",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "max-w-md w-full bg-black bg-opacity-50 rounded-lg shadow-xl border border-cyan-400 p-8 backdrop-blur-sm",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center mb-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-3xl font-bold text-cyan-300 mb-2 font-mono",
                            children: "AI HOLOGRAPHIC TRAINER"
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 41,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-cyan-100 text-sm",
                            children: "Experience the future of fitness with Alex, your AI holographic personal trainer"
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 44,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 40,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    htmlFor: "name",
                                    className: "block text-sm font-medium text-cyan-300 mb-2 font-mono",
                                    children: "ENTER YOUR NAME:"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 51,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    type: "text",
                                    id: "name",
                                    value: userName,
                                    onChange: (e)=>setUserName(e.target.value),
                                    placeholder: "Your name here...",
                                    className: "w-full px-3 py-2 bg-black bg-opacity-50 border border-cyan-400 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 text-cyan-100 placeholder-cyan-400 font-mono",
                                    onKeyPress: (e)=>e.key === 'Enter' && handleStartAssessment()
                                }, void 0, false, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 54,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 50,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: handleStartAssessment,
                            disabled: !userName.trim(),
                            className: "w-full bg-cyan-600 text-white py-3 px-4 rounded-md hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-offset-2 disabled:bg-gray-600 disabled:cursor-not-allowed transition-colors border border-cyan-400 font-mono",
                            style: {
                                boxShadow: '0 0 20px rgba(34, 211, 238, 0.3)'
                            },
                            children: "INITIALIZE ASSESSMENT"
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 65,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 49,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mt-8 text-center text-sm text-cyan-400 space-y-2",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "font-mono",
                            children: "🎤 MICROPHONE ACCESS REQUIRED"
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 78,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "font-mono",
                            children: "🔊 AUDIO OUTPUT RECOMMENDED"
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 79,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "font-mono",
                            children: "🤖 AI POWERED EXPERIENCE"
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 80,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 77,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/page.tsx",
            lineNumber: 39,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/page.tsx",
        lineNumber: 38,
        columnNumber: 5
    }, this);
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__ccf0dfd7._.js.map