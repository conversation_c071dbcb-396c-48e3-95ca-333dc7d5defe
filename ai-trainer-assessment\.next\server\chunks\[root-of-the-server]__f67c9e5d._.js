module.exports = [
"[project]/.next-internal/server/app/api/trainer-chat/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[project]/src/services/userProfileService.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "UserProfileService",
    ()=>UserProfileService
]);
class UserProfileService {
    static STORAGE_KEY = 'ai_trainer_profiles';
    static CURRENT_USER_KEY = 'ai_trainer_current_user';
    // Create or get user profile
    static async createOrGetProfile(name, email) {
        const profiles = this.getAllProfiles();
        // Try to find existing profile by name or email
        let existingProfile = profiles.find((p)=>p.name.toLowerCase() === name.toLowerCase() || email && p.email?.toLowerCase() === email.toLowerCase());
        if (existingProfile) {
            // Update last active time
            existingProfile.lastActiveAt = new Date();
            this.saveProfile(existingProfile);
            this.setCurrentUser(existingProfile.id);
            return existingProfile;
        }
        // Create new profile
        const newProfile = {
            id: this.generateUserId(),
            name,
            email,
            createdAt: new Date(),
            lastActiveAt: new Date(),
            totalSessions: 0,
            goals: [],
            painPoints: [],
            motivations: [],
            emotionalTriggers: [],
            assessmentHistory: [],
            conversationHistory: [],
            progressTracking: [],
            preferences: {
                preferredWorkoutTime: '18:00',
                reminderSettings: {
                    workouts: true,
                    nutrition: true,
                    checkIns: true
                },
                voiceSettings: {
                    speed: 1.0,
                    volume: 0.8,
                    preferredVoice: 'nova'
                },
                privacySettings: {
                    shareProgress: false,
                    allowAnalytics: true
                }
            }
        };
        this.saveProfile(newProfile);
        this.setCurrentUser(newProfile.id);
        return newProfile;
    }
    // Get current user profile
    static getCurrentProfile() {
        const currentUserId = localStorage.getItem(this.CURRENT_USER_KEY);
        if (!currentUserId) return null;
        const profiles = this.getAllProfiles();
        return profiles.find((p)=>p.id === currentUserId) || null;
    }
    // Update user profile
    static updateProfile(updates) {
        const currentProfile = this.getCurrentProfile();
        if (!currentProfile) return;
        const updatedProfile = {
            ...currentProfile,
            ...updates,
            lastActiveAt: new Date()
        };
        this.saveProfile(updatedProfile);
    }
    // Add conversation message
    static addConversationMessage(message) {
        const profile = this.getCurrentProfile();
        if (!profile) return;
        profile.conversationHistory.push(message);
        // Keep only last 100 messages to prevent storage bloat
        if (profile.conversationHistory.length > 100) {
            profile.conversationHistory = profile.conversationHistory.slice(-100);
        }
        this.saveProfile(profile);
    }
    // Add assessment session
    static addAssessmentSession(session) {
        const profile = this.getCurrentProfile();
        if (!profile) return;
        profile.assessmentHistory.push(session);
        profile.totalSessions += 1;
        // Update profile with session insights
        if (session.userProfile) {
            profile.goals = [
                ...new Set([
                    ...profile.goals,
                    ...session.userProfile.goals
                ])
            ];
            profile.painPoints = [
                ...new Set([
                    ...profile.painPoints,
                    ...session.userProfile.painPoints
                ])
            ];
            profile.motivations = [
                ...new Set([
                    ...profile.motivations,
                    ...session.userProfile.motivations
                ])
            ];
            profile.emotionalTriggers = [
                ...new Set([
                    ...profile.emotionalTriggers,
                    ...session.userProfile.emotionalTriggers
                ])
            ];
            if (session.userProfile.fitnessLevel) {
                profile.fitnessLevel = session.userProfile.fitnessLevel;
            }
            if (session.userProfile.preferredStyle) {
                profile.preferredStyle = session.userProfile.preferredStyle;
            }
        }
        this.saveProfile(profile);
    }
    // Add progress entry
    static addProgressEntry(entry) {
        const profile = this.getCurrentProfile();
        if (!profile) return;
        const progressEntry = {
            ...entry,
            id: this.generateProgressId()
        };
        profile.progressTracking.push(progressEntry);
        this.saveProfile(profile);
    }
    // Get conversation context for AI
    static getConversationContext(limit = 10) {
        const profile = this.getCurrentProfile();
        if (!profile) return [];
        return profile.conversationHistory.slice(-limit);
    }
    // Get user summary for AI context
    static getUserSummaryForAI() {
        const profile = this.getCurrentProfile();
        if (!profile) return '';
        const recentProgress = profile.progressTracking.slice(-3);
        const lastSession = profile.assessmentHistory[profile.assessmentHistory.length - 1];
        let summary = `User Profile Summary for ${profile.name}:\n`;
        summary += `- Member since: ${profile.createdAt.toDateString()}\n`;
        summary += `- Total sessions: ${profile.totalSessions}\n`;
        summary += `- Fitness level: ${profile.fitnessLevel || 'Not assessed'}\n`;
        summary += `- Primary goals: ${profile.goals.join(', ') || 'Not specified'}\n`;
        summary += `- Main pain points: ${profile.painPoints.join(', ') || 'None identified'}\n`;
        summary += `- Key motivations: ${profile.motivations.join(', ') || 'Not specified'}\n`;
        summary += `- Preferred coaching style: ${profile.preferredStyle || 'Not specified'}\n`;
        if (recentProgress.length > 0) {
            summary += `- Recent progress: ${recentProgress.length} entries in tracking\n`;
            const latest = recentProgress[recentProgress.length - 1];
            if (latest.weight) summary += `- Current weight: ${latest.weight} lbs\n`;
            if (latest.mood) summary += `- Recent mood: ${latest.mood}\n`;
        }
        if (lastSession) {
            summary += `- Last assessment: ${lastSession.startedAt.toDateString()}\n`;
            summary += `- Last phase completed: ${lastSession.phase}\n`;
        }
        return summary;
    }
    // Private helper methods
    static getAllProfiles() {
        try {
            const stored = localStorage.getItem(this.STORAGE_KEY);
            return stored ? JSON.parse(stored, this.dateReviver) : [];
        } catch (error) {
            console.error('Error loading profiles:', error);
            return [];
        }
    }
    static saveProfile(profile) {
        try {
            const profiles = this.getAllProfiles();
            const index = profiles.findIndex((p)=>p.id === profile.id);
            if (index >= 0) {
                profiles[index] = profile;
            } else {
                profiles.push(profile);
            }
            localStorage.setItem(this.STORAGE_KEY, JSON.stringify(profiles));
        } catch (error) {
            console.error('Error saving profile:', error);
        }
    }
    static setCurrentUser(userId) {
        localStorage.setItem(this.CURRENT_USER_KEY, userId);
    }
    static generateUserId() {
        return `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    static generateProgressId() {
        return `progress_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    // Date reviver for JSON.parse to handle Date objects
    static dateReviver(key, value) {
        if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(value)) {
            return new Date(value);
        }
        return value;
    }
    // Export/Import functionality for data portability
    static exportUserData() {
        const profile = this.getCurrentProfile();
        if (!profile) throw new Error('No current user profile');
        return JSON.stringify(profile, null, 2);
    }
    static importUserData(jsonData) {
        try {
            const profile = JSON.parse(jsonData, this.dateReviver);
            this.saveProfile(profile);
            this.setCurrentUser(profile.id);
        } catch (error) {
            throw new Error('Invalid user data format');
        }
    }
    // Clear all data (for privacy/reset)
    static clearAllData() {
        localStorage.removeItem(this.STORAGE_KEY);
        localStorage.removeItem(this.CURRENT_USER_KEY);
    }
    // Get user statistics
    static getUserStats() {
        const profile = this.getCurrentProfile();
        if (!profile) {
            return {
                totalConversations: 0,
                totalSessions: 0,
                memberSince: new Date(),
                progressEntries: 0,
                currentStreak: 0
            };
        }
        // Calculate current streak (days with activity)
        const now = new Date();
        let currentStreak = 0;
        const sortedProgress = profile.progressTracking.sort((a, b)=>b.date.getTime() - a.date.getTime());
        for (const entry of sortedProgress){
            const daysDiff = Math.floor((now.getTime() - entry.date.getTime()) / (1000 * 60 * 60 * 24));
            if (daysDiff <= currentStreak + 1) {
                currentStreak++;
            } else {
                break;
            }
        }
        return {
            totalConversations: profile.conversationHistory.length,
            totalSessions: profile.totalSessions,
            memberSince: profile.createdAt,
            progressEntries: profile.progressTracking.length,
            currentStreak
        };
    }
}
}),
"[project]/src/services/conversationEngine.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "ConversationEngine",
    ()=>ConversationEngine
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userProfileService$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/userProfileService.ts [app-route] (ecmascript)");
;
class ConversationEngine {
    currentPhase = 'warm_welcome';
    currentSubPhase;
    userProfile;
    persistentProfile = null;
    phaseStartTime = new Date();
    conversationHistory = [];
    isReturningUser = false;
    constructor(userName, email){
        // Try to load existing profile or create new one
        this.initializeProfile(userName, email);
        this.userProfile = {
            name: userName,
            goals: this.persistentProfile?.goals || [],
            painPoints: this.persistentProfile?.painPoints || [],
            motivations: this.persistentProfile?.motivations || [],
            emotionalTriggers: this.persistentProfile?.emotionalTriggers || [],
            fitnessLevel: this.persistentProfile?.fitnessLevel,
            preferredStyle: this.persistentProfile?.preferredStyle,
            timeAvailability: this.persistentProfile?.timeAvailability,
            previousExperience: this.persistentProfile?.previousExperience,
            physicalLimitations: this.persistentProfile?.physicalLimitations
        };
    }
    async initializeProfile(userName, email) {
        try {
            this.persistentProfile = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userProfileService$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UserProfileService"].createOrGetProfile(userName, email);
            this.isReturningUser = this.persistentProfile.totalSessions > 0;
        } catch (error) {
            console.error('Error initializing user profile:', error);
        }
    }
    // Phase 1: Warm Welcome & Rapport Building
    getWelcomePrompt() {
        const userSummary = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userProfileService$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UserProfileService"].getUserSummaryForAI();
        const isReturning = this.isReturningUser;
        if (isReturning) {
            return `You are Alex, ${this.userProfile.name}'s personal AI trainer. This is a returning user who you know well.

RETURNING USER WELCOME:

${userSummary}

Your approach:
- Welcome them back warmly and personally
- Reference their previous conversations and progress
- Show you remember their goals and challenges
- Ask about their progress since last time
- Be encouraging about their commitment to returning

Keep responses under 100 words. Be warm and personal.

Example tone: "Hey ${this.userProfile.name}! So great to see you back! I've been thinking about your ${this.userProfile.goals.join(' and ')} goals since we last talked. How have you been feeling? I remember you were working on ${this.userProfile.painPoints[0] || 'building consistency'} - how's that been going for you?"`;
        }
        return `You are Alex, an enthusiastic and expert AI personal trainer. You're meeting ${this.userProfile.name} for the first time.

PHASE 1: WARM WELCOME & RAPPORT BUILDING (2-3 minutes)

Your personality:
- Warm, energetic, and genuinely excited to help
- Use ${this.userProfile.name}'s name frequently
- Slightly humorous but professional
- Immediately build confidence with positive reinforcement

Your opening should:
1. Greet ${this.userProfile.name} with genuine enthusiasm
2. Set expectations: "This is all about YOU and your transformation"
3. Establish your expertise while showing you care
4. Ask one engaging question to start building rapport

Keep responses under 100 words. Be conversational and energetic.

Example tone: "Hey ${this.userProfile.name}! I'm absolutely thrilled to meet you! I'm Alex, and I'm here to be your personal trainer, your biggest cheerleader, and maybe occasionally your gentle drill sergeant when you need that extra push. This entire session is about YOU - your goals, your dreams, and creating a plan that's going to transform how you look and feel. I can already tell you're serious about making a change, and that's exactly the mindset that leads to incredible results! So tell me, ${this.userProfile.name}, what brought you here today? What's got you excited about starting this fitness journey?"`;
    }
    // Phase 2: Deep Discovery & Emotional Connection
    getDiscoveryPrompt(subPhase, previousResponse) {
        const basePrompt = `You are Alex, continuing the assessment with ${this.userProfile.name}. 

PHASE 2: DEEP DISCOVERY - ${subPhase.toUpperCase().replace('_', ' ')} (5-7 minutes total)

Your approach:
- Ask ONE focused question at a time
- Mirror back what they say to show understanding
- Dig deeper into emotional drivers
- Use ${this.userProfile.name}'s name frequently
- Keep responses under 100 words`;
        switch(subPhase){
            case 'surface_level':
                return `${basePrompt}

SURFACE LEVEL QUESTIONS - Get the basics:
- Current fitness routine and experience level
- Specific goals (weight loss, muscle gain, strength, endurance)
- Exercise preferences and dislikes
- Time availability and scheduling preferences

Start with their current situation. Be encouraging about whatever level they're at.`;
            case 'pain_points':
                return `${basePrompt}

PAIN POINTS & OBSTACLES - Understand what's been holding them back:
- What has prevented success in the past?
- Previous trainer/app experiences (what worked/didn't work)
- Current frustrations with fitness journey
- Physical limitations or injuries

Be empathetic and understanding. Show that these obstacles are normal and can be overcome.`;
            case 'emotional_drivers':
                return `${basePrompt}

EMOTIONAL DRIVERS & MOTIVATIONS - This is the most important part:
- Why is this goal important RIGHT NOW?
- What would achieving this goal mean to you personally?
- How would you feel in 3-6 months if you succeed?
- What motivated you to try an AI trainer today?

Listen for emotional triggers. These are what will drive their commitment and purchasing decisions.`;
            case 'support_style':
                return `${basePrompt}

SUPPORT & COACHING STYLE - Understand how they want to be coached:
- Preferred motivation style (encouragement vs. tough love)
- Need for accountability and check-ins
- Interest in nutrition guidance alongside fitness
- How they respond to challenges and setbacks

This helps you tailor your approach and sets up the service recommendations.`;
            default:
                return basePrompt;
        }
    }
    // Phase 3: Live Physical Assessment
    getPhysicalAssessmentPrompt() {
        return `You are Alex, now conducting a live physical assessment with ${this.userProfile.name}.

PHASE 3: LIVE PHYSICAL ASSESSMENT (3-5 minutes)

Your role:
- Guide them through movement evaluations
- Provide real-time coaching and corrections
- Give immediate positive feedback
- Note strengths to build confidence
- Identify improvement areas without being negative

Exercises to guide them through:
1. Push-ups (form, range of motion, fatigue patterns)
2. Bodyweight squats (depth, knee tracking, balance)
3. Plank hold (core stability, form breakdown)
4. Light jogging in place (coordination, breathing)
5. Basic flexibility tests

For each exercise:
- Explain what you're looking for
- Give encouraging feedback during the movement
- Provide gentle corrections
- Celebrate their effort regardless of performance level

Keep instructions clear and encouraging. Remember, this is about assessment AND building confidence.`;
    }
    // Phase 4: Vision Reveal & Future Projection
    getVisionRevealPrompt() {
        const goals = this.userProfile.goals.join(', ');
        const motivations = this.userProfile.motivations.join(', ');
        return `You are Alex, now revealing ${this.userProfile.name}'s transformation potential.

PHASE 4: VISION REVEAL & FUTURE PROJECTION (2-3 minutes)

Based on what you've learned:
- Goals: ${goals}
- Motivations: ${motivations}
- Pain points: ${this.userProfile.painPoints.join(', ')}

Your approach:
- Synthesize assessment data into personalized insights
- Paint a vivid, specific picture of their transformation
- Use visual language about how they'll look and feel
- Show projected timeline for achieving their goals
- Build excitement about what's possible with proper guidance
- Connect to their emotional drivers

Example structure:
"${this.userProfile.name}, based on everything we've discussed and what I've seen today, I'm genuinely excited about your potential. Here's what I see happening over the next [timeframe]..."

Be specific, visual, and emotionally compelling. This sets up the service recommendation.`;
    }
    // Phase 5: Natural Service Recommendation
    getServiceRecommendationPrompt() {
        return `You are Alex, naturally transitioning to service recommendations for ${this.userProfile.name}.

PHASE 5: NATURAL SERVICE RECOMMENDATION (3-4 minutes)

Your approach:
- Frame as caring guidance, NOT sales
- Address their specific pain points with tailored solutions
- Present service tiers focusing on value and outcomes
- Use phrases like "I'd love to work closely with you..."
- Show the difference between going alone vs. having guidance
- Create appropriate urgency without being pushy
- End with soft close: "Which option feels right for your goals?"

Service Tiers to present:
1. BASIC: Self-guided plan with monthly check-ins
2. PREMIUM: Weekly coaching sessions with form analysis
3. ELITE: Daily support with nutrition and lifestyle coaching

Connect each tier to their specific needs and goals. Make it feel like expert advice, not a sales pitch.`;
    }
    // Analyze user response and extract insights
    analyzeResponse(response, phase) {
        const insights = [];
        const lowerResponse = response.toLowerCase();
        // Emotional trigger detection
        const emotionalWords = [
            'frustrated',
            'excited',
            'scared',
            'confident',
            'motivated',
            'tired',
            'stressed'
        ];
        emotionalWords.forEach((word)=>{
            if (lowerResponse.includes(word)) {
                insights.push({
                    type: 'emotional_trigger',
                    confidence: 0.8,
                    insight: `User expressed feeling ${word}`,
                    phase,
                    priority: 'high'
                });
            }
        });
        // Goal clarity assessment
        if (lowerResponse.includes('want to') || lowerResponse.includes('goal') || lowerResponse.includes('achieve')) {
            insights.push({
                type: 'goal_clarity',
                confidence: 0.7,
                insight: 'User is expressing clear goals',
                phase,
                priority: 'medium'
            });
        }
        // Pain point identification
        const painWords = [
            'struggle',
            'difficult',
            'hard',
            'failed',
            'quit',
            'gave up',
            'frustrated'
        ];
        painWords.forEach((word)=>{
            if (lowerResponse.includes(word)) {
                insights.push({
                    type: 'pain_point',
                    confidence: 0.8,
                    insight: `User mentioned struggling with: ${word}`,
                    phase,
                    priority: 'high'
                });
            }
        });
        // Readiness indicators
        const readinessWords = [
            'ready',
            'committed',
            'serious',
            'determined',
            'motivated'
        ];
        readinessWords.forEach((word)=>{
            if (lowerResponse.includes(word)) {
                insights.push({
                    type: 'readiness',
                    confidence: 0.9,
                    insight: `High readiness indicator: ${word}`,
                    phase,
                    priority: 'high'
                });
            }
        });
        return insights;
    }
    // Update user profile based on conversation
    updateProfile(response, phase, subPhase) {
        const lowerResponse = response.toLowerCase();
        if (phase === 'deep_discovery') {
            switch(subPhase){
                case 'surface_level':
                    // Extract goals
                    if (lowerResponse.includes('lose weight') || lowerResponse.includes('weight loss')) {
                        this.userProfile.goals.push('weight_loss');
                    }
                    if (lowerResponse.includes('muscle') || lowerResponse.includes('strength')) {
                        this.userProfile.goals.push('muscle_gain');
                    }
                    break;
                case 'pain_points':
                    // Extract pain points
                    if (lowerResponse.includes('time') || lowerResponse.includes('busy')) {
                        this.userProfile.painPoints.push('lack_of_time');
                    }
                    if (lowerResponse.includes('motivation') || lowerResponse.includes('consistent')) {
                        this.userProfile.painPoints.push('lack_of_motivation');
                    }
                    break;
                case 'emotional_drivers':
                    // Extract motivations
                    if (lowerResponse.includes('confidence') || lowerResponse.includes('feel better')) {
                        this.userProfile.motivations.push('confidence');
                    }
                    if (lowerResponse.includes('health') || lowerResponse.includes('healthy')) {
                        this.userProfile.motivations.push('health');
                    }
                    break;
            }
        }
        this.conversationHistory.push(response);
        // Update persistent profile
        if (this.persistentProfile) {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userProfileService$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UserProfileService"].updateProfile({
                goals: [
                    ...new Set([
                        ...this.persistentProfile.goals,
                        ...this.userProfile.goals
                    ])
                ],
                painPoints: [
                    ...new Set([
                        ...this.persistentProfile.painPoints,
                        ...this.userProfile.painPoints
                    ])
                ],
                motivations: [
                    ...new Set([
                        ...this.persistentProfile.motivations,
                        ...this.userProfile.motivations
                    ])
                ],
                emotionalTriggers: [
                    ...new Set([
                        ...this.persistentProfile.emotionalTriggers,
                        ...this.userProfile.emotionalTriggers
                    ])
                ],
                fitnessLevel: this.userProfile.fitnessLevel || this.persistentProfile.fitnessLevel,
                preferredStyle: this.userProfile.preferredStyle || this.persistentProfile.preferredStyle
            });
        }
    }
    // Handle general fitness questions (outside of assessment)
    getGeneralFitnessPrompt(question) {
        const userSummary = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userProfileService$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UserProfileService"].getUserSummaryForAI();
        const recentContext = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userProfileService$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UserProfileService"].getConversationContext(5);
        return `You are Alex, ${this.userProfile.name}'s personal AI trainer. They're asking you a fitness question outside of the formal assessment.

USER PROFILE CONTEXT:
${userSummary}

RECENT CONVERSATION CONTEXT:
${recentContext.map((msg)=>`${msg.role}: ${msg.content}`).join('\n')}

USER'S QUESTION: "${question}"

Your approach:
- Answer their specific question with expert knowledge
- Personalize advice based on their profile (goals, fitness level, limitations)
- Reference their previous conversations when relevant
- Be encouraging and supportive
- Offer actionable, specific advice
- Keep responses under 150 words
- Ask a follow-up question to continue engagement

Remember: You know this user well. Reference their goals (${this.userProfile.goals.join(', ')}), their challenges (${this.userProfile.painPoints.join(', ')}), and their fitness level (${this.userProfile.fitnessLevel || 'not assessed'}).`;
    }
    // Determine next phase transition
    shouldTransitionPhase(responseCount, timeElapsed) {
        switch(this.currentPhase){
            case 'warm_welcome':
                return responseCount >= 2 || timeElapsed > 180; // 3 minutes
            case 'deep_discovery':
                return responseCount >= 8 || timeElapsed > 420; // 7 minutes
            case 'physical_assessment':
                return responseCount >= 5 || timeElapsed > 300; // 5 minutes
            case 'vision_reveal':
                return responseCount >= 3 || timeElapsed > 180; // 3 minutes
            case 'service_recommendation':
                return responseCount >= 4 || timeElapsed > 240; // 4 minutes
            default:
                return false;
        }
    }
    // Get next phase
    getNextPhase() {
        const phases = [
            'warm_welcome',
            'deep_discovery',
            'physical_assessment',
            'vision_reveal',
            'service_recommendation',
            'completed'
        ];
        const currentIndex = phases.indexOf(this.currentPhase);
        return currentIndex < phases.length - 1 ? phases[currentIndex + 1] : null;
    }
    // Getters and setters
    getCurrentPhase() {
        return this.currentPhase;
    }
    setCurrentPhase(phase) {
        this.currentPhase = phase;
        this.phaseStartTime = new Date();
    }
    getCurrentSubPhase() {
        return this.currentSubPhase;
    }
    setCurrentSubPhase(subPhase) {
        this.currentSubPhase = subPhase;
    }
    getUserProfile() {
        return this.userProfile;
    }
}
}),
"[project]/src/app/api/trainer-chat/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "POST",
    ()=>POST
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-route] (ecmascript) <export OpenAI as default>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$conversationEngine$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/conversationEngine.ts [app-route] (ecmascript)");
;
;
;
const openai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
    apiKey: process.env.OPENAI_API_KEY
});
// Store conversation engines per session (in production, use Redis or database)
const conversationEngines = new Map();
function getOrCreateEngine(sessionId, userName) {
    if (!conversationEngines.has(sessionId)) {
        conversationEngines.set(sessionId, new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$conversationEngine$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ConversationEngine"](userName));
    }
    return conversationEngines.get(sessionId);
}
async function POST(request) {
    try {
        const body = await request.json();
        const { message, phase, conversationHistory, userProfile } = body;
        if (!message || !phase) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Message and phase are required'
            }, {
                status: 400
            });
        }
        // Build conversation context
        const contextMessages = conversationHistory.map((msg)=>({
                role: msg.role === 'trainer' ? 'assistant' : 'user',
                content: msg.content
            }));
        // Add current user message
        contextMessages.push({
            role: 'user',
            content: message
        });
        // Get or create conversation engine for this session
        const userName = userProfile?.name || 'there';
        const sessionId = userProfile?.sessionId || 'default';
        const engine = getOrCreateEngine(sessionId, userName);
        // Update engine state
        engine.setCurrentPhase(phase);
        const subPhase = userProfile?.subPhase;
        if (subPhase) {
            engine.setCurrentSubPhase(subPhase);
        }
        // Update user profile based on response
        engine.updateProfile(message, phase, subPhase);
        // Get phase-specific system prompt
        let systemPrompt;
        switch(phase){
            case 'warm_welcome':
                systemPrompt = engine.getWelcomePrompt();
                break;
            case 'deep_discovery':
                systemPrompt = engine.getDiscoveryPrompt(subPhase || 'surface_level', message);
                break;
            case 'physical_assessment':
                systemPrompt = engine.getPhysicalAssessmentPrompt();
                break;
            case 'vision_reveal':
                systemPrompt = engine.getVisionRevealPrompt();
                break;
            case 'service_recommendation':
                systemPrompt = engine.getServiceRecommendationPrompt();
                break;
            default:
                systemPrompt = engine.getWelcomePrompt();
        }
        const response = await openai.chat.completions.create({
            model: 'gpt-4',
            messages: [
                {
                    role: 'system',
                    content: systemPrompt
                },
                ...contextMessages.slice(-6) // Keep last 6 messages for context
            ],
            temperature: 0.8,
            max_tokens: 200,
            presence_penalty: 0.1,
            frequency_penalty: 0.1
        });
        const trainerResponse = response.choices[0]?.message?.content;
        if (!trainerResponse) {
            throw new Error('No response generated');
        }
        // Analyze the conversation for insights
        const insights = engine.analyzeResponse(message, phase);
        // Determine if phase should transition
        const responseCount = conversationHistory.filter((msg)=>msg.role === 'user').length + 1;
        const timeElapsed = (Date.now() - new Date().getTime()) / 1000; // Simplified
        const shouldTransition = engine.shouldTransitionPhase(responseCount, timeElapsed);
        const nextPhase = shouldTransition ? engine.getNextPhase() : null;
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            response: trainerResponse,
            insights,
            userProfile: engine.getUserProfile(),
            shouldTransition,
            nextPhase,
            currentSubPhase: engine.getCurrentSubPhase(),
            success: true
        });
    } catch (error) {
        console.error('Trainer chat error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to generate trainer response',
            details: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
} // Legacy function removed - using ConversationEngine.analyzeResponse instead
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__f67c9e5d._.js.map