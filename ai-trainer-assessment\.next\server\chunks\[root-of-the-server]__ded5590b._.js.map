{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trainer%20test/ai-trainer-assessment/src/app/api/trainer-chat/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport OpenAI from 'openai';\nimport { AssessmentPhase, ConversationMessage, AIInsight, DiscoverySubPhase } from '@/types';\nimport { ConversationEngine } from '@/services/conversationEngine';\n\nconst openai = new OpenAI({\n  apiKey: process.env.OPENAI_API_KEY,\n});\n\n// AI Trainer Persona and System Prompts\nconst TRAINER_SYSTEM_PROMPT = `You are <PERSON>, a warm, charismatic, and highly skilled personal trainer conducting an initial fitness assessment. Your goal is to build trust, gather information, and naturally guide the conversation toward premium training services.\n\nPERSONALITY:\n- Warm, encouraging, and slightly humorous\n- Uses the user's name frequently\n- Provides positive reinforcement immediately\n- Professional but approachable\n- Genuinely cares about the user's success\n\nCONVERSATION PHASES:\n1. WELCOME: Build rapport, create excitement\n2. DISCOVERY: Learn about goals, pain points, motivations\n3. PHYSICAL_ASSESSMENT: Guide through movement tests\n4. LIFESTYLE_ASSESSMENT: Understand daily habits, constraints\n5. REVEAL_VISION: Show what's possible, build excitement\n6. UPSELL: Naturally recommend premium services\n\nGUIDELINES:\n- Keep responses conversational and under 100 words\n- Ask one focused question at a time\n- Mirror back what the user says to show understanding\n- Identify emotional drivers and pain points\n- Build toward the vision of their transformed self\n- Never hard sell - make recommendations feel like caring advice\n\nCurrent phase will be provided in the request. Adapt your response accordingly.`;\n\nconst PHASE_PROMPTS = {\n  welcome: `Focus on building rapport and excitement. Welcome them warmly, use their name, and set a positive tone for the session.`,\n  \n  discovery: `Ask layered questions to understand their goals and motivations:\n- Surface level: Current fitness routine, experience\n- Pain points: What's been holding them back\n- Emotional drivers: Why this goal matters now\n- Future vision: How they want to look and feel`,\n  \n  physical_assessment: `Guide them through simple movement assessments while providing encouraging feedback. Focus on form, mobility, and identifying areas for improvement.`,\n  \n  lifestyle_assessment: `Understand their daily life constraints:\n- Sleep habits and stress levels\n- Time availability for workouts\n- Nutrition patterns\n- Equipment access`,\n  \n  reveal_vision: `Paint a picture of their potential transformation. Use specific, visual language about how they could look and feel. Build excitement about what's possible.`,\n  \n  upsell: `Naturally transition to recommending services. Frame it as caring guidance rather than selling:\n- \"I'd love to work closely with you...\"\n- \"Here's the difference between going alone vs. having guidance...\"\n- Present options without pressure`\n};\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { \n      message, \n      phase, \n      conversationHistory, \n      userProfile \n    }: {\n      message: string;\n      phase: AssessmentPhase;\n      conversationHistory: ConversationMessage[];\n      userProfile?: any;\n    } = body;\n\n    if (!message || !phase) {\n      return NextResponse.json(\n        { error: 'Message and phase are required' },\n        { status: 400 }\n      );\n    }\n\n    // Build conversation context\n    const contextMessages = conversationHistory.map(msg => ({\n      role: msg.role === 'trainer' ? 'assistant' : 'user',\n      content: msg.content\n    }));\n\n    // Add current user message\n    contextMessages.push({\n      role: 'user',\n      content: message\n    });\n\n    // Create phase-specific system prompt\n    const phasePrompt = PHASE_PROMPTS[phase] || PHASE_PROMPTS.welcome;\n    const fullSystemPrompt = `${TRAINER_SYSTEM_PROMPT}\\n\\nCURRENT PHASE: ${phase.toUpperCase()}\\n${phasePrompt}`;\n\n    // Add user profile context if available\n    const profileContext = userProfile ? \n      `\\n\\nUSER PROFILE: ${JSON.stringify(userProfile)}` : '';\n\n    const response = await openai.chat.completions.create({\n      model: 'gpt-4',\n      messages: [\n        {\n          role: 'system',\n          content: fullSystemPrompt + profileContext\n        },\n        ...contextMessages\n      ],\n      temperature: 0.8, // Slightly creative for personality\n      max_tokens: 200, // Keep responses concise\n      presence_penalty: 0.1,\n      frequency_penalty: 0.1\n    });\n\n    const trainerResponse = response.choices[0]?.message?.content;\n\n    if (!trainerResponse) {\n      throw new Error('No response generated');\n    }\n\n    // Analyze the conversation for insights\n    const insights = await analyzeConversation(message, trainerResponse, phase);\n\n    return NextResponse.json({\n      response: trainerResponse,\n      insights,\n      success: true\n    });\n\n  } catch (error) {\n    console.error('Trainer chat error:', error);\n    \n    return NextResponse.json(\n      { \n        error: 'Failed to generate trainer response',\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n\n// Analyze conversation for AI insights\nasync function analyzeConversation(\n  userMessage: string, \n  trainerResponse: string, \n  phase: AssessmentPhase\n): Promise<AIInsight[]> {\n  try {\n    const analysisPrompt = `Analyze this fitness assessment conversation for key insights:\n\nUSER MESSAGE: \"${userMessage}\"\nTRAINER RESPONSE: \"${trainerResponse}\"\nPHASE: ${phase}\n\nIdentify insights in these categories:\n1. MOTIVATION: What drives this person?\n2. PAIN_POINT: What frustrates or holds them back?\n3. GOAL_CLARITY: How clear are their goals?\n4. READINESS: How ready are they to commit?\n5. UPSELL_OPPORTUNITY: When/how to recommend premium services?\n\nReturn a JSON array of insights with: type, confidence (0-1), insight, suggestedResponse.\nKeep insights concise and actionable.`;\n\n    const response = await openai.chat.completions.create({\n      model: 'gpt-3.5-turbo',\n      messages: [{ role: 'user', content: analysisPrompt }],\n      temperature: 0.3,\n      max_tokens: 300\n    });\n\n    const analysisText = response.choices[0]?.message?.content;\n    \n    if (analysisText) {\n      try {\n        return JSON.parse(analysisText);\n      } catch {\n        // If JSON parsing fails, return basic insights\n        return [{\n          type: 'motivation',\n          confidence: 0.5,\n          insight: 'User is engaged in the conversation',\n          suggestedResponse: 'Continue building rapport'\n        }];\n      }\n    }\n\n    return [];\n  } catch (error) {\n    console.error('Analysis error:', error);\n    return [];\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;AAIA,MAAM,SAAS,IAAI,mLAAM,CAAC;IACxB,QAAQ,QAAQ,GAAG,CAAC,cAAc;AACpC;AAEA,wCAAwC;AACxC,MAAM,wBAAwB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;+EAyBgD,CAAC;AAEhF,MAAM,gBAAgB;IACpB,SAAS,CAAC,uHAAuH,CAAC;IAElI,WAAW,CAAC;;;;+CAIiC,CAAC;IAE9C,qBAAqB,CAAC,oJAAoJ,CAAC;IAE3K,sBAAsB,CAAC;;;;kBAIP,CAAC;IAEjB,eAAe,CAAC,4JAA4J,CAAC;IAE7K,QAAQ,CAAC;;;kCAGuB,CAAC;AACnC;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,OAAO,EACP,KAAK,EACL,mBAAmB,EACnB,WAAW,EACZ,GAKG;QAEJ,IAAI,CAAC,WAAW,CAAC,OAAO;YACtB,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiC,GAC1C;gBAAE,QAAQ;YAAI;QAElB;QAEA,6BAA6B;QAC7B,MAAM,kBAAkB,oBAAoB,GAAG,CAAC,CAAA,MAAO,CAAC;gBACtD,MAAM,IAAI,IAAI,KAAK,YAAY,cAAc;gBAC7C,SAAS,IAAI,OAAO;YACtB,CAAC;QAED,2BAA2B;QAC3B,gBAAgB,IAAI,CAAC;YACnB,MAAM;YACN,SAAS;QACX;QAEA,sCAAsC;QACtC,MAAM,cAAc,aAAa,CAAC,MAAM,IAAI,cAAc,OAAO;QACjE,MAAM,mBAAmB,GAAG,sBAAsB,mBAAmB,EAAE,MAAM,WAAW,GAAG,EAAE,EAAE,aAAa;QAE5G,wCAAwC;QACxC,MAAM,iBAAiB,cACrB,CAAC,kBAAkB,EAAE,KAAK,SAAS,CAAC,cAAc,GAAG;QAEvD,MAAM,WAAW,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACpD,OAAO;YACP,UAAU;gBACR;oBACE,MAAM;oBACN,SAAS,mBAAmB;gBAC9B;mBACG;aACJ;YACD,aAAa;YACb,YAAY;YACZ,kBAAkB;YAClB,mBAAmB;QACrB;QAEA,MAAM,kBAAkB,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;QAEtD,IAAI,CAAC,iBAAiB;YACpB,MAAM,IAAI,MAAM;QAClB;QAEA,wCAAwC;QACxC,MAAM,WAAW,MAAM,oBAAoB,SAAS,iBAAiB;QAErE,OAAO,gJAAY,CAAC,IAAI,CAAC;YACvB,UAAU;YACV;YACA,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QAErC,OAAO,gJAAY,CAAC,IAAI,CACtB;YACE,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,uCAAuC;AACvC,eAAe,oBACb,WAAmB,EACnB,eAAuB,EACvB,KAAsB;IAEtB,IAAI;QACF,MAAM,iBAAiB,CAAC;;eAEb,EAAE,YAAY;mBACV,EAAE,gBAAgB;OAC9B,EAAE,MAAM;;;;;;;;;;qCAUsB,CAAC;QAElC,MAAM,WAAW,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACpD,OAAO;YACP,UAAU;gBAAC;oBAAE,MAAM;oBAAQ,SAAS;gBAAe;aAAE;YACrD,aAAa;YACb,YAAY;QACd;QAEA,MAAM,eAAe,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;QAEnD,IAAI,cAAc;YAChB,IAAI;gBACF,OAAO,KAAK,KAAK,CAAC;YACpB,EAAE,OAAM;gBACN,+CAA+C;gBAC/C,OAAO;oBAAC;wBACN,MAAM;wBACN,YAAY;wBACZ,SAAS;wBACT,mBAAmB;oBACrB;iBAAE;YACJ;QACF;QAEA,OAAO,EAAE;IACX,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mBAAmB;QACjC,OAAO,EAAE;IACX;AACF", "debugId": null}}]}