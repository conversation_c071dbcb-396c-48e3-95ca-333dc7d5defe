'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Phone, Video, Clock, User, Zap } from 'lucide-react';
import { TrainerPersonality } from '@/services/trainerPersonalities';

interface CallTrainerButtonProps {
  trainerPersonality: TrainerPersonality;
  userName: string;
  onStartCall: () => void;
  className?: string;
}

export const CallTrainerButton: React.FC<CallTrainerButtonProps> = ({
  trainerPersonality,
  userName,
  onStartCall,
  className = ''
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);

  const getAvailabilityStatus = () => {
    const hour = currentTime.getHours();
    if (hour >= 6 && hour < 22) {
      return { status: 'available', text: 'Available Now', color: 'text-red-500', bgColor: 'bg-red-500' };
    } else {
      return { status: 'away', text: 'Available Soon', color: 'text-red-400', bgColor: 'bg-red-400' };
    }
  };

  const getEstimatedResponseTime = () => {
    const availability = getAvailabilityStatus();
    return availability.status === 'available' ? 'Instant' : '< 1 min';
  };

  const availability = getAvailabilityStatus();

  const handleStartCall = async () => {
    setIsConnecting(true);
    try {
      // Add a small delay for visual feedback
      await new Promise(resolve => setTimeout(resolve, 500));
      onStartCall();
    } catch (error) {
      console.error('Error starting call:', error);
    } finally {
      setIsConnecting(false);
    }
  };

  return (
    <motion.div
      className={`relative ${className}`}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      {/* Main Call Button */}
      <motion.button
        onClick={handleStartCall}
        disabled={isConnecting}
        className="w-full bg-black bg-opacity-60 backdrop-blur-xl border border-red-600 border-opacity-40 rounded-3xl p-8 text-left transition-all duration-300 hover:bg-opacity-80 hover:border-opacity-60 disabled:opacity-50 disabled:cursor-not-allowed"
        whileHover={{ scale: isConnecting ? 1 : 1.02 }}
        whileTap={{ scale: isConnecting ? 1 : 0.98 }}
      >
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-red-600 bg-opacity-20 border border-red-600 border-opacity-40 rounded-2xl flex items-center justify-center">
              <span className="text-2xl">{trainerPersonality.avatar}</span>
            </div>
            <div>
              <h3 className="text-xl font-semibold text-white mb-1">
                {trainerPersonality.name}
              </h3>
              <p className="text-white text-opacity-60 text-sm">
                {trainerPersonality.description}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <div className={`w-3 h-3 ${availability.bgColor} rounded-full ${isConnecting ? 'animate-pulse' : ''}`}></div>
            <span className={`text-sm font-medium ${availability.color}`}>
              {isConnecting ? 'Connecting...' : availability.text}
            </span>
          </div>
        </div>

        {/* Call Stats */}
        <div className="grid grid-cols-3 gap-4 mb-6">
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Clock className="w-4 h-4 text-white text-opacity-60" />
            </div>
            <div className="text-white text-opacity-90 text-sm font-medium">
              {getEstimatedResponseTime()}
            </div>
            <div className="text-white text-opacity-50 text-xs">
              Response Time
            </div>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Video className="w-4 h-4 text-white text-opacity-60" />
            </div>
            <div className="text-white text-opacity-90 text-sm font-medium">
              HD Video
            </div>
            <div className="text-white text-opacity-50 text-xs">
              Quality
            </div>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Zap className="w-4 h-4 text-white text-opacity-60" />
            </div>
            <div className="text-white text-opacity-90 text-sm font-medium">
              AI Powered
            </div>
            <div className="text-white text-opacity-50 text-xs">
              Analysis
            </div>
          </div>
        </div>

        {/* Call Action */}
        <div className="flex items-center justify-between">
          <div>
            <div className="text-white text-opacity-60 text-sm mb-1">
              Ready to start your session?
            </div>
            <div className="text-white font-medium">
              Tap to call {trainerPersonality.name}
            </div>
          </div>
          
          <motion.div
            className={`w-14 h-14 rounded-full flex items-center justify-center ${
              isConnecting
                ? 'bg-red-400 animate-pulse'
                : 'bg-red-600 hover:bg-red-500'
            }`}
            animate={
              isConnecting
                ? { scale: [1, 1.1, 1], rotate: [0, 180, 360] }
                : isHovered
                  ? { scale: 1.1 }
                  : { scale: 1 }
            }
            transition={{
              duration: isConnecting ? 1 : 0.2,
              repeat: isConnecting ? Infinity : 0
            }}
          >
            <Phone className="w-6 h-6 text-white" />
          </motion.div>
        </div>
      </motion.button>

      {/* Floating Features */}
      <motion.div
        className="absolute -top-2 -right-2 bg-red-600 text-white text-xs font-medium px-3 py-1 rounded-full border border-red-500"
        animate={{ y: [0, -2, 0] }}
        transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
      >
        Live Analysis
      </motion.div>

      {/* Privacy Notice */}
      <div className="mt-4 text-center">
        <p className="text-white text-opacity-40 text-xs">
          Camera access required for personalized coaching. Your privacy is protected.
        </p>
      </div>
    </motion.div>
  );
};

export default CallTrainerButton;
