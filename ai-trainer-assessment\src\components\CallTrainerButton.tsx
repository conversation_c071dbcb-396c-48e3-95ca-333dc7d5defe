'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Phone, Video, Clock, User, Zap } from 'lucide-react';
import { TrainerPersonality } from '@/services/trainerPersonalities';

interface CallTrainerButtonProps {
  trainerPersonality: TrainerPersonality;
  userName: string;
  onStartCall: () => void;
  className?: string;
}

export const CallTrainerButton: React.FC<CallTrainerButtonProps> = ({
  trainerPersonality,
  userName,
  onStartCall,
  className = ''
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);

  const getAvailabilityStatus = () => {
    const hour = currentTime.getHours();
    if (hour >= 6 && hour < 22) {
      return { status: 'available', text: 'Available Now', color: 'text-green-500', bgColor: 'bg-green-500' };
    } else {
      return { status: 'away', text: 'Available Soon', color: 'text-yellow-500', bgColor: 'bg-yellow-500' };
    }
  };

  const getEstimatedResponseTime = () => {
    const availability = getAvailabilityStatus();
    return availability.status === 'available' ? 'Instant' : '< 1 min';
  };

  const availability = getAvailabilityStatus();

  return (
    <motion.div
      className={`relative ${className}`}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      {/* Main Call Button */}
      <motion.button
        onClick={onStartCall}
        className="w-full bg-white bg-opacity-5 backdrop-blur-xl border border-white border-opacity-10 rounded-3xl p-8 text-left transition-all duration-300 hover:bg-opacity-10 hover:border-opacity-20"
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-white bg-opacity-10 rounded-2xl flex items-center justify-center">
              <span className="text-2xl">{trainerPersonality.avatar}</span>
            </div>
            <div>
              <h3 className="text-xl font-semibold text-white mb-1">
                {trainerPersonality.name}
              </h3>
              <p className="text-white text-opacity-60 text-sm">
                {trainerPersonality.description}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <div className={`w-3 h-3 ${availability.bgColor} rounded-full`}></div>
            <span className={`text-sm font-medium ${availability.color}`}>
              {availability.text}
            </span>
          </div>
        </div>

        {/* Call Stats */}
        <div className="grid grid-cols-3 gap-4 mb-6">
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Clock className="w-4 h-4 text-white text-opacity-60" />
            </div>
            <div className="text-white text-opacity-90 text-sm font-medium">
              {getEstimatedResponseTime()}
            </div>
            <div className="text-white text-opacity-50 text-xs">
              Response Time
            </div>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Video className="w-4 h-4 text-white text-opacity-60" />
            </div>
            <div className="text-white text-opacity-90 text-sm font-medium">
              HD Video
            </div>
            <div className="text-white text-opacity-50 text-xs">
              Quality
            </div>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Zap className="w-4 h-4 text-white text-opacity-60" />
            </div>
            <div className="text-white text-opacity-90 text-sm font-medium">
              AI Powered
            </div>
            <div className="text-white text-opacity-50 text-xs">
              Analysis
            </div>
          </div>
        </div>

        {/* Call Action */}
        <div className="flex items-center justify-between">
          <div>
            <div className="text-white text-opacity-60 text-sm mb-1">
              Ready to start your session?
            </div>
            <div className="text-white font-medium">
              Tap to call {trainerPersonality.name}
            </div>
          </div>
          
          <motion.div
            className="w-14 h-14 bg-green-500 rounded-full flex items-center justify-center"
            animate={isHovered ? { scale: 1.1 } : { scale: 1 }}
            transition={{ duration: 0.2 }}
          >
            <Phone className="w-6 h-6 text-white" />
          </motion.div>
        </div>
      </motion.button>

      {/* Floating Features */}
      <motion.div
        className="absolute -top-2 -right-2 bg-blue-500 text-white text-xs font-medium px-3 py-1 rounded-full"
        animate={{ y: [0, -2, 0] }}
        transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
      >
        Live Analysis
      </motion.div>

      {/* Privacy Notice */}
      <div className="mt-4 text-center">
        <p className="text-white text-opacity-40 text-xs">
          Camera access required for personalized coaching. Your privacy is protected.
        </p>
      </div>
    </motion.div>
  );
};

export default CallTrainerButton;
