{"name": "safe-array-concat", "version": "1.1.3", "description": "`Array.prototype.concat`, but made safe by ignoring Symbol.isConcatSpreadable", "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "postlint": "tsc && attw -P", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "tests-only": "nyc tape test", "test": "npm run tests-only", "posttest": "npx npm@'>= 10.2' audit --production"}, "keywords": ["safe", "Array", "concat", "push", "isConcatSpreadable"], "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/ljharb/safe-array-concat.git"}, "bugs": {"url": "https://github.com/ljharb/safe-array-concat/issues"}, "homepage": "https://github.com/ljharb/safe-array-concat#readme", "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/call-bind": "^1.0.5", "@types/get-intrinsic": "^1.2.3", "@types/has-symbols": "^1.0.2", "@types/isarray": "^2.0.2", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "mock-property": "^1.1.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "set-function-length": "^1.2.2", "tape": "^5.9.0", "typescript": "next"}, "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "get-intrinsic": "^1.2.6", "has-symbols": "^1.1.0", "isarray": "^2.0.5"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">=0.4"}}