'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Mi<PERSON>, MicOff, Volume2, VolumeX } from 'lucide-react';
import Hologram from './Avatar';
import { speechService } from '@/services/speechService';
import { 
  AssessmentPhase, 
  ConversationMessage, 
  SpeechState, 
  AvatarState,
  AIInsight 
} from '@/types';

interface AssessmentInterfaceProps {
  userName?: string;
  onPhaseChange?: (phase: AssessmentPhase) => void;
  onInsights?: (insights: AIInsight[]) => void;
}

export const AssessmentInterface: React.FC<AssessmentInterfaceProps> = ({
  userName = 'there',
  onPhaseChange,
  onInsights
}) => {
  // State management
  const [currentPhase, setCurrentPhase] = useState<AssessmentPhase>('welcome');
  const [conversation, setConversation] = useState<ConversationMessage[]>([]);
  const [speechState, setSpeechState] = useState<SpeechState>({
    isListening: false,
    isProcessing: false,
    isSpeaking: false,
    transcript: '',
    confidence: 0
  });
  const [avatarState, setAvatarState] = useState<AvatarState>({
    isAnimating: false,
    currentAnimation: 'idle'
  });
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [currentTranscript, setCurrentTranscript] = useState('');

  // Initialize with welcome message
  useEffect(() => {
    const welcomeMessage: ConversationMessage = {
      id: '1',
      role: 'trainer',
      content: `Hey ${userName}! I'm Alex, your personal trainer. I'm so excited to work with you today! This session is all about YOU - where you're at right now and where you want to go. I'll be your coach, your cheerleader, and maybe a little bit of a drill sergeant when you need it. Sound good?`,
      timestamp: new Date()
    };

    setConversation([welcomeMessage]);
    
    // Speak the welcome message
    if (isAudioEnabled) {
      speakMessage(welcomeMessage.content);
    }
  }, [userName, isAudioEnabled]);

  // Handle speech synthesis
  const speakMessage = useCallback(async (text: string) => {
    if (!isAudioEnabled) return;

    setAvatarState(prev => ({ ...prev, currentAnimation: 'talking' }));
    setSpeechState(prev => ({ ...prev, isSpeaking: true }));

    try {
      await speechService.speak(
        text,
        () => {
          // On start
          setAvatarState(prev => ({ ...prev, currentAnimation: 'talking' }));
        },
        () => {
          // On end
          setAvatarState(prev => ({ ...prev, currentAnimation: 'idle' }));
          setSpeechState(prev => ({ ...prev, isSpeaking: false }));
        },
        (mouthSyncData) => {
          // On mouth sync
          setAvatarState(prev => ({ 
            ...prev, 
            mouthSyncData: { ...mouthSyncData, currentTime: Date.now() }
          }));
        }
      );
    } catch (error) {
      console.error('Speech synthesis error:', error);
      setAvatarState(prev => ({ ...prev, currentAnimation: 'idle' }));
      setSpeechState(prev => ({ ...prev, isSpeaking: false }));
    }
  }, [isAudioEnabled]);

  // Handle voice input
  const startListening = useCallback(async () => {
    if (speechState.isListening || speechState.isSpeaking) return;

    setAvatarState(prev => ({ ...prev, currentAnimation: 'listening' }));
    setSpeechState(prev => ({ ...prev, isListening: true, transcript: '' }));
    setCurrentTranscript('');

    try {
      await speechService.startListening(
        (transcript, isFinal) => {
          setCurrentTranscript(transcript);
          setSpeechState(prev => ({ 
            ...prev, 
            transcript,
            confidence: isFinal ? 1 : 0.5
          }));

          if (isFinal && transcript.trim()) {
            handleUserMessage(transcript.trim());
          }
        },
        (error) => {
          console.error('Speech recognition error:', error);
          stopListening();
        }
      );
    } catch (error) {
      console.error('Failed to start listening:', error);
      stopListening();
    }
  }, [speechState.isListening, speechState.isSpeaking]);

  const stopListening = useCallback(() => {
    speechService.stopListening();
    setAvatarState(prev => ({ ...prev, currentAnimation: 'idle' }));
    setSpeechState(prev => ({ 
      ...prev, 
      isListening: false,
      isProcessing: false
    }));
  }, []);

  // Handle user message
  const handleUserMessage = useCallback(async (message: string) => {
    if (!message.trim()) return;

    stopListening();
    setSpeechState(prev => ({ ...prev, isProcessing: true }));
    setAvatarState(prev => ({ ...prev, currentAnimation: 'thinking' }));

    // Add user message to conversation
    const userMessage: ConversationMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: message,
      timestamp: new Date()
    };

    setConversation(prev => [...prev, userMessage]);

    try {
      // Send to AI trainer
      const response = await fetch('/api/trainer-chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message,
          phase: currentPhase,
          conversationHistory: conversation,
          userProfile: { name: userName }
        })
      });

      if (!response.ok) {
        throw new Error('Failed to get trainer response');
      }

      const data = await response.json();
      
      // Add trainer response to conversation
      const trainerMessage: ConversationMessage = {
        id: (Date.now() + 1).toString(),
        role: 'trainer',
        content: data.response,
        timestamp: new Date()
      };

      setConversation(prev => [...prev, trainerMessage]);

      // Handle insights
      if (data.insights && onInsights) {
        onInsights(data.insights);
      }

      // Speak the response
      await speakMessage(data.response);

      // Check for phase transitions (simplified logic)
      checkPhaseTransition(data.response, data.insights);

    } catch (error) {
      console.error('Error getting trainer response:', error);
      
      const errorMessage: ConversationMessage = {
        id: (Date.now() + 1).toString(),
        role: 'trainer',
        content: "I'm sorry, I had a technical hiccup there. Could you repeat that?",
        timestamp: new Date()
      };

      setConversation(prev => [...prev, errorMessage]);
      await speakMessage(errorMessage.content);
    } finally {
      setSpeechState(prev => ({ ...prev, isProcessing: false }));
      setAvatarState(prev => ({ ...prev, currentAnimation: 'idle' }));
    }
  }, [conversation, currentPhase, userName, onInsights, speakMessage, stopListening]);

  // Simple phase transition logic
  const checkPhaseTransition = (response: string, insights: AIInsight[]) => {
    // This is simplified - in a real app, you'd have more sophisticated logic
    const responseWords = response.toLowerCase();
    
    if (currentPhase === 'welcome' && conversation.length > 4) {
      setCurrentPhase('discovery');
      onPhaseChange?.('discovery');
    } else if (currentPhase === 'discovery' && conversation.length > 10) {
      setCurrentPhase('physical_assessment');
      onPhaseChange?.('physical_assessment');
    }
    // Add more phase transition logic as needed
  };

  // Toggle audio
  const toggleAudio = () => {
    setIsAudioEnabled(!isAudioEnabled);
    if (speechState.isSpeaking) {
      speechService.stopSpeaking();
      setAvatarState(prev => ({ ...prev, currentAnimation: 'idle' }));
      setSpeechState(prev => ({ ...prev, isSpeaking: false }));
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-4">
      {/* Hologram */}
      <div className="mb-8">
        <Hologram state={avatarState} size="large" />
      </div>

      {/* Conversation Display */}
      <div className="w-full max-w-2xl mb-6">
        <div className="bg-black bg-opacity-50 rounded-lg shadow-lg border border-cyan-400 p-6 max-h-60 overflow-y-auto backdrop-blur-sm">
          <AnimatePresence>
            {conversation.slice(-3).map((message) => (
              <motion.div
                key={message.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className={`mb-4 ${
                  message.role === 'trainer' ? 'text-left' : 'text-right'
                }`}
              >
                <div
                  className={`inline-block p-3 rounded-lg max-w-xs border ${
                    message.role === 'trainer'
                      ? 'bg-cyan-900 bg-opacity-50 text-cyan-100 border-cyan-400'
                      : 'bg-purple-900 bg-opacity-50 text-purple-100 border-purple-400'
                  }`}
                >
                  <p className="text-sm font-medium mb-1 font-mono">
                    {message.role === 'trainer' ? '> ALEX' : '> YOU'}
                  </p>
                  <p className="text-sm">{message.content}</p>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      </div>

      {/* Current Transcript */}
      {currentTranscript && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="mb-4 p-3 bg-green-900 bg-opacity-50 rounded-lg border border-green-400 backdrop-blur-sm"
        >
          <p className="text-sm text-green-300 font-mono">
            > RECEIVING: "{currentTranscript}"
          </p>
        </motion.div>
      )}

      {/* Controls */}
      <div className="flex space-x-4">
        {/* Microphone Button */}
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={speechState.isListening ? stopListening : startListening}
          disabled={speechState.isSpeaking || speechState.isProcessing}
          className={`p-4 rounded-full shadow-lg transition-colors border-2 ${
            speechState.isListening
              ? 'bg-red-500 text-white border-red-400 shadow-red-400/50'
              : speechState.isSpeaking || speechState.isProcessing
              ? 'bg-gray-600 text-gray-400 cursor-not-allowed border-gray-500'
              : 'bg-cyan-500 text-white hover:bg-cyan-600 border-cyan-400 shadow-cyan-400/50'
          }`}
          style={{
            boxShadow: speechState.isListening
              ? '0 0 20px rgba(239, 68, 68, 0.5)'
              : '0 0 20px rgba(34, 211, 238, 0.5)'
          }}
        >
          {speechState.isListening ? <MicOff size={24} /> : <Mic size={24} />}
        </motion.button>

        {/* Audio Toggle */}
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={toggleAudio}
          className={`p-4 rounded-full shadow-lg transition-colors border-2 ${
            isAudioEnabled
              ? 'bg-green-500 text-white hover:bg-green-600 border-green-400 shadow-green-400/50'
              : 'bg-gray-600 text-white hover:bg-gray-700 border-gray-500 shadow-gray-500/50'
          }`}
          style={{
            boxShadow: isAudioEnabled
              ? '0 0 20px rgba(34, 197, 94, 0.5)'
              : '0 0 20px rgba(107, 114, 128, 0.5)'
          }}
        >
          {isAudioEnabled ? <Volume2 size={24} /> : <VolumeX size={24} />}
        </motion.button>
      </div>

      {/* Status */}
      <div className="mt-4 text-center">
        <div className="bg-black bg-opacity-50 px-4 py-2 rounded-lg border border-cyan-400 backdrop-blur-sm">
          <p className="text-sm text-cyan-300 font-mono">
            {speechState.isSpeaking && '> ALEX TRANSMITTING...'}
            {speechState.isListening && '> LISTENING FOR INPUT...'}
            {speechState.isProcessing && '> PROCESSING MESSAGE...'}
            {!speechState.isSpeaking && !speechState.isListening && !speechState.isProcessing &&
              '> CLICK MICROPHONE TO RESPOND'}
          </p>
          <p className="text-xs text-purple-300 mt-1 font-mono">
            PHASE: {currentPhase.replace('_', ' ').toUpperCase()}
          </p>
        </div>
      </div>
    </div>
  );
};

export default AssessmentInterface;
