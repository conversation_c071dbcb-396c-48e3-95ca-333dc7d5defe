'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Video, 
  VideoOff, 
  Mic, 
  MicOff, 
  Phone, 
  PhoneOff, 
  Settings,
  Eye,
  EyeOff,
  Activity,
  Heart,
  Brain
} from 'lucide-react';
import { TrainerPersonality } from '@/services/trainerPersonalities';

interface VideoCallInterfaceProps {
  trainerPersonality: TrainerPersonality;
  userName: string;
  onEndCall: () => void;
  onFacialAnalysis?: (analysis: FacialAnalysis) => void;
  className?: string;
}

export interface FacialAnalysis {
  emotion: 'happy' | 'sad' | 'angry' | 'surprised' | 'neutral' | 'focused' | 'tired';
  confidence: number;
  engagement: number; // 0-100
  fatigue: number; // 0-100
  stress: number; // 0-100
  timestamp: Date;
}

export const VideoCallInterface: React.FC<VideoCallInterfaceProps> = ({
  trainerPersonality,
  userName,
  onEndCall,
  onFacialAnalysis,
  className = ''
}) => {
  // Call state
  const [isConnecting, setIsConnecting] = useState(true);
  const [isConnected, setIsConnected] = useState(false);
  const [callDuration, setCallDuration] = useState(0);
  
  // Media state
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [cameraPermission, setCameraPermission] = useState<'granted' | 'denied' | 'prompt'>('prompt');
  
  // Analysis state
  const [facialAnalysis, setFacialAnalysis] = useState<FacialAnalysis | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [privacyMode, setPrivacyMode] = useState(false);
  
  // Refs
  const videoRef = useRef<HTMLVideoElement>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const callStartTime = useRef<Date>(new Date());

  // Initialize video call
  useEffect(() => {
    initializeCall();
    return () => {
      cleanup();
    };
  }, []);

  // Call duration timer
  useEffect(() => {
    if (isConnected) {
      const interval = setInterval(() => {
        setCallDuration(Math.floor((Date.now() - callStartTime.current.getTime()) / 1000));
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [isConnected]);

  const initializeCall = async () => {
    try {
      setIsConnecting(true);
      
      // Simulate connection delay for realism
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Request camera permission
      if (isVideoEnabled) {
        await requestCameraAccess();
      }
      
      setIsConnecting(false);
      setIsConnected(true);
      callStartTime.current = new Date();
      
    } catch (error) {
      console.error('Failed to initialize call:', error);
      setIsConnecting(false);
    }
  };

  const requestCameraAccess = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: true, 
        audio: isAudioEnabled 
      });
      
      streamRef.current = stream;
      setCameraPermission('granted');
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
      
      // Start facial analysis
      if (!privacyMode) {
        startFacialAnalysis();
      }
      
    } catch (error) {
      console.error('Camera access denied:', error);
      setCameraPermission('denied');
      setIsVideoEnabled(false);
    }
  };

  const startFacialAnalysis = () => {
    setIsAnalyzing(true);
    
    // Simulate facial analysis (in real implementation, use MediaPipe, Face-API.js, etc.)
    const analysisInterval = setInterval(() => {
      if (!privacyMode && isVideoEnabled && streamRef.current) {
        const mockAnalysis: FacialAnalysis = {
          emotion: ['happy', 'neutral', 'focused', 'tired'][Math.floor(Math.random() * 4)] as any,
          confidence: 0.7 + Math.random() * 0.3,
          engagement: 60 + Math.random() * 40,
          fatigue: Math.random() * 30,
          stress: Math.random() * 25,
          timestamp: new Date()
        };
        
        setFacialAnalysis(mockAnalysis);
        onFacialAnalysis?.(mockAnalysis);
      }
    }, 3000);

    return () => clearInterval(analysisInterval);
  };

  const toggleVideo = async () => {
    if (isVideoEnabled) {
      // Turn off video
      if (streamRef.current) {
        streamRef.current.getVideoTracks().forEach(track => track.stop());
      }
      setIsVideoEnabled(false);
      setIsAnalyzing(false);
    } else {
      // Turn on video
      try {
        await requestCameraAccess();
        setIsVideoEnabled(true);
      } catch (error) {
        console.error('Failed to enable video:', error);
      }
    }
  };

  const toggleAudio = () => {
    if (streamRef.current) {
      streamRef.current.getAudioTracks().forEach(track => {
        track.enabled = !isAudioEnabled;
      });
    }
    setIsAudioEnabled(!isAudioEnabled);
  };

  const togglePrivacyMode = () => {
    setPrivacyMode(!privacyMode);
    if (!privacyMode) {
      setIsAnalyzing(false);
      setFacialAnalysis(null);
    } else if (isVideoEnabled) {
      startFacialAnalysis();
    }
  };

  const endCall = () => {
    cleanup();
    onEndCall();
  };

  const cleanup = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
    }
    setIsAnalyzing(false);
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getPersonalityColor = () => {
    switch (trainerPersonality.color) {
      case 'red': return 'border-red-400 bg-red-900';
      case 'green': return 'border-green-400 bg-green-900';
      case 'blue': return 'border-blue-400 bg-blue-900';
      case 'purple': return 'border-purple-400 bg-purple-900';
      case 'yellow': return 'border-yellow-400 bg-yellow-900';
      default: return 'border-cyan-400 bg-cyan-900';
    }
  };

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 ${className}`}>
      {/* Connection Status */}
      <AnimatePresence>
        {isConnecting && (
          <motion.div
            className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <div className="text-center">
              <div className="text-6xl mb-4 animate-pulse">{trainerPersonality.avatar}</div>
              <h2 className="text-2xl font-bold text-cyan-300 mb-2 font-mono">
                CONNECTING TO {trainerPersonality.name.toUpperCase()}
              </h2>
              <div className="flex items-center justify-center space-x-2 text-cyan-100">
                <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
              <p className="text-cyan-400 mt-4 font-mono">Establishing secure connection...</p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Video Call Interface */}
      {isConnected && (
        <div className="flex flex-col h-screen">
          {/* Header */}
          <div className="bg-black bg-opacity-50 p-4 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="text-2xl">{trainerPersonality.avatar}</div>
              <div>
                <h2 className="text-lg font-bold text-white font-mono">
                  {trainerPersonality.name}
                </h2>
                <p className="text-sm text-gray-300">Personal Trainer Session</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="text-white font-mono">{formatDuration(callDuration)}</div>
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-green-400 text-sm font-mono">LIVE</span>
              </div>
            </div>
          </div>

          {/* Video Area */}
          <div className="flex-1 flex">
            {/* Trainer Hologram Area */}
            <div className="flex-1 relative bg-gradient-to-br from-blue-900 to-purple-900">
              <div className="absolute inset-0 flex items-center justify-center">
                <motion.div
                  className={`w-96 h-96 rounded-full border-4 ${getPersonalityColor()} bg-opacity-20 flex items-center justify-center backdrop-blur-sm`}
                  animate={{ 
                    boxShadow: [
                      '0 0 20px rgba(34, 211, 238, 0.3)',
                      '0 0 40px rgba(34, 211, 238, 0.6)',
                      '0 0 20px rgba(34, 211, 238, 0.3)'
                    ]
                  }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  <div className="text-9xl animate-pulse">{trainerPersonality.avatar}</div>
                </motion.div>
              </div>
              
              {/* Trainer Status */}
              <div className="absolute top-4 left-4">
                <div className="bg-black bg-opacity-50 rounded-lg p-3 backdrop-blur-sm">
                  <div className="flex items-center space-x-2 text-green-400">
                    <Eye className="w-4 h-4" />
                    <span className="text-sm font-mono">TRAINER ACTIVE</span>
                  </div>
                  {isAnalyzing && (
                    <div className="flex items-center space-x-2 text-cyan-400 mt-1">
                      <Brain className="w-4 h-4 animate-pulse" />
                      <span className="text-xs font-mono">ANALYZING</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* User Video Area */}
            <div className="w-80 bg-black relative">
              {isVideoEnabled && cameraPermission === 'granted' ? (
                <video
                  ref={videoRef}
                  autoPlay
                  muted
                  playsInline
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-gray-800">
                  <div className="text-center">
                    <VideoOff className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-400 font-mono">
                      {cameraPermission === 'denied' ? 'Camera Access Denied' : 'Video Disabled'}
                    </p>
                  </div>
                </div>
              )}
              
              {/* User Status Overlay */}
              <div className="absolute top-4 right-4">
                <div className="bg-black bg-opacity-50 rounded-lg p-2 backdrop-blur-sm">
                  <div className="text-white text-sm font-mono">{userName}</div>
                </div>
              </div>

              {/* Privacy Mode Indicator */}
              {privacyMode && (
                <div className="absolute bottom-4 right-4">
                  <div className="bg-red-600 bg-opacity-80 rounded-lg p-2 backdrop-blur-sm">
                    <div className="flex items-center space-x-1 text-white">
                      <EyeOff className="w-4 h-4" />
                      <span className="text-xs font-mono">PRIVATE</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Facial Analysis Panel */}
          {facialAnalysis && !privacyMode && (
            <motion.div
              className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-70 rounded-lg p-4 backdrop-blur-sm"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
            >
              <h3 className="text-white font-mono text-sm mb-3">BIOMETRIC ANALYSIS</h3>
              <div className="space-y-2 text-xs">
                <div className="flex justify-between">
                  <span className="text-gray-300">Emotion:</span>
                  <span className="text-cyan-300 font-mono">{facialAnalysis.emotion.toUpperCase()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">Engagement:</span>
                  <span className="text-green-300 font-mono">{Math.round(facialAnalysis.engagement)}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">Fatigue:</span>
                  <span className="text-yellow-300 font-mono">{Math.round(facialAnalysis.fatigue)}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">Stress:</span>
                  <span className="text-red-300 font-mono">{Math.round(facialAnalysis.stress)}%</span>
                </div>
              </div>
            </motion.div>
          )}

          {/* Control Panel */}
          <div className="bg-black bg-opacity-50 p-4">
            <div className="flex items-center justify-center space-x-6">
              {/* Video Toggle */}
              <motion.button
                onClick={toggleVideo}
                className={`p-3 rounded-full ${isVideoEnabled ? 'bg-gray-700' : 'bg-red-600'} text-white`}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                {isVideoEnabled ? <Video className="w-6 h-6" /> : <VideoOff className="w-6 h-6" />}
              </motion.button>

              {/* Audio Toggle */}
              <motion.button
                onClick={toggleAudio}
                className={`p-3 rounded-full ${isAudioEnabled ? 'bg-gray-700' : 'bg-red-600'} text-white`}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                {isAudioEnabled ? <Mic className="w-6 h-6" /> : <MicOff className="w-6 h-6" />}
              </motion.button>

              {/* Privacy Toggle */}
              <motion.button
                onClick={togglePrivacyMode}
                className={`p-3 rounded-full ${privacyMode ? 'bg-red-600' : 'bg-gray-700'} text-white`}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                title="Toggle facial analysis privacy"
              >
                {privacyMode ? <EyeOff className="w-6 h-6" /> : <Eye className="w-6 h-6" />}
              </motion.button>

              {/* End Call */}
              <motion.button
                onClick={endCall}
                className="p-4 rounded-full bg-red-600 text-white"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <PhoneOff className="w-6 h-6" />
              </motion.button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoCallInterface;
