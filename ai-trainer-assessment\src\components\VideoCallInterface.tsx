'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Video, 
  VideoOff, 
  Mic, 
  MicOff, 
  Phone, 
  PhoneOff, 
  Settings,
  Eye,
  EyeOff,
  Activity,
  Heart,
  Brain
} from 'lucide-react';
import { TrainerPersonality } from '@/services/trainerPersonalities';
import { facialAnalysisService, getFitnessInsights } from '@/services/facialAnalysisService';

interface VideoCallInterfaceProps {
  trainerPersonality: TrainerPersonality;
  userName: string;
  onEndCall: () => void;
  onFacialAnalysis?: (analysis: FacialAnalysis) => void;
  className?: string;
}

export interface FacialAnalysis {
  emotion: 'happy' | 'sad' | 'angry' | 'surprised' | 'neutral' | 'focused' | 'tired';
  confidence: number;
  engagement: number; // 0-100
  fatigue: number; // 0-100
  stress: number; // 0-100
  timestamp: Date;
}

export const VideoCallInterface: React.FC<VideoCallInterfaceProps> = ({
  trainerPersonality,
  userName,
  onEndCall,
  onFacialAnalysis,
  className = ''
}) => {
  // Call state
  const [isConnecting, setIsConnecting] = useState(true);
  const [isConnected, setIsConnected] = useState(false);
  const [callDuration, setCallDuration] = useState(0);
  
  // Media state
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [cameraPermission, setCameraPermission] = useState<'granted' | 'denied' | 'prompt'>('prompt');
  
  // Analysis state
  const [facialAnalysis, setFacialAnalysis] = useState<FacialAnalysis | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [privacyMode, setPrivacyMode] = useState(false);
  
  // Refs
  const videoRef = useRef<HTMLVideoElement>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const callStartTime = useRef<Date>(new Date());

  // Initialize video call
  useEffect(() => {
    initializeCall();
    return () => {
      cleanup();
    };
  }, []);

  // Call duration timer
  useEffect(() => {
    if (isConnected) {
      const interval = setInterval(() => {
        setCallDuration(Math.floor((Date.now() - callStartTime.current.getTime()) / 1000));
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [isConnected]);

  const initializeCall = async () => {
    try {
      setIsConnecting(true);
      
      // Simulate connection delay for realism
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Request camera permission
      if (isVideoEnabled) {
        await requestCameraAccess();
      }
      
      setIsConnecting(false);
      setIsConnected(true);
      callStartTime.current = new Date();
      
    } catch (error) {
      console.error('Failed to initialize call:', error);
      setIsConnecting(false);
    }
  };

  const requestCameraAccess = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: true, 
        audio: isAudioEnabled 
      });
      
      streamRef.current = stream;
      setCameraPermission('granted');
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
      
      // Start facial analysis
      if (!privacyMode) {
        startFacialAnalysis();
      }
      
    } catch (error) {
      console.error('Camera access denied:', error);
      setCameraPermission('denied');
      setIsVideoEnabled(false);
    }
  };

  const startFacialAnalysis = async () => {
    if (!videoRef.current || privacyMode || !facialAnalysisService) return;

    try {
      // Initialize facial analysis service
      const initialized = await facialAnalysisService.initialize();
      if (!initialized) {
        console.warn('Facial analysis service failed to initialize');
        return;
      }

      setIsAnalyzing(true);

      // Start real-time facial analysis
      facialAnalysisService.startAnalysis(
        videoRef.current,
        (analysis: FacialAnalysis) => {
          if (!privacyMode && isVideoEnabled) {
            setFacialAnalysis(analysis);
            onFacialAnalysis?.(analysis);

            // Log insights for debugging
            const insights = getFitnessInsights(analysis);
            if (insights.length > 0) {
              console.log('Fitness insights:', insights);
            }
          }
        },
        3000 // Analyze every 3 seconds
      );

    } catch (error) {
      console.error('Failed to start facial analysis:', error);
      setIsAnalyzing(false);
    }
  };

  const toggleVideo = async () => {
    if (isVideoEnabled) {
      // Turn off video
      if (streamRef.current) {
        streamRef.current.getVideoTracks().forEach(track => track.stop());
      }
      setIsVideoEnabled(false);
      setIsAnalyzing(false);
    } else {
      // Turn on video
      try {
        await requestCameraAccess();
        setIsVideoEnabled(true);
      } catch (error) {
        console.error('Failed to enable video:', error);
      }
    }
  };

  const toggleAudio = () => {
    if (streamRef.current) {
      streamRef.current.getAudioTracks().forEach(track => {
        track.enabled = !isAudioEnabled;
      });
    }
    setIsAudioEnabled(!isAudioEnabled);
  };

  const togglePrivacyMode = () => {
    const newPrivacyMode = !privacyMode;
    setPrivacyMode(newPrivacyMode);

    if (newPrivacyMode) {
      // Entering privacy mode - stop analysis
      if (facialAnalysisService) {
        facialAnalysisService.stopAnalysis();
      }
      setIsAnalyzing(false);
      setFacialAnalysis(null);
    } else if (isVideoEnabled && videoRef.current) {
      // Exiting privacy mode - restart analysis
      startFacialAnalysis();
    }
  };

  const endCall = () => {
    cleanup();
    onEndCall();
  };

  const cleanup = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
    }

    // Stop facial analysis
    if (facialAnalysisService) {
      facialAnalysisService.stopAnalysis();
    }
    setIsAnalyzing(false);
    setFacialAnalysis(null);
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getPersonalityColor = () => {
    // Use scarlet red accent color for all personalities
    return 'border-red-600 bg-red-900';
  };

  return (
    <div className={`min-h-screen bg-black ${className}`}>
      {/* Connection Status */}
      <AnimatePresence>
        {isConnecting && (
          <motion.div
            className="fixed inset-0 bg-black flex items-center justify-center z-50"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <div className="text-center">
              <div className="w-20 h-20 bg-white bg-opacity-10 rounded-full flex items-center justify-center mb-6">
                <div className="text-3xl">{trainerPersonality.avatar}</div>
              </div>
              <h2 className="text-xl font-medium text-white mb-2">
                Connecting to {trainerPersonality.name}
              </h2>
              <div className="flex items-center justify-center space-x-1">
                <div className="w-2 h-2 bg-white bg-opacity-60 rounded-full animate-pulse"></div>
                <div className="w-2 h-2 bg-white bg-opacity-60 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                <div className="w-2 h-2 bg-white bg-opacity-60 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Video Call Interface */}
      {isConnected && (
        <div className="flex flex-col h-screen">
          {/* Header */}
          <div className="bg-black border-b border-white border-opacity-20 px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-red-600 bg-opacity-20 border border-red-600 border-opacity-40 rounded-full flex items-center justify-center">
                  <span className="text-lg">{trainerPersonality.avatar}</span>
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-white">
                    {trainerPersonality.name}
                  </h2>
                  <p className="text-sm text-white text-opacity-60">Personal Trainer</p>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="text-white text-opacity-80 text-sm font-medium">
                  {formatDuration(callDuration)}
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-red-600 rounded-full animate-pulse"></div>
                  <span className="text-red-600 text-sm font-medium">Live</span>
                </div>
              </div>
            </div>
          </div>

          {/* Video Area */}
          <div className="flex-1 flex">
            {/* Trainer Area */}
            <div className="flex-1 relative bg-black">
              <div className="absolute inset-0 flex items-center justify-center">
                <motion.div
                  className="w-96 h-96 bg-red-600 bg-opacity-10 rounded-3xl flex items-center justify-center backdrop-blur-sm border-2 border-red-600 border-opacity-30 shadow-2xl"
                  animate={{
                    scale: [1, 1.02, 1],
                    boxShadow: [
                      "0 0 30px rgba(220, 20, 60, 0.3)",
                      "0 0 50px rgba(220, 20, 60, 0.5)",
                      "0 0 30px rgba(220, 20, 60, 0.3)"
                    ]
                  }}
                  transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
                >
                  <div className="text-9xl opacity-95 filter drop-shadow-lg">{trainerPersonality.avatar}</div>
                </motion.div>
              </div>

              {/* Trainer Status */}
              <div className="absolute top-6 left-6">
                <div className="bg-black bg-opacity-60 backdrop-blur-xl rounded-2xl px-4 py-2 border border-red-600 border-opacity-40">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-red-600 rounded-full animate-pulse"></div>
                    <span className="text-white text-sm font-medium">Active</span>
                  </div>
                  {isAnalyzing && (
                    <div className="flex items-center space-x-2 mt-1">
                      <Brain className="w-3 h-3 text-red-400 animate-pulse" />
                      <span className="text-red-400 text-xs font-medium">Analyzing</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* User Video Area */}
            <div className="w-80 bg-black border-l border-white border-opacity-20 relative">
              {isVideoEnabled && cameraPermission === 'granted' ? (
                <video
                  ref={videoRef}
                  autoPlay
                  muted
                  playsInline
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-black">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-red-600 bg-opacity-20 border border-red-600 border-opacity-40 rounded-full flex items-center justify-center mb-4">
                      <VideoOff className="w-8 h-8 text-white text-opacity-60" />
                    </div>
                    <p className="text-white text-opacity-60 text-sm font-medium">
                      {cameraPermission === 'denied' ? 'Camera Access Denied' : 'Video Off'}
                    </p>
                  </div>
                </div>
              )}

              {/* User Status Overlay */}
              <div className="absolute top-4 right-4">
                <div className="bg-black bg-opacity-60 backdrop-blur-xl rounded-xl px-3 py-1 border border-red-600 border-opacity-40">
                  <div className="text-white text-sm font-medium">{userName}</div>
                </div>
              </div>

              {/* Privacy Mode Indicator */}
              {privacyMode && (
                <div className="absolute bottom-4 right-4">
                  <div className="bg-red-600 bg-opacity-90 backdrop-blur-xl rounded-xl px-3 py-2 border border-red-600 border-opacity-60">
                    <div className="flex items-center space-x-2 text-white">
                      <EyeOff className="w-4 h-4" />
                      <span className="text-xs font-medium">Private</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Facial Analysis Panel */}
          {facialAnalysis && !privacyMode && (
            <motion.div
              className="absolute right-6 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-60 backdrop-blur-xl rounded-2xl p-4 border border-red-600 border-opacity-40"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
            >
              <h3 className="text-white font-semibold text-sm mb-3">Biometric Analysis</h3>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between items-center">
                  <span className="text-white text-opacity-70">Emotion</span>
                  <span className="text-red-400 font-medium capitalize">{facialAnalysis.emotion}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-white text-opacity-70">Engagement</span>
                  <span className="text-white font-medium">{Math.round(facialAnalysis.engagement)}%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-white text-opacity-70">Energy</span>
                  <span className="text-white font-medium">{Math.round(100 - facialAnalysis.fatigue)}%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-white text-opacity-70">Focus</span>
                  <span className="text-white font-medium">{Math.round(100 - facialAnalysis.stress)}%</span>
                </div>
              </div>
            </motion.div>
          )}

          {/* Control Panel */}
          <div className="bg-black border-t border-white border-opacity-20 px-6 py-6">
            <div className="flex items-center justify-center space-x-4">
              {/* Video Toggle */}
              <motion.button
                onClick={toggleVideo}
                className={`w-14 h-14 rounded-full flex items-center justify-center transition-all border-2 ${
                  isVideoEnabled
                    ? 'bg-white bg-opacity-10 text-white border-white border-opacity-30'
                    : 'bg-red-600 text-white border-red-600'
                }`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {isVideoEnabled ? <Video className="w-6 h-6" /> : <VideoOff className="w-6 h-6" />}
              </motion.button>

              {/* Audio Toggle */}
              <motion.button
                onClick={toggleAudio}
                className={`w-14 h-14 rounded-full flex items-center justify-center transition-all border-2 ${
                  isAudioEnabled
                    ? 'bg-white bg-opacity-10 text-white border-white border-opacity-30'
                    : 'bg-red-600 text-white border-red-600'
                }`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {isAudioEnabled ? <Mic className="w-6 h-6" /> : <MicOff className="w-6 h-6" />}
              </motion.button>

              {/* Privacy Toggle */}
              <motion.button
                onClick={togglePrivacyMode}
                className={`w-14 h-14 rounded-full flex items-center justify-center transition-all border-2 ${
                  privacyMode
                    ? 'bg-red-600 text-white border-red-600'
                    : 'bg-white bg-opacity-10 text-white border-white border-opacity-30'
                }`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                title="Toggle facial analysis privacy"
              >
                {privacyMode ? <EyeOff className="w-6 h-6" /> : <Eye className="w-6 h-6" />}
              </motion.button>

              {/* End Call */}
              <motion.button
                onClick={endCall}
                className="w-16 h-16 rounded-full bg-red-600 text-white flex items-center justify-center ml-4 border-2 border-red-600 shadow-lg"
                whileHover={{ scale: 1.05, boxShadow: "0 0 20px rgba(220, 20, 60, 0.5)" }}
                whileTap={{ scale: 0.95 }}
              >
                <PhoneOff className="w-7 h-7" />
              </motion.button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoCallInterface;
