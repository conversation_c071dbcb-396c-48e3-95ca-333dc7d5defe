import { NextRequest, NextResponse } from 'next/server';

const ELEVENLABS_API_KEY = '***************************************************';
const ELEVENLABS_API_URL = 'https://api.elevenlabs.io/v1';

export async function POST(request: NextRequest) {
  try {
    const { text, voiceId, voiceSettings } = await request.json();

    if (!text) {
      return NextResponse.json({ error: 'Text is required' }, { status: 400 });
    }

    if (!voiceId) {
      return NextResponse.json({ error: 'Voice ID is required' }, { status: 400 });
    }

    console.log('🎤 ElevenLabs TTS Request:', { 
      text: text.substring(0, 50) + '...', 
      voiceId,
      voiceSettings 
    });

    // Call ElevenLabs TTS API
    const response = await fetch(`${ELEVENLABS_API_URL}/text-to-speech/${voiceId}`, {
      method: 'POST',
      headers: {
        'Accept': 'audio/mpeg',
        'Content-Type': 'application/json',
        'xi-api-key': ELEVENLABS_API_KEY,
      },
      body: JSON.stringify({
        text,
        model_id: 'eleven_monolingual_v1',
        voice_settings: {
          stability: voiceSettings?.stability || 0.5,
          similarity_boost: voiceSettings?.similarity_boost || 0.75,
          style: voiceSettings?.style || 0.0,
          use_speaker_boost: voiceSettings?.use_speaker_boost || true,
        },
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ ElevenLabs API Error:', response.status, errorText);
      return NextResponse.json(
        { error: `ElevenLabs API error: ${response.status}` },
        { status: response.status }
      );
    }

    console.log('✅ ElevenLabs TTS Success');

    // Get the audio data
    const audioBuffer = await response.arrayBuffer();

    // Return the audio data with proper headers
    return new NextResponse(audioBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'audio/mpeg',
        'Content-Length': audioBuffer.byteLength.toString(),
      },
    });

  } catch (error) {
    console.error('❌ ElevenLabs TTS Error:', error);
    return NextResponse.json(
      { error: 'Failed to generate speech' },
      { status: 500 }
    );
  }
}
