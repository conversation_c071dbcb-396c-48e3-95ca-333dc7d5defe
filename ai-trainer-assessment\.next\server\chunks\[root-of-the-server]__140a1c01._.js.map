{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trainer%20test/ai-trainer-assessment/src/app/api/text-to-speech/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport OpenAI from 'openai';\n\nconst openai = new OpenAI({\n  apiKey: process.env.OPENAI_API_KEY,\n});\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { text } = body;\n\n    if (!text) {\n      return NextResponse.json(\n        { error: 'Text is required' },\n        { status: 400 }\n      );\n    }\n\n    // Generate speech using OpenAI TTS\n    const mp3 = await openai.audio.speech.create({\n      model: 'tts-1', // Use tts-1-hd for higher quality but slower\n      voice: 'nova', // <PERSON> has a warm, engaging voice perfect for a trainer\n      input: text,\n      speed: 1.0, // Normal speed\n    });\n\n    // Convert the response to a buffer\n    const buffer = Buffer.from(await mp3.arrayBuffer());\n\n    // Return the audio file\n    return new NextResponse(buffer, {\n      status: 200,\n      headers: {\n        'Content-Type': 'audio/mpeg',\n        'Content-Length': buffer.length.toString(),\n      },\n    });\n\n  } catch (error) {\n    console.error('TTS error:', error);\n    \n    return NextResponse.json(\n      { \n        error: 'Failed to generate speech',\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;AAEA,MAAM,SAAS,IAAI,mLAAM,CAAC;IACxB,QAAQ,QAAQ,GAAG,CAAC,cAAc;AACpC;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,IAAI,EAAE,GAAG;QAEjB,IAAI,CAAC,MAAM;YACT,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,mCAAmC;QACnC,MAAM,MAAM,MAAM,OAAO,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC;YAC3C,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;QACT;QAEA,mCAAmC;QACnC,MAAM,SAAS,OAAO,IAAI,CAAC,MAAM,IAAI,WAAW;QAEhD,wBAAwB;QACxB,OAAO,IAAI,gJAAY,CAAC,QAAQ;YAC9B,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,kBAAkB,OAAO,MAAM,CAAC,QAAQ;YAC1C;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAE5B,OAAO,gJAAY,CAAC,IAAI,CACtB;YACE,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}