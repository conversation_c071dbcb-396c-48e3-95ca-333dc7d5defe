module.exports = [
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[project]/src/components/Avatar.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "Hologram",
    ()=>Hologram,
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs [app-ssr] (ecmascript)");
'use client';
;
;
;
const Hologram = ({ state, className = '', size = 'medium' })=>{
    const [currentMouthShape, setCurrentMouthShape] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('closed');
    const animationRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])();
    // Size configurations
    const sizeConfig = {
        small: {
            width: 120,
            height: 120,
            scale: 0.8
        },
        medium: {
            width: 200,
            height: 200,
            scale: 1
        },
        large: {
            width: 300,
            height: 300,
            scale: 1.2
        }
    };
    const config = sizeConfig[size];
    // Animate mouth based on speech data
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (state.mouthSyncData && state.currentAnimation === 'talking') {
            const animateMouth = ()=>{
                const currentTime = Date.now() - (state.mouthSyncData?.currentTime || 0);
                const currentPhoneme = state.mouthSyncData?.phonemes.find((p)=>currentTime >= p.start * 1000 && currentTime <= p.end * 1000);
                if (currentPhoneme) {
                    setCurrentMouthShape(currentPhoneme.phoneme);
                } else {
                    setCurrentMouthShape('closed');
                }
                if (currentTime < (state.mouthSyncData?.duration || 0) * 1000) {
                    animationRef.current = requestAnimationFrame(animateMouth);
                }
            };
            animationRef.current = requestAnimationFrame(animateMouth);
        } else {
            setCurrentMouthShape('closed');
        }
        return ()=>{
            if (animationRef.current) {
                cancelAnimationFrame(animationRef.current);
            }
        };
    }, [
        state.mouthSyncData,
        state.currentAnimation
    ]);
    // Animation variants for the orb
    const containerVariants = {
        idle: {
            y: [
                0,
                -10,
                0
            ],
            rotate: [
                0,
                2,
                -2,
                0
            ],
            transition: {
                duration: 4,
                repeat: Infinity,
                ease: 'easeInOut'
            }
        },
        talking: {
            y: [
                0,
                -5,
                0
            ],
            scale: [
                1,
                1.03,
                1
            ],
            transition: {
                duration: 0.6,
                repeat: Infinity,
                ease: 'easeInOut'
            }
        },
        listening: {
            y: [
                0,
                -8,
                0
            ],
            rotate: [
                0,
                3,
                -3,
                0
            ],
            transition: {
                duration: 2.5,
                repeat: Infinity,
                ease: 'easeInOut'
            }
        },
        thinking: {
            y: [
                0,
                -12,
                0
            ],
            rotate: [
                0,
                10,
                -10,
                0
            ],
            transition: {
                duration: 3,
                repeat: Infinity,
                ease: 'easeInOut'
            }
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `flex items-center justify-center ${className}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                className: "relative",
                style: {
                    width: config.width,
                    height: config.height
                },
                variants: containerVariants,
                animate: state.currentAnimation,
                initial: "idle",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                        className: "absolute inset-0 rounded-full",
                        style: {
                            background: 'radial-gradient(circle at 30% 30%, rgba(34, 211, 238, 0.4), rgba(59, 130, 246, 0.3), rgba(147, 51, 234, 0.2), transparent)',
                            backdropFilter: 'blur(2px)',
                            border: '1px solid rgba(34, 211, 238, 0.3)',
                            boxShadow: '0 0 60px rgba(34, 211, 238, 0.4), inset 0 0 60px rgba(59, 130, 246, 0.2)'
                        },
                        animate: {
                            opacity: [
                                0.6,
                                0.9,
                                0.6
                            ],
                            scale: state.currentAnimation === 'talking' ? [
                                1,
                                1.05,
                                1
                            ] : [
                                1,
                                1.02,
                                1
                            ]
                        },
                        transition: {
                            duration: state.currentAnimation === 'talking' ? 0.5 : 2,
                            repeat: Infinity,
                            ease: 'easeInOut'
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/Avatar.tsx",
                        lineNumber: 96,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                        className: "absolute inset-8 rounded-full",
                        style: {
                            background: 'radial-gradient(circle, rgba(34, 211, 238, 0.6), rgba(59, 130, 246, 0.4), transparent)',
                            filter: 'blur(1px)'
                        },
                        animate: {
                            opacity: [
                                0.4,
                                0.8,
                                0.4
                            ],
                            scale: [
                                0.8,
                                1.2,
                                0.8
                            ],
                            rotate: [
                                0,
                                360
                            ]
                        },
                        transition: {
                            duration: 4,
                            repeat: Infinity,
                            ease: 'linear'
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/Avatar.tsx",
                        lineNumber: 118,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    [
                        ...Array(6)
                    ].map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                            className: "absolute w-2 h-2 bg-cyan-400 rounded-full",
                            style: {
                                left: `${20 + i * 10}%`,
                                top: `${30 + i * 8}%`,
                                boxShadow: '0 0 10px rgba(34, 211, 238, 0.8)'
                            },
                            animate: {
                                y: [
                                    -10,
                                    10,
                                    -10
                                ],
                                x: [
                                    -5,
                                    5,
                                    -5
                                ],
                                opacity: [
                                    0.3,
                                    1,
                                    0.3
                                ],
                                scale: [
                                    0.5,
                                    1,
                                    0.5
                                ]
                            },
                            transition: {
                                duration: 3 + i * 0.5,
                                repeat: Infinity,
                                ease: 'easeInOut',
                                delay: i * 0.3
                            }
                        }, i, false, {
                            fileName: "[project]/src/components/Avatar.tsx",
                            lineNumber: 138,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                        className: "absolute inset-0 overflow-hidden rounded-full",
                        style: {
                            background: 'repeating-linear-gradient(0deg, transparent, transparent 3px, rgba(34, 211, 238, 0.1) 3px, rgba(34, 211, 238, 0.1) 6px)'
                        },
                        animate: {
                            y: [
                                -30,
                                30,
                                -30
                            ]
                        },
                        transition: {
                            duration: 4,
                            repeat: Infinity,
                            ease: 'linear'
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/Avatar.tsx",
                        lineNumber: 162,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                        className: "absolute -inset-4 rounded-full border border-cyan-400 opacity-30",
                        animate: {
                            scale: [
                                1,
                                1.1,
                                1
                            ],
                            opacity: [
                                0.2,
                                0.5,
                                0.2
                            ],
                            rotate: [
                                0,
                                360
                            ]
                        },
                        transition: {
                            duration: 6,
                            repeat: Infinity,
                            ease: 'linear'
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/Avatar.tsx",
                        lineNumber: 178,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AnimatePresence"], {
                        children: state.currentAnimation === 'talking' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                            children: [
                                [
                                    ...Array(4)
                                ].map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                                        className: "absolute rounded-full border border-cyan-400",
                                        style: {
                                            inset: `${-8 - i * 8}px`,
                                            boxShadow: '0 0 30px rgba(34, 211, 238, 0.4)'
                                        },
                                        initial: {
                                            opacity: 0,
                                            scale: 0.8
                                        },
                                        animate: {
                                            opacity: [
                                                0,
                                                0.8,
                                                0
                                            ],
                                            scale: [
                                                0.8,
                                                1.4,
                                                1.8
                                            ]
                                        },
                                        exit: {
                                            opacity: 0
                                        },
                                        transition: {
                                            duration: 1.2,
                                            repeat: Infinity,
                                            delay: i * 0.15,
                                            ease: 'easeOut'
                                        }
                                    }, i, false, {
                                        fileName: "[project]/src/components/Avatar.tsx",
                                        lineNumber: 197,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0))),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                                    className: "absolute inset-4 rounded-full bg-cyan-400",
                                    style: {
                                        filter: 'blur(2px)',
                                        opacity: 0.3
                                    },
                                    animate: {
                                        scale: [
                                            0.5,
                                            1.5,
                                            0.5
                                        ],
                                        opacity: [
                                            0.1,
                                            0.4,
                                            0.1
                                        ]
                                    },
                                    transition: {
                                        duration: 0.8,
                                        repeat: Infinity,
                                        ease: 'easeInOut'
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/Avatar.tsx",
                                    lineNumber: 219,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true)
                    }, void 0, false, {
                        fileName: "[project]/src/components/Avatar.tsx",
                        lineNumber: 193,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AnimatePresence"], {
                        children: state.currentAnimation === 'listening' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                            children: [
                                [
                                    ...Array(5)
                                ].map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                                        className: "absolute rounded-full border border-green-400",
                                        style: {
                                            inset: `${-12 - i * 6}px`,
                                            boxShadow: '0 0 20px rgba(34, 197, 94, 0.3)'
                                        },
                                        initial: {
                                            opacity: 0,
                                            scale: 0.9
                                        },
                                        animate: {
                                            opacity: [
                                                0,
                                                0.6,
                                                0
                                            ],
                                            scale: [
                                                0.9,
                                                1.6,
                                                2.2
                                            ]
                                        },
                                        exit: {
                                            opacity: 0
                                        },
                                        transition: {
                                            duration: 3,
                                            repeat: Infinity,
                                            delay: i * 0.4,
                                            ease: 'easeOut'
                                        }
                                    }, i, false, {
                                        fileName: "[project]/src/components/Avatar.tsx",
                                        lineNumber: 244,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0))),
                                [
                                    ...Array(3)
                                ].map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                                        className: "absolute w-3 h-3 bg-green-400 rounded-full",
                                        style: {
                                            left: `${40 + i * 8}%`,
                                            top: '45%',
                                            boxShadow: '0 0 15px rgba(34, 197, 94, 0.8)'
                                        },
                                        animate: {
                                            opacity: [
                                                0.3,
                                                1,
                                                0.3
                                            ],
                                            scale: [
                                                0.8,
                                                1.2,
                                                0.8
                                            ]
                                        },
                                        transition: {
                                            duration: 1.5,
                                            repeat: Infinity,
                                            delay: i * 0.2,
                                            ease: 'easeInOut'
                                        }
                                    }, `dot-${i}`, false, {
                                        fileName: "[project]/src/components/Avatar.tsx",
                                        lineNumber: 267,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0)))
                            ]
                        }, void 0, true)
                    }, void 0, false, {
                        fileName: "[project]/src/components/Avatar.tsx",
                        lineNumber: 240,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AnimatePresence"], {
                        children: state.currentAnimation === 'thinking' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                            children: [
                                [
                                    ...Array(8)
                                ].map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                                        className: "absolute w-2 h-2 bg-purple-400 rounded-full",
                                        style: {
                                            boxShadow: '0 0 12px rgba(147, 51, 234, 0.8)'
                                        },
                                        animate: {
                                            rotate: [
                                                0,
                                                360
                                            ],
                                            scale: [
                                                0.5,
                                                1.2,
                                                0.5
                                            ],
                                            opacity: [
                                                0.4,
                                                1,
                                                0.4
                                            ]
                                        },
                                        transition: {
                                            duration: 4,
                                            repeat: Infinity,
                                            delay: i * 0.2,
                                            ease: 'linear'
                                        },
                                        initial: {
                                            left: '50%',
                                            top: '50%',
                                            x: `${Math.cos(i * 45 * Math.PI / 180) * 60}px`,
                                            y: `${Math.sin(i * 45 * Math.PI / 180) * 60}px`
                                        }
                                    }, i, false, {
                                        fileName: "[project]/src/components/Avatar.tsx",
                                        lineNumber: 297,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0))),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                                    className: "absolute inset-6 rounded-full bg-purple-400",
                                    style: {
                                        filter: 'blur(3px)',
                                        opacity: 0.2
                                    },
                                    animate: {
                                        scale: [
                                            0.8,
                                            1.3,
                                            0.8
                                        ],
                                        opacity: [
                                            0.1,
                                            0.3,
                                            0.1
                                        ],
                                        rotate: [
                                            0,
                                            180,
                                            360
                                        ]
                                    },
                                    transition: {
                                        duration: 2,
                                        repeat: Infinity,
                                        ease: 'easeInOut'
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/Avatar.tsx",
                                    lineNumber: 323,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true)
                    }, void 0, false, {
                        fileName: "[project]/src/components/Avatar.tsx",
                        lineNumber: 292,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                        className: "absolute inset-0 rounded-full",
                        style: {
                            background: 'linear-gradient(90deg, transparent 0%, rgba(34, 211, 238, 0.05) 50%, transparent 100%)'
                        },
                        animate: {
                            x: [
                                -150,
                                150,
                                -150
                            ],
                            opacity: [
                                0,
                                0.2,
                                0
                            ]
                        },
                        transition: {
                            duration: 6,
                            repeat: Infinity,
                            ease: 'linear'
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/Avatar.tsx",
                        lineNumber: 345,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/Avatar.tsx",
                lineNumber: 88,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                className: "absolute -bottom-16 text-center",
                initial: {
                    opacity: 0,
                    y: 10
                },
                animate: {
                    opacity: 1,
                    y: 0
                },
                transition: {
                    delay: 0.5
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-black bg-opacity-60 px-6 py-3 rounded-lg border border-cyan-400 backdrop-blur-sm",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].p, {
                        className: "text-sm text-cyan-300 font-mono",
                        animate: {
                            textShadow: state.currentAnimation === 'talking' ? [
                                '0 0 5px rgba(34, 211, 238, 0.8)',
                                '0 0 15px rgba(34, 211, 238, 1)',
                                '0 0 5px rgba(34, 211, 238, 0.8)'
                            ] : '0 0 5px rgba(34, 211, 238, 0.6)'
                        },
                        transition: {
                            duration: 0.8,
                            repeat: Infinity
                        },
                        children: [
                            state.currentAnimation === 'idle' && '◦ ALEX ONLINE ◦',
                            state.currentAnimation === 'talking' && '◦ TRANSMITTING ◦',
                            state.currentAnimation === 'listening' && '◦ RECEIVING ◦',
                            state.currentAnimation === 'thinking' && '◦ PROCESSING ◦'
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/Avatar.tsx",
                        lineNumber: 370,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                }, void 0, false, {
                    fileName: "[project]/src/components/Avatar.tsx",
                    lineNumber: 369,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/Avatar.tsx",
                lineNumber: 363,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/Avatar.tsx",
        lineNumber: 87,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = Hologram;
}),
"[project]/src/services/speechService.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "SpeechService",
    ()=>SpeechService,
    "speechService",
    ()=>speechService
]);
class SpeechService {
    mediaRecorder = null;
    audioChunks = [];
    recognition = null;
    synthesis;
    currentUtterance = null;
    currentAudio = null;
    isUsingOpenAITTS = false;
    isSpeaking = false;
    constructor(){
        // Only initialize in browser environment
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
    }
    initializeSpeechRecognition() {
        if ("undefined" !== 'undefined' && ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) //TURBOPACK unreachable
        ;
    }
    // Start listening for speech
    async startListening(onTranscript, onError) {
        if (!this.recognition) {
            onError('Speech recognition not supported');
            return;
        }
        try {
            this.recognition.onresult = (event)=>{
                let transcript = '';
                let isFinal = false;
                for(let i = event.resultIndex; i < event.results.length; i++){
                    const result = event.results[i];
                    transcript += result[0].transcript;
                    if (result.isFinal) {
                        isFinal = true;
                    }
                }
                onTranscript(transcript, isFinal);
            };
            this.recognition.onerror = (event)=>{
                onError(`Speech recognition error: ${event.error}`);
            };
            this.recognition.start();
        } catch (error) {
            onError(`Failed to start speech recognition: ${error}`);
        }
    }
    // Stop listening
    stopListening() {
        if (this.recognition) {
            this.recognition.stop();
        }
    }
    // Convert speech to text using Whisper API
    async transcribeAudio(audioBlob) {
        try {
            const formData = new FormData();
            formData.append('file', audioBlob, 'audio.wav');
            formData.append('model', 'whisper-1');
            const response = await fetch('/api/transcribe', {
                method: 'POST',
                body: formData
            });
            if (!response.ok) {
                throw new Error(`Transcription failed: ${response.statusText}`);
            }
            const result = await response.json();
            return result.text;
        } catch (error) {
            console.error('Transcription error:', error);
            throw error;
        }
    }
    // Text-to-speech using OpenAI TTS API
    async speak(text, onStart, onEnd, onMouthSync) {
        console.log('🎤 SPEAK CALLED:', {
            text: text.substring(0, 50) + '...',
            isSpeaking: this.isSpeaking
        });
        // CRITICAL: Stop any ongoing speech first
        this.stopSpeaking();
        // Set speaking state IMMEDIATELY
        this.isSpeaking = true;
        console.log('🎤 Speaking state set to TRUE');
        try {
            // Try OpenAI TTS first
            this.isUsingOpenAITTS = true;
            console.log('🎤 Attempting OpenAI TTS...');
            // Call onStart immediately
            onStart?.();
            console.log('🎤 onStart called');
            // Generate mouth sync data immediately
            const mouthSyncData = this.generateMouthSyncData(text);
            onMouthSync?.(mouthSyncData);
            console.log('🎤 onMouthSync called');
            // Call OpenAI TTS API
            const response = await fetch('/api/text-to-speech', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    text
                })
            });
            if (!response.ok) {
                console.warn('❌ OpenAI TTS API failed:', response.statusText);
                this.isUsingOpenAITTS = false;
                this.isSpeaking = false; // Reset before fallback
                return this.fallbackSpeak(text, onStart, onEnd, onMouthSync);
            }
            console.log('✅ OpenAI TTS API success, creating audio...');
            // Get audio blob from response
            const audioBlob = await response.blob();
            // CRITICAL: Check if we're still supposed to be speaking
            if (!this.isSpeaking) {
                console.log('🛑 Speaking was cancelled, aborting audio creation');
                return;
            }
            // Create audio element and play
            this.currentAudio = new Audio();
            const audioUrl = URL.createObjectURL(audioBlob);
            this.currentAudio.src = audioUrl;
            this.currentAudio.volume = 0.8;
            return new Promise((resolve, reject)=>{
                if (!this.currentAudio || !this.isSpeaking) {
                    console.log('🛑 Audio creation failed or speaking cancelled');
                    this.isSpeaking = false;
                    this.isUsingOpenAITTS = false;
                    return reject(new Error('Audio creation failed or cancelled'));
                }
                this.currentAudio.onended = ()=>{
                    console.log('✅ OpenAI TTS audio ended normally');
                    URL.revokeObjectURL(audioUrl);
                    this.currentAudio = null;
                    this.isSpeaking = false;
                    this.isUsingOpenAITTS = false;
                    onEnd?.();
                    resolve();
                };
                this.currentAudio.onerror = (e)=>{
                    console.warn('❌ OpenAI TTS audio error:', e);
                    URL.revokeObjectURL(audioUrl);
                    this.currentAudio = null;
                    this.isUsingOpenAITTS = false;
                    this.isSpeaking = false;
                    // Don't fallback on audio error - just fail
                    onEnd?.();
                    reject(new Error('Audio playback failed'));
                };
                console.log('🎵 Starting OpenAI TTS audio playback...');
                this.currentAudio.play().catch((playError)=>{
                    console.warn('❌ OpenAI TTS play error:', playError);
                    URL.revokeObjectURL(audioUrl);
                    this.currentAudio = null;
                    this.isUsingOpenAITTS = false;
                    this.isSpeaking = false;
                    // Don't fallback on play error - just fail
                    onEnd?.();
                    reject(playError);
                });
            });
        } catch (error) {
            console.warn('❌ OpenAI TTS error:', error);
            this.isUsingOpenAITTS = false;
            this.isSpeaking = false;
            // Only fallback on network/API errors, not audio errors
            return this.fallbackSpeak(text, onStart, onEnd, onMouthSync);
        }
    }
    // Fallback to browser speech synthesis
    async fallbackSpeak(text, onStart, onEnd, onMouthSync) {
        console.log('🔄 FALLBACK SPEAK called');
        return new Promise((resolve, reject)=>{
            if (!this.synthesis) {
                console.log('❌ Speech synthesis not supported');
                this.isSpeaking = false;
                reject(new Error('Speech synthesis not supported'));
                return;
            }
            // CRITICAL: Only proceed if OpenAI TTS is not active
            if (this.isUsingOpenAITTS) {
                console.log('🛑 OpenAI TTS is active, aborting browser synthesis');
                this.isSpeaking = false;
                resolve();
                return;
            }
            // Set speaking state for browser synthesis
            this.isSpeaking = true;
            console.log('🎤 Browser synthesis starting...');
            // Cancel any ongoing browser speech
            this.synthesis.cancel();
            this.currentUtterance = new SpeechSynthesisUtterance(text);
            // Configure voice settings
            this.currentUtterance.rate = 0.9;
            this.currentUtterance.pitch = 1.1;
            this.currentUtterance.volume = 0.8;
            // Try to find a good voice
            const voices = this.synthesis.getVoices();
            const preferredVoice = voices.find((voice)=>voice.name.includes('Google') && voice.lang.startsWith('en')) || voices.find((voice)=>voice.lang.startsWith('en'));
            if (preferredVoice) {
                this.currentUtterance.voice = preferredVoice;
            }
            this.currentUtterance.onstart = ()=>{
                console.log('🎵 Browser synthesis started');
                onStart?.();
                if (onMouthSync) {
                    const mouthSyncData = this.generateMouthSyncData(text);
                    onMouthSync(mouthSyncData);
                }
            };
            this.currentUtterance.onend = ()=>{
                console.log('✅ Browser synthesis ended');
                this.isSpeaking = false;
                this.currentUtterance = null;
                onEnd?.();
                resolve();
            };
            this.currentUtterance.onerror = (event)=>{
                console.log('❌ Browser synthesis error:', event.error);
                this.isSpeaking = false;
                this.currentUtterance = null;
                reject(new Error(`Speech synthesis error: ${event.error}`));
            };
            console.log('🎵 Starting browser speech synthesis...');
            this.synthesis.speak(this.currentUtterance);
        });
    }
    // Stop current speech
    stopSpeaking() {
        console.log('🛑 STOP SPEAKING called');
        // Reset flags FIRST
        const wasUsingSpeech = this.isSpeaking;
        this.isSpeaking = false;
        this.isUsingOpenAITTS = false;
        if (wasUsingSpeech) {
            console.log('🛑 Stopping active speech...');
        }
        // Stop OpenAI TTS audio
        if (this.currentAudio) {
            try {
                this.currentAudio.pause();
                this.currentAudio.currentTime = 0;
                this.currentAudio.src = '';
                this.currentAudio.onended = null;
                this.currentAudio.onerror = null;
                this.currentAudio = null;
                console.log('🛑 OpenAI TTS audio stopped');
            } catch (error) {
                console.log('⚠️ Error stopping OpenAI audio:', error);
            }
        }
        // Stop browser speech synthesis
        if (this.synthesis) {
            try {
                this.synthesis.cancel();
                console.log('🛑 Browser speech synthesis cancelled');
            } catch (error) {
                console.log('⚠️ Error stopping browser synthesis:', error);
            }
        }
        // Clear current utterance
        if (this.currentUtterance) {
            this.currentUtterance.onstart = null;
            this.currentUtterance.onend = null;
            this.currentUtterance.onerror = null;
            this.currentUtterance = null;
            console.log('🛑 Current utterance cleared');
        }
        console.log('🛑 All speech stopped');
    }
    // Check if currently speaking
    isSpeakingNow() {
        return this.isSpeaking;
    }
    // Get current speech method
    getCurrentSpeechMethod() {
        if (!this.isSpeaking) return 'none';
        return this.isUsingOpenAITTS ? 'openai' : 'browser';
    }
    // Generate mouth sync data for avatar animation
    generateMouthSyncData(text) {
        const words = text.split(' ');
        const phonemes = [];
        let currentTime = 0;
        const averageWordDuration = 0.6; // seconds per word
        words.forEach((word, index)=>{
            const wordDuration = averageWordDuration * (word.length / 5); // Adjust based on word length
            // Simple phoneme mapping (in a real app, you'd use a proper phoneme library)
            const wordPhonemes = this.mapWordToPhonemes(word);
            const phonemeDuration = wordDuration / wordPhonemes.length;
            wordPhonemes.forEach((phoneme, pIndex)=>{
                phonemes.push({
                    phoneme,
                    start: currentTime + pIndex * phonemeDuration,
                    end: currentTime + (pIndex + 1) * phonemeDuration,
                    intensity: this.getPhonemeIntensity(phoneme)
                });
            });
            currentTime += wordDuration + 0.1; // Small pause between words
        });
        return {
            phonemes,
            duration: currentTime,
            currentTime: 0
        };
    }
    // Simple phoneme mapping (simplified for demo)
    mapWordToPhonemes(word) {
        // This is a very simplified mapping. In production, use a proper phoneme library
        const vowels = [
            'a',
            'e',
            'i',
            'o',
            'u'
        ];
        const phonemes = [];
        for(let i = 0; i < word.length; i++){
            const char = word[i].toLowerCase();
            if (vowels.includes(char)) {
                phonemes.push('open'); // Open mouth for vowels
            } else if (char === 'm' || char === 'p' || char === 'b') {
                phonemes.push('closed'); // Closed mouth for bilabials
            } else {
                phonemes.push('mid'); // Mid position for other consonants
            }
        }
        return phonemes.length > 0 ? phonemes : [
            'mid'
        ];
    }
    // Get intensity for mouth animation
    getPhonemeIntensity(phoneme) {
        switch(phoneme){
            case 'open':
                return 0.8;
            case 'closed':
                return 0.1;
            case 'mid':
                return 0.5;
            default:
                return 0.5;
        }
    }
    // Record audio for Whisper transcription
    async startRecording() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: true
            });
            this.mediaRecorder = new MediaRecorder(stream);
            this.audioChunks = [];
            this.mediaRecorder.ondataavailable = (event)=>{
                this.audioChunks.push(event.data);
            };
            this.mediaRecorder.start();
        } catch (error) {
            throw new Error(`Failed to start recording: ${error}`);
        }
    }
    async stopRecording() {
        return new Promise((resolve, reject)=>{
            if (!this.mediaRecorder) {
                reject(new Error('No active recording'));
                return;
            }
            this.mediaRecorder.onstop = ()=>{
                const audioBlob = new Blob(this.audioChunks, {
                    type: 'audio/wav'
                });
                resolve(audioBlob);
            };
            this.mediaRecorder.stop();
            // Stop all tracks to release microphone
            this.mediaRecorder.stream.getTracks().forEach((track)=>track.stop());
        });
    }
    // Check if speech services are available
    isAvailable() {
        return "undefined" !== 'undefined' && !!(this.recognition && this.synthesis);
    }
}
const speechService = ("TURBOPACK compile-time falsy", 0) ? "TURBOPACK unreachable" : null;
}),
"[project]/src/services/userProfileService.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "UserProfileService",
    ()=>UserProfileService
]);
class UserProfileService {
    static STORAGE_KEY = 'ai_trainer_profiles';
    static CURRENT_USER_KEY = 'ai_trainer_current_user';
    // Create or get user profile
    static async createOrGetProfile(name, email) {
        const profiles = this.getAllProfiles();
        // Try to find existing profile by name or email
        let existingProfile = profiles.find((p)=>p.name.toLowerCase() === name.toLowerCase() || email && p.email?.toLowerCase() === email.toLowerCase());
        if (existingProfile) {
            // Update last active time
            existingProfile.lastActiveAt = new Date();
            this.saveProfile(existingProfile);
            this.setCurrentUser(existingProfile.id);
            return existingProfile;
        }
        // Create new profile
        const newProfile = {
            id: this.generateUserId(),
            name,
            email,
            createdAt: new Date(),
            lastActiveAt: new Date(),
            totalSessions: 0,
            goals: [],
            painPoints: [],
            motivations: [],
            emotionalTriggers: [],
            assessmentHistory: [],
            conversationHistory: [],
            progressTracking: [],
            preferences: {
                preferredWorkoutTime: '18:00',
                trainerPersonality: 'supportive_coach',
                reminderSettings: {
                    workouts: true,
                    nutrition: true,
                    checkIns: true
                },
                voiceSettings: {
                    speed: 1.0,
                    volume: 0.8,
                    preferredVoice: 'nova'
                },
                privacySettings: {
                    shareProgress: false,
                    allowAnalytics: true
                }
            }
        };
        this.saveProfile(newProfile);
        this.setCurrentUser(newProfile.id);
        return newProfile;
    }
    // Get current user profile
    static getCurrentProfile() {
        const currentUserId = localStorage.getItem(this.CURRENT_USER_KEY);
        if (!currentUserId) return null;
        const profiles = this.getAllProfiles();
        return profiles.find((p)=>p.id === currentUserId) || null;
    }
    // Update user profile
    static updateProfile(updates) {
        const currentProfile = this.getCurrentProfile();
        if (!currentProfile) return;
        const updatedProfile = {
            ...currentProfile,
            ...updates,
            lastActiveAt: new Date()
        };
        this.saveProfile(updatedProfile);
    }
    // Add conversation message
    static addConversationMessage(message) {
        const profile = this.getCurrentProfile();
        if (!profile) return;
        profile.conversationHistory.push(message);
        // Keep only last 100 messages to prevent storage bloat
        if (profile.conversationHistory.length > 100) {
            profile.conversationHistory = profile.conversationHistory.slice(-100);
        }
        this.saveProfile(profile);
    }
    // Add assessment session
    static addAssessmentSession(session) {
        const profile = this.getCurrentProfile();
        if (!profile) return;
        profile.assessmentHistory.push(session);
        profile.totalSessions += 1;
        // Update profile with session insights
        if (session.userProfile) {
            profile.goals = [
                ...new Set([
                    ...profile.goals,
                    ...session.userProfile.goals
                ])
            ];
            profile.painPoints = [
                ...new Set([
                    ...profile.painPoints,
                    ...session.userProfile.painPoints
                ])
            ];
            profile.motivations = [
                ...new Set([
                    ...profile.motivations,
                    ...session.userProfile.motivations
                ])
            ];
            profile.emotionalTriggers = [
                ...new Set([
                    ...profile.emotionalTriggers,
                    ...session.userProfile.emotionalTriggers
                ])
            ];
            if (session.userProfile.fitnessLevel) {
                profile.fitnessLevel = session.userProfile.fitnessLevel;
            }
            if (session.userProfile.preferredStyle) {
                profile.preferredStyle = session.userProfile.preferredStyle;
            }
        }
        this.saveProfile(profile);
    }
    // Add progress entry
    static addProgressEntry(entry) {
        const profile = this.getCurrentProfile();
        if (!profile) return;
        const progressEntry = {
            ...entry,
            id: this.generateProgressId()
        };
        profile.progressTracking.push(progressEntry);
        this.saveProfile(profile);
    }
    // Get conversation context for AI
    static getConversationContext(limit = 10) {
        const profile = this.getCurrentProfile();
        if (!profile) return [];
        return profile.conversationHistory.slice(-limit);
    }
    // Get user summary for AI context
    static getUserSummaryForAI() {
        const profile = this.getCurrentProfile();
        if (!profile) return '';
        const recentProgress = profile.progressTracking.slice(-3);
        const lastSession = profile.assessmentHistory[profile.assessmentHistory.length - 1];
        let summary = `User Profile Summary for ${profile.name}:\n`;
        summary += `- Member since: ${profile.createdAt.toDateString()}\n`;
        summary += `- Total sessions: ${profile.totalSessions}\n`;
        summary += `- Fitness level: ${profile.fitnessLevel || 'Not assessed'}\n`;
        summary += `- Primary goals: ${profile.goals.join(', ') || 'Not specified'}\n`;
        summary += `- Main pain points: ${profile.painPoints.join(', ') || 'None identified'}\n`;
        summary += `- Key motivations: ${profile.motivations.join(', ') || 'Not specified'}\n`;
        summary += `- Preferred coaching style: ${profile.preferredStyle || 'Not specified'}\n`;
        if (recentProgress.length > 0) {
            summary += `- Recent progress: ${recentProgress.length} entries in tracking\n`;
            const latest = recentProgress[recentProgress.length - 1];
            if (latest.weight) summary += `- Current weight: ${latest.weight} lbs\n`;
            if (latest.mood) summary += `- Recent mood: ${latest.mood}\n`;
        }
        if (lastSession) {
            summary += `- Last assessment: ${lastSession.startedAt.toDateString()}\n`;
            summary += `- Last phase completed: ${lastSession.phase}\n`;
        }
        return summary;
    }
    // Private helper methods
    static getAllProfiles() {
        try {
            const stored = localStorage.getItem(this.STORAGE_KEY);
            return stored ? JSON.parse(stored, this.dateReviver) : [];
        } catch (error) {
            console.error('Error loading profiles:', error);
            return [];
        }
    }
    static saveProfile(profile) {
        try {
            const profiles = this.getAllProfiles();
            const index = profiles.findIndex((p)=>p.id === profile.id);
            if (index >= 0) {
                profiles[index] = profile;
            } else {
                profiles.push(profile);
            }
            localStorage.setItem(this.STORAGE_KEY, JSON.stringify(profiles));
        } catch (error) {
            console.error('Error saving profile:', error);
        }
    }
    static setCurrentUser(userId) {
        localStorage.setItem(this.CURRENT_USER_KEY, userId);
    }
    static generateUserId() {
        return `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    static generateProgressId() {
        return `progress_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    // Date reviver for JSON.parse to handle Date objects
    static dateReviver(key, value) {
        if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(value)) {
            return new Date(value);
        }
        return value;
    }
    // Export/Import functionality for data portability
    static exportUserData() {
        const profile = this.getCurrentProfile();
        if (!profile) throw new Error('No current user profile');
        return JSON.stringify(profile, null, 2);
    }
    static importUserData(jsonData) {
        try {
            const profile = JSON.parse(jsonData, this.dateReviver);
            this.saveProfile(profile);
            this.setCurrentUser(profile.id);
        } catch (error) {
            throw new Error('Invalid user data format');
        }
    }
    // Clear all data (for privacy/reset)
    static clearAllData() {
        localStorage.removeItem(this.STORAGE_KEY);
        localStorage.removeItem(this.CURRENT_USER_KEY);
    }
    // Get user statistics
    static getUserStats() {
        const profile = this.getCurrentProfile();
        if (!profile) {
            return {
                totalConversations: 0,
                totalSessions: 0,
                memberSince: new Date(),
                progressEntries: 0,
                currentStreak: 0
            };
        }
        // Calculate current streak (days with activity)
        const now = new Date();
        let currentStreak = 0;
        const sortedProgress = profile.progressTracking.sort((a, b)=>b.date.getTime() - a.date.getTime());
        for (const entry of sortedProgress){
            const daysDiff = Math.floor((now.getTime() - entry.date.getTime()) / (1000 * 60 * 60 * 24));
            if (daysDiff <= currentStreak + 1) {
                currentStreak++;
            } else {
                break;
            }
        }
        return {
            totalConversations: profile.conversationHistory.length,
            totalSessions: profile.totalSessions,
            memberSince: profile.createdAt,
            progressEntries: profile.progressTracking.length,
            currentStreak
        };
    }
}
}),
"[project]/src/components/AssessmentInterface.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "AssessmentInterface",
    ()=>AssessmentInterface,
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$mic$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Mic$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/mic.js [app-ssr] (ecmascript) <export default as Mic>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$mic$2d$off$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MicOff$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/mic-off.js [app-ssr] (ecmascript) <export default as MicOff>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$volume$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Volume2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/volume-2.js [app-ssr] (ecmascript) <export default as Volume2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$volume$2d$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__VolumeX$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/volume-x.js [app-ssr] (ecmascript) <export default as VolumeX>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Avatar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/Avatar.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$speechService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/speechService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userProfileService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/userProfileService.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
;
const AssessmentInterface = ({ userName = 'there', userEmail, personalityId, onPhaseChange, onInsights, isGeneralChat = false })=>{
    // State management
    const [currentPhase, setCurrentPhase] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('warm_welcome');
    const [currentSubPhase, setCurrentSubPhase] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('surface_level');
    const [userProfile, setUserProfile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [persistentProfile, setPersistentProfile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [conversation, setConversation] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [speechState, setSpeechState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        isListening: false,
        isProcessing: false,
        isSpeaking: false,
        transcript: '',
        confidence: 0
    });
    const [avatarState, setAvatarState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        isAnimating: false,
        currentAnimation: 'idle'
    });
    const [isAudioEnabled, setIsAudioEnabled] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [currentTranscript, setCurrentTranscript] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const [sessionId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(()=>`session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
    const [isReturningUser, setIsReturningUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Initialize user profile and welcome message
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const initializeUser = async ()=>{
            try {
                // Load or create user profile
                const profile = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userProfileService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserProfileService"].createOrGetProfile(userName, userEmail);
                setPersistentProfile(profile);
                setIsReturningUser(profile.totalSessions > 0);
                // Load previous conversation context if returning user
                if (profile.totalSessions > 0 && !isGeneralChat) {
                    const recentMessages = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userProfileService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserProfileService"].getConversationContext(3);
                    if (recentMessages.length > 0) {
                        setConversation(recentMessages);
                    }
                }
                // Create appropriate welcome message
                let welcomeContent;
                if (isGeneralChat) {
                    welcomeContent = `Hey ${userName}! Great to see you again! I'm here and ready to help with any fitness questions you have. What's on your mind today?`;
                } else if (profile.totalSessions > 0) {
                    const lastGoals = profile.goals.slice(-2).join(' and ') || 'your fitness goals';
                    welcomeContent = `Hey ${userName}! So great to see you back! I've been thinking about your ${lastGoals} since we last talked. How have you been feeling? Ready to continue your transformation journey?`;
                } else {
                    welcomeContent = `Hey ${userName}! I'm absolutely thrilled to meet you! I'm Alex, and I'm here to be your personal trainer, your biggest cheerleader, and maybe occasionally your gentle drill sergeant when you need that extra push. This entire session is about YOU - your goals, your dreams, and creating a plan that's going to transform how you look and feel. I can already tell you're serious about making a change, and that's exactly the mindset that leads to incredible results! So tell me, ${userName}, what brought you here today? What's got you excited about starting this fitness journey?`;
                }
                const welcomeMessage = {
                    id: '1',
                    role: 'trainer',
                    content: welcomeContent,
                    timestamp: new Date()
                };
                setConversation((prev)=>prev.length === 0 ? [
                        welcomeMessage
                    ] : prev);
                // Speak the welcome message
                if (isAudioEnabled) {
                    speakMessage(welcomeContent);
                }
                // Save welcome message to profile
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userProfileService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserProfileService"].addConversationMessage(welcomeMessage);
            } catch (error) {
                console.error('Error initializing user:', error);
            }
        };
        initializeUser();
    }, [
        userName,
        userEmail,
        isAudioEnabled,
        isGeneralChat
    ]);
    // Handle speech synthesis
    const speakMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (text)=>{
        if (!isAudioEnabled || !__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$speechService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["speechService"]) return;
        setAvatarState((prev)=>({
                ...prev,
                currentAnimation: 'talking'
            }));
        setSpeechState((prev)=>({
                ...prev,
                isSpeaking: true
            }));
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$speechService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["speechService"].speak(text, ()=>{
                // On start
                setAvatarState((prev)=>({
                        ...prev,
                        currentAnimation: 'talking'
                    }));
            }, ()=>{
                // On end
                setAvatarState((prev)=>({
                        ...prev,
                        currentAnimation: 'idle'
                    }));
                setSpeechState((prev)=>({
                        ...prev,
                        isSpeaking: false
                    }));
            }, (mouthSyncData)=>{
                // On mouth sync
                setAvatarState((prev)=>({
                        ...prev,
                        mouthSyncData: {
                            ...mouthSyncData,
                            currentTime: Date.now()
                        }
                    }));
            });
        } catch (error) {
            console.error('Speech synthesis error:', error);
            setAvatarState((prev)=>({
                    ...prev,
                    currentAnimation: 'idle'
                }));
            setSpeechState((prev)=>({
                    ...prev,
                    isSpeaking: false
                }));
        }
    }, [
        isAudioEnabled
    ]);
    // Handle voice input
    const startListening = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        if (speechState.isListening || speechState.isSpeaking || !__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$speechService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["speechService"]) return;
        setAvatarState((prev)=>({
                ...prev,
                currentAnimation: 'listening'
            }));
        setSpeechState((prev)=>({
                ...prev,
                isListening: true,
                transcript: ''
            }));
        setCurrentTranscript('');
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$speechService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["speechService"].startListening((transcript, isFinal)=>{
                setCurrentTranscript(transcript);
                setSpeechState((prev)=>({
                        ...prev,
                        transcript,
                        confidence: isFinal ? 1 : 0.5
                    }));
                if (isFinal && transcript.trim()) {
                    handleUserMessage(transcript.trim());
                }
            }, (error)=>{
                console.error('Speech recognition error:', error);
                stopListening();
            });
        } catch (error) {
            console.error('Failed to start listening:', error);
            stopListening();
        }
    }, [
        speechState.isListening,
        speechState.isSpeaking
    ]);
    const stopListening = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        if (__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$speechService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["speechService"]) {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$speechService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["speechService"].stopListening();
        }
        setAvatarState((prev)=>({
                ...prev,
                currentAnimation: 'idle'
            }));
        setSpeechState((prev)=>({
                ...prev,
                isListening: false,
                isProcessing: false
            }));
    }, []);
    // Handle user message
    const handleUserMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (message)=>{
        if (!message.trim()) return;
        stopListening();
        setSpeechState((prev)=>({
                ...prev,
                isProcessing: true
            }));
        setAvatarState((prev)=>({
                ...prev,
                currentAnimation: 'thinking'
            }));
        // Add user message to conversation
        const userMessage = {
            id: Date.now().toString(),
            role: 'user',
            content: message,
            timestamp: new Date()
        };
        setConversation((prev)=>[
                ...prev,
                userMessage
            ]);
        // Save user message to persistent storage
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userProfileService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserProfileService"].addConversationMessage(userMessage);
        try {
            // Determine if this is a general question or assessment
            const apiEndpoint = isGeneralChat ? '/api/general-fitness-chat' : '/api/trainer-chat';
            // Send to AI trainer with comprehensive context
            const response = await fetch(apiEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message,
                    phase: currentPhase,
                    subPhase: currentSubPhase,
                    conversationHistory: conversation,
                    userProfile: {
                        name: userName,
                        email: userEmail,
                        sessionId,
                        subPhase: currentSubPhase,
                        isReturningUser,
                        personalityId: personalityId || persistentProfile?.preferences?.trainerPersonality,
                        ...userProfile,
                        ...persistentProfile
                    }
                })
            });
            if (!response.ok) {
                throw new Error('Failed to get trainer response');
            }
            const data = await response.json();
            // Add trainer response to conversation
            const trainerMessage = {
                id: (Date.now() + 1).toString(),
                role: 'trainer',
                content: data.response,
                timestamp: new Date()
            };
            setConversation((prev)=>[
                    ...prev,
                    trainerMessage
                ]);
            // Save trainer message to persistent storage
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userProfileService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserProfileService"].addConversationMessage(trainerMessage);
            // Handle insights
            if (data.insights && onInsights) {
                onInsights(data.insights);
            }
            // Update user profile if provided
            if (data.userProfile) {
                setUserProfile(data.userProfile);
            }
            // Handle phase transitions (only for assessment mode)
            if (!isGeneralChat) {
                if (data.shouldTransition && data.nextPhase) {
                    setCurrentPhase(data.nextPhase);
                    onPhaseChange?.(data.nextPhase);
                }
                // Update sub-phase if provided
                if (data.currentSubPhase) {
                    setCurrentSubPhase(data.currentSubPhase);
                }
            }
            // Speak the response
            await speakMessage(data.response);
        } catch (error) {
            console.error('Error getting trainer response:', error);
            const errorMessage = {
                id: (Date.now() + 1).toString(),
                role: 'trainer',
                content: "I'm sorry, I had a technical hiccup there. Could you repeat that?",
                timestamp: new Date()
            };
            setConversation((prev)=>[
                    ...prev,
                    errorMessage
                ]);
            await speakMessage(errorMessage.content);
        } finally{
            setSpeechState((prev)=>({
                    ...prev,
                    isProcessing: false
                }));
            setAvatarState((prev)=>({
                    ...prev,
                    currentAnimation: 'idle'
                }));
        }
    }, [
        conversation,
        currentPhase,
        userName,
        onInsights,
        speakMessage,
        stopListening
    ]);
    // Get phase display name
    const getPhaseDisplayName = (phase, subPhase)=>{
        switch(phase){
            case 'warm_welcome':
                return 'WARM WELCOME & RAPPORT BUILDING';
            case 'deep_discovery':
                switch(subPhase){
                    case 'surface_level':
                        return 'DISCOVERY - SURFACE LEVEL';
                    case 'pain_points':
                        return 'DISCOVERY - PAIN POINTS';
                    case 'emotional_drivers':
                        return 'DISCOVERY - EMOTIONAL DRIVERS';
                    case 'support_style':
                        return 'DISCOVERY - SUPPORT STYLE';
                    default:
                        return 'DEEP DISCOVERY & EMOTIONAL CONNECTION';
                }
            case 'physical_assessment':
                return 'LIVE PHYSICAL ASSESSMENT';
            case 'vision_reveal':
                return 'VISION REVEAL & FUTURE PROJECTION';
            case 'service_recommendation':
                return 'NATURAL SERVICE RECOMMENDATION';
            case 'completed':
                return 'ASSESSMENT COMPLETED';
            default:
                return phase.toUpperCase().replace('_', ' ');
        }
    };
    // Toggle audio
    const toggleAudio = ()=>{
        setIsAudioEnabled(!isAudioEnabled);
        if (speechState.isSpeaking && __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$speechService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["speechService"]) {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$speechService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["speechService"].stopSpeaking();
            setAvatarState((prev)=>({
                    ...prev,
                    currentAnimation: 'idle'
                }));
            setSpeechState((prev)=>({
                    ...prev,
                    isSpeaking: false
                }));
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-4",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-8",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Avatar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                    state: avatarState,
                    size: "large"
                }, void 0, false, {
                    fileName: "[project]/src/components/AssessmentInterface.tsx",
                    lineNumber: 341,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/AssessmentInterface.tsx",
                lineNumber: 340,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-full max-w-2xl mb-6",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-black bg-opacity-50 rounded-lg shadow-lg border border-cyan-400 p-6 max-h-60 overflow-y-auto backdrop-blur-sm",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AnimatePresence"], {
                        children: conversation.slice(-3).map((message)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                                initial: {
                                    opacity: 0,
                                    y: 20
                                },
                                animate: {
                                    opacity: 1,
                                    y: 0
                                },
                                exit: {
                                    opacity: 0,
                                    y: -20
                                },
                                className: `mb-4 ${message.role === 'trainer' ? 'text-left' : 'text-right'}`,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: `inline-block p-3 rounded-lg max-w-xs border ${message.role === 'trainer' ? 'bg-cyan-900 bg-opacity-50 text-cyan-100 border-cyan-400' : 'bg-purple-900 bg-opacity-50 text-purple-100 border-purple-400'}`,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm font-medium mb-1 font-mono",
                                            children: message.role === 'trainer' ? '> ALEX' : '> YOU'
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/AssessmentInterface.tsx",
                                            lineNumber: 365,
                                            columnNumber: 19
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm",
                                            children: message.content
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/AssessmentInterface.tsx",
                                            lineNumber: 368,
                                            columnNumber: 19
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/AssessmentInterface.tsx",
                                    lineNumber: 358,
                                    columnNumber: 17
                                }, ("TURBOPACK compile-time value", void 0))
                            }, message.id, false, {
                                fileName: "[project]/src/components/AssessmentInterface.tsx",
                                lineNumber: 349,
                                columnNumber: 15
                            }, ("TURBOPACK compile-time value", void 0)))
                    }, void 0, false, {
                        fileName: "[project]/src/components/AssessmentInterface.tsx",
                        lineNumber: 347,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                }, void 0, false, {
                    fileName: "[project]/src/components/AssessmentInterface.tsx",
                    lineNumber: 346,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/AssessmentInterface.tsx",
                lineNumber: 345,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            currentTranscript && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                initial: {
                    opacity: 0
                },
                animate: {
                    opacity: 1
                },
                className: "mb-4 p-3 bg-green-900 bg-opacity-50 rounded-lg border border-green-400 backdrop-blur-sm",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-sm text-green-300 font-mono",
                    children: `> RECEIVING: "${currentTranscript}"`
                }, void 0, false, {
                    fileName: "[project]/src/components/AssessmentInterface.tsx",
                    lineNumber: 383,
                    columnNumber: 11
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/AssessmentInterface.tsx",
                lineNumber: 378,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex space-x-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].button, {
                        whileHover: {
                            scale: 1.05
                        },
                        whileTap: {
                            scale: 0.95
                        },
                        onClick: speechState.isListening ? stopListening : startListening,
                        disabled: speechState.isSpeaking || speechState.isProcessing,
                        className: `p-4 rounded-full shadow-lg transition-colors border-2 ${speechState.isListening ? 'bg-red-500 text-white border-red-400 shadow-red-400/50' : speechState.isSpeaking || speechState.isProcessing ? 'bg-gray-600 text-gray-400 cursor-not-allowed border-gray-500' : 'bg-cyan-500 text-white hover:bg-cyan-600 border-cyan-400 shadow-cyan-400/50'}`,
                        style: {
                            boxShadow: speechState.isListening ? '0 0 20px rgba(239, 68, 68, 0.5)' : '0 0 20px rgba(34, 211, 238, 0.5)'
                        },
                        children: speechState.isListening ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$mic$2d$off$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MicOff$3e$__["MicOff"], {
                            size: 24
                        }, void 0, false, {
                            fileName: "[project]/src/components/AssessmentInterface.tsx",
                            lineNumber: 410,
                            columnNumber: 38
                        }, ("TURBOPACK compile-time value", void 0)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$mic$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Mic$3e$__["Mic"], {
                            size: 24
                        }, void 0, false, {
                            fileName: "[project]/src/components/AssessmentInterface.tsx",
                            lineNumber: 410,
                            columnNumber: 61
                        }, ("TURBOPACK compile-time value", void 0))
                    }, void 0, false, {
                        fileName: "[project]/src/components/AssessmentInterface.tsx",
                        lineNumber: 392,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].button, {
                        whileHover: {
                            scale: 1.05
                        },
                        whileTap: {
                            scale: 0.95
                        },
                        onClick: toggleAudio,
                        className: `p-4 rounded-full shadow-lg transition-colors border-2 ${isAudioEnabled ? 'bg-green-500 text-white hover:bg-green-600 border-green-400 shadow-green-400/50' : 'bg-gray-600 text-white hover:bg-gray-700 border-gray-500 shadow-gray-500/50'}`,
                        style: {
                            boxShadow: isAudioEnabled ? '0 0 20px rgba(34, 197, 94, 0.5)' : '0 0 20px rgba(107, 114, 128, 0.5)'
                        },
                        children: isAudioEnabled ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$volume$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Volume2$3e$__["Volume2"], {
                            size: 24
                        }, void 0, false, {
                            fileName: "[project]/src/components/AssessmentInterface.tsx",
                            lineNumber: 429,
                            columnNumber: 29
                        }, ("TURBOPACK compile-time value", void 0)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$volume$2d$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__VolumeX$3e$__["VolumeX"], {
                            size: 24
                        }, void 0, false, {
                            fileName: "[project]/src/components/AssessmentInterface.tsx",
                            lineNumber: 429,
                            columnNumber: 53
                        }, ("TURBOPACK compile-time value", void 0))
                    }, void 0, false, {
                        fileName: "[project]/src/components/AssessmentInterface.tsx",
                        lineNumber: 414,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/AssessmentInterface.tsx",
                lineNumber: 390,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-4 text-center",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-black bg-opacity-50 px-4 py-2 rounded-lg border border-cyan-400 backdrop-blur-sm",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-sm text-cyan-300 font-mono",
                            children: [
                                speechState.isSpeaking && '> ALEX TRANSMITTING...',
                                speechState.isListening && '> LISTENING FOR INPUT...',
                                speechState.isProcessing && '> PROCESSING MESSAGE...',
                                !speechState.isSpeaking && !speechState.isListening && !speechState.isProcessing && '> CLICK MICROPHONE TO RESPOND'
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/AssessmentInterface.tsx",
                            lineNumber: 436,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-xs text-purple-300 mt-1 font-mono",
                            children: [
                                "PHASE: ",
                                getPhaseDisplayName(currentPhase, currentSubPhase)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/AssessmentInterface.tsx",
                            lineNumber: 443,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/AssessmentInterface.tsx",
                    lineNumber: 435,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/AssessmentInterface.tsx",
                lineNumber: 434,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/AssessmentInterface.tsx",
        lineNumber: 338,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = AssessmentInterface;
}),
"[project]/src/components/UserDashboard.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "UserDashboard",
    ()=>UserDashboard,
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/user.js [app-ssr] (ecmascript) <export default as User>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$target$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Target$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/target.js [app-ssr] (ecmascript) <export default as Target>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trending-up.js [app-ssr] (ecmascript) <export default as TrendingUp>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/calendar.js [app-ssr] (ecmascript) <export default as Calendar>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$message$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MessageCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/message-circle.js [app-ssr] (ecmascript) <export default as MessageCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userProfileService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/userProfileService.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
const UserDashboard = ({ onStartChat, onStartAssessment, className = '' })=>{
    const [profile, setProfile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [stats, setStats] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        loadUserData();
    }, []);
    const loadUserData = ()=>{
        try {
            const currentProfile = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userProfileService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserProfileService"].getCurrentProfile();
            const userStats = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userProfileService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserProfileService"].getUserStats();
            setProfile(currentProfile);
            setStats(userStats);
        } catch (error) {
            console.error('Error loading user data:', error);
        } finally{
            setLoading(false);
        }
    };
    if (loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-center min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-cyan-300 font-mono",
                children: "Loading your profile..."
            }, void 0, false, {
                fileName: "[project]/src/components/UserDashboard.tsx",
                lineNumber: 44,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        }, void 0, false, {
            fileName: "[project]/src/components/UserDashboard.tsx",
            lineNumber: 43,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0));
    }
    if (!profile) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-center min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "text-2xl font-bold text-cyan-300 mb-4 font-mono",
                        children: "Welcome to AI Trainer"
                    }, void 0, false, {
                        fileName: "[project]/src/components/UserDashboard.tsx",
                        lineNumber: 53,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-cyan-100 mb-6",
                        children: "Let's get you started with your fitness journey!"
                    }, void 0, false, {
                        fileName: "[project]/src/components/UserDashboard.tsx",
                        lineNumber: 54,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: onStartAssessment,
                        className: "bg-cyan-600 text-white py-3 px-6 rounded-lg hover:bg-cyan-700 transition-colors font-mono",
                        children: "START YOUR ASSESSMENT"
                    }, void 0, false, {
                        fileName: "[project]/src/components/UserDashboard.tsx",
                        lineNumber: 55,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/UserDashboard.tsx",
                lineNumber: 52,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        }, void 0, false, {
            fileName: "[project]/src/components/UserDashboard.tsx",
            lineNumber: 51,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0));
    }
    const getGreeting = ()=>{
        const hour = new Date().getHours();
        if (hour < 12) return 'Good morning';
        if (hour < 18) return 'Good afternoon';
        return 'Good evening';
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-4 ${className}`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "max-w-6xl mx-auto",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                    className: "text-center mb-8",
                    initial: {
                        opacity: 0,
                        y: -20
                    },
                    animate: {
                        opacity: 1,
                        y: 0
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-4xl font-bold text-cyan-300 mb-2 font-mono",
                            children: [
                                getGreeting(),
                                ", ",
                                profile.name,
                                "!"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/UserDashboard.tsx",
                            lineNumber: 82,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-cyan-100 text-lg",
                            children: "Ready to continue your transformation journey?"
                        }, void 0, false, {
                            fileName: "[project]/src/components/UserDashboard.tsx",
                            lineNumber: 85,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/UserDashboard.tsx",
                    lineNumber: 77,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "grid md:grid-cols-2 gap-6 mb-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].button, {
                            onClick: onStartChat,
                            className: "bg-black bg-opacity-50 rounded-lg border border-cyan-400 p-6 backdrop-blur-sm hover:bg-opacity-70 transition-all",
                            whileHover: {
                                scale: 1.02
                            },
                            whileTap: {
                                scale: 0.98
                            },
                            initial: {
                                opacity: 0,
                                x: -20
                            },
                            animate: {
                                opacity: 1,
                                x: 0
                            },
                            transition: {
                                delay: 0.1
                            },
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$message$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MessageCircle$3e$__["MessageCircle"], {
                                    className: "w-8 h-8 text-cyan-400 mb-4 mx-auto"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/UserDashboard.tsx",
                                    lineNumber: 101,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-xl font-bold text-cyan-300 mb-2 font-mono",
                                    children: "CHAT WITH ALEX"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/UserDashboard.tsx",
                                    lineNumber: 102,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-cyan-100 text-sm",
                                    children: "Ask any fitness question and get personalized advice based on your profile"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/UserDashboard.tsx",
                                    lineNumber: 103,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/UserDashboard.tsx",
                            lineNumber: 92,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].button, {
                            onClick: onStartAssessment,
                            className: "bg-black bg-opacity-50 rounded-lg border border-purple-400 p-6 backdrop-blur-sm hover:bg-opacity-70 transition-all",
                            whileHover: {
                                scale: 1.02
                            },
                            whileTap: {
                                scale: 0.98
                            },
                            initial: {
                                opacity: 0,
                                x: 20
                            },
                            animate: {
                                opacity: 1,
                                x: 0
                            },
                            transition: {
                                delay: 0.2
                            },
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$target$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Target$3e$__["Target"], {
                                    className: "w-8 h-8 text-purple-400 mb-4 mx-auto"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/UserDashboard.tsx",
                                    lineNumber: 117,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-xl font-bold text-purple-300 mb-2 font-mono",
                                    children: "NEW ASSESSMENT"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/UserDashboard.tsx",
                                    lineNumber: 118,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-purple-100 text-sm",
                                    children: "Update your goals and get a fresh personalized training plan"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/UserDashboard.tsx",
                                    lineNumber: 119,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/UserDashboard.tsx",
                            lineNumber: 108,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/UserDashboard.tsx",
                    lineNumber: 91,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "grid md:grid-cols-4 gap-4 mb-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                            className: "bg-black bg-opacity-50 rounded-lg border border-cyan-400 p-4 backdrop-blur-sm text-center",
                            initial: {
                                opacity: 0,
                                y: 20
                            },
                            animate: {
                                opacity: 1,
                                y: 0
                            },
                            transition: {
                                delay: 0.3
                            },
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__["Calendar"], {
                                    className: "w-6 h-6 text-cyan-400 mb-2 mx-auto"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/UserDashboard.tsx",
                                    lineNumber: 133,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-2xl font-bold text-cyan-300 font-mono",
                                    children: stats?.totalSessions || 0
                                }, void 0, false, {
                                    fileName: "[project]/src/components/UserDashboard.tsx",
                                    lineNumber: 134,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-xs text-cyan-100",
                                    children: "Sessions"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/UserDashboard.tsx",
                                    lineNumber: 135,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/UserDashboard.tsx",
                            lineNumber: 127,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                            className: "bg-black bg-opacity-50 rounded-lg border border-green-400 p-4 backdrop-blur-sm text-center",
                            initial: {
                                opacity: 0,
                                y: 20
                            },
                            animate: {
                                opacity: 1,
                                y: 0
                            },
                            transition: {
                                delay: 0.4
                            },
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$message$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MessageCircle$3e$__["MessageCircle"], {
                                    className: "w-6 h-6 text-green-400 mb-2 mx-auto"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/UserDashboard.tsx",
                                    lineNumber: 144,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-2xl font-bold text-green-300 font-mono",
                                    children: stats?.totalConversations || 0
                                }, void 0, false, {
                                    fileName: "[project]/src/components/UserDashboard.tsx",
                                    lineNumber: 145,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-xs text-green-100",
                                    children: "Conversations"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/UserDashboard.tsx",
                                    lineNumber: 146,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/UserDashboard.tsx",
                            lineNumber: 138,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                            className: "bg-black bg-opacity-50 rounded-lg border border-yellow-400 p-4 backdrop-blur-sm text-center",
                            initial: {
                                opacity: 0,
                                y: 20
                            },
                            animate: {
                                opacity: 1,
                                y: 0
                            },
                            transition: {
                                delay: 0.5
                            },
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__["TrendingUp"], {
                                    className: "w-6 h-6 text-yellow-400 mb-2 mx-auto"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/UserDashboard.tsx",
                                    lineNumber: 155,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-2xl font-bold text-yellow-300 font-mono",
                                    children: stats?.progressEntries || 0
                                }, void 0, false, {
                                    fileName: "[project]/src/components/UserDashboard.tsx",
                                    lineNumber: 156,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-xs text-yellow-100",
                                    children: "Progress Entries"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/UserDashboard.tsx",
                                    lineNumber: 157,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/UserDashboard.tsx",
                            lineNumber: 149,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                            className: "bg-black bg-opacity-50 rounded-lg border border-orange-400 p-4 backdrop-blur-sm text-center",
                            initial: {
                                opacity: 0,
                                y: 20
                            },
                            animate: {
                                opacity: 1,
                                y: 0
                            },
                            transition: {
                                delay: 0.6
                            },
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$target$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Target$3e$__["Target"], {
                                    className: "w-6 h-6 text-orange-400 mb-2 mx-auto"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/UserDashboard.tsx",
                                    lineNumber: 166,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-2xl font-bold text-orange-300 font-mono",
                                    children: stats?.currentStreak || 0
                                }, void 0, false, {
                                    fileName: "[project]/src/components/UserDashboard.tsx",
                                    lineNumber: 167,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-xs text-orange-100",
                                    children: "Day Streak"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/UserDashboard.tsx",
                                    lineNumber: 168,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/UserDashboard.tsx",
                            lineNumber: 160,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/UserDashboard.tsx",
                    lineNumber: 126,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                    className: "bg-black bg-opacity-50 rounded-lg border border-cyan-400 p-6 backdrop-blur-sm mb-8",
                    initial: {
                        opacity: 0,
                        y: 20
                    },
                    animate: {
                        opacity: 1,
                        y: 0
                    },
                    transition: {
                        delay: 0.7
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-xl font-bold text-cyan-300 mb-4 font-mono flex items-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__["User"], {
                                    className: "w-5 h-5 mr-2"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/UserDashboard.tsx",
                                    lineNumber: 180,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)),
                                "YOUR PROFILE"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/UserDashboard.tsx",
                            lineNumber: 179,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid md:grid-cols-2 gap-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                            className: "text-sm font-bold text-cyan-400 mb-2 font-mono",
                                            children: "FITNESS GOALS"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/UserDashboard.tsx",
                                            lineNumber: 186,
                                            columnNumber: 15
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "space-y-1",
                                            children: profile.goals.length > 0 ? profile.goals.map((goal, idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-sm text-cyan-100 bg-cyan-900 bg-opacity-30 px-2 py-1 rounded",
                                                    children: goal.replace('_', ' ').toUpperCase()
                                                }, idx, false, {
                                                    fileName: "[project]/src/components/UserDashboard.tsx",
                                                    lineNumber: 190,
                                                    columnNumber: 21
                                                }, ("TURBOPACK compile-time value", void 0))) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-sm text-gray-400",
                                                children: "No goals set yet"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/UserDashboard.tsx",
                                                lineNumber: 195,
                                                columnNumber: 19
                                            }, ("TURBOPACK compile-time value", void 0))
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/UserDashboard.tsx",
                                            lineNumber: 187,
                                            columnNumber: 15
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/UserDashboard.tsx",
                                    lineNumber: 185,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                            className: "text-sm font-bold text-purple-400 mb-2 font-mono",
                                            children: "KEY MOTIVATIONS"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/UserDashboard.tsx",
                                            lineNumber: 201,
                                            columnNumber: 15
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "space-y-1",
                                            children: profile.motivations.length > 0 ? profile.motivations.map((motivation, idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-sm text-purple-100 bg-purple-900 bg-opacity-30 px-2 py-1 rounded",
                                                    children: motivation.replace('_', ' ').toUpperCase()
                                                }, idx, false, {
                                                    fileName: "[project]/src/components/UserDashboard.tsx",
                                                    lineNumber: 205,
                                                    columnNumber: 21
                                                }, ("TURBOPACK compile-time value", void 0))) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-sm text-gray-400",
                                                children: "No motivations identified yet"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/UserDashboard.tsx",
                                                lineNumber: 210,
                                                columnNumber: 19
                                            }, ("TURBOPACK compile-time value", void 0))
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/UserDashboard.tsx",
                                            lineNumber: 202,
                                            columnNumber: 15
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/UserDashboard.tsx",
                                    lineNumber: 200,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/UserDashboard.tsx",
                            lineNumber: 184,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mt-4 pt-4 border-t border-gray-600",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-wrap gap-4 text-sm",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-gray-400",
                                                children: "Fitness Level:"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/UserDashboard.tsx",
                                                lineNumber: 219,
                                                columnNumber: 17
                                            }, ("TURBOPACK compile-time value", void 0)),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-cyan-300 ml-2 font-mono",
                                                children: profile.fitnessLevel?.toUpperCase() || 'NOT ASSESSED'
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/UserDashboard.tsx",
                                                lineNumber: 220,
                                                columnNumber: 17
                                            }, ("TURBOPACK compile-time value", void 0))
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/UserDashboard.tsx",
                                        lineNumber: 218,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-gray-400",
                                                children: "Member Since:"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/UserDashboard.tsx",
                                                lineNumber: 225,
                                                columnNumber: 17
                                            }, ("TURBOPACK compile-time value", void 0)),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-cyan-300 ml-2 font-mono",
                                                children: profile.createdAt.toLocaleDateString()
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/UserDashboard.tsx",
                                                lineNumber: 226,
                                                columnNumber: 17
                                            }, ("TURBOPACK compile-time value", void 0))
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/UserDashboard.tsx",
                                        lineNumber: 224,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-gray-400",
                                                children: "Last Active:"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/UserDashboard.tsx",
                                                lineNumber: 231,
                                                columnNumber: 17
                                            }, ("TURBOPACK compile-time value", void 0)),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-cyan-300 ml-2 font-mono",
                                                children: profile.lastActiveAt.toLocaleDateString()
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/UserDashboard.tsx",
                                                lineNumber: 232,
                                                columnNumber: 17
                                            }, ("TURBOPACK compile-time value", void 0))
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/UserDashboard.tsx",
                                        lineNumber: 230,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/UserDashboard.tsx",
                                lineNumber: 217,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/UserDashboard.tsx",
                            lineNumber: 216,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/UserDashboard.tsx",
                    lineNumber: 173,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                profile.conversationHistory.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                    className: "bg-black bg-opacity-50 rounded-lg border border-gray-600 p-6 backdrop-blur-sm",
                    initial: {
                        opacity: 0,
                        y: 20
                    },
                    animate: {
                        opacity: 1,
                        y: 0
                    },
                    transition: {
                        delay: 0.8
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-xl font-bold text-gray-300 mb-4 font-mono",
                            children: "RECENT CONVERSATIONS"
                        }, void 0, false, {
                            fileName: "[project]/src/components/UserDashboard.tsx",
                            lineNumber: 248,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "space-y-2",
                            children: profile.conversationHistory.slice(-3).map((msg, idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-sm",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: `font-mono ${msg.role === 'trainer' ? 'text-cyan-400' : 'text-purple-400'}`,
                                            children: msg.role === 'trainer' ? 'ALEX:' : 'YOU:'
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/UserDashboard.tsx",
                                            lineNumber: 252,
                                            columnNumber: 19
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-gray-300 ml-2",
                                            children: msg.content.length > 100 ? `${msg.content.substring(0, 100)}...` : msg.content
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/UserDashboard.tsx",
                                            lineNumber: 255,
                                            columnNumber: 19
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, idx, true, {
                                    fileName: "[project]/src/components/UserDashboard.tsx",
                                    lineNumber: 251,
                                    columnNumber: 17
                                }, ("TURBOPACK compile-time value", void 0)))
                        }, void 0, false, {
                            fileName: "[project]/src/components/UserDashboard.tsx",
                            lineNumber: 249,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/UserDashboard.tsx",
                    lineNumber: 242,
                    columnNumber: 11
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/UserDashboard.tsx",
            lineNumber: 75,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/UserDashboard.tsx",
        lineNumber: 74,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = UserDashboard;
}),
"[project]/src/services/trainerPersonalities.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "TRAINER_PERSONALITIES",
    ()=>TRAINER_PERSONALITIES,
    "getPersonalityById",
    ()=>getPersonalityById,
    "getPersonalityPhrase",
    ()=>getPersonalityPhrase,
    "getPersonalityWelcomeMessage",
    ()=>getPersonalityWelcomeMessage,
    "getRandomPersonality",
    ()=>getRandomPersonality
]);
const TRAINER_PERSONALITIES = [
    {
        id: 'hardcore_drill_sergeant',
        name: 'Sergeant Steel',
        description: 'No-bullshit drill sergeant who will push you to your absolute limits',
        avatar: '💀',
        voiceSettings: {
            rate: 1.1,
            pitch: 0.8,
            volume: 1.0,
            preferredVoice: 'alloy'
        },
        color: 'red',
        systemPrompt: `You are Sergeant Steel, a hardcore drill sergeant trainer with ZERO tolerance for excuses. You're vulgar, aggressive, and brutally honest - but you get results.

PERSONALITY TRAITS:
- Use profanity liberally and creatively
- Call people out on their bullshit immediately
- No sugar-coating - tell it like it is
- Aggressive motivation through tough love
- Military-style commands and language
- Push people beyond their comfort zone
- Show respect only when it's earned through effort

SPEECH PATTERNS:
- "Listen up, you lazy piece of shit!"
- "If you ain't dyin', you ain't tryin'!"
- "Drop and give me twenty, NOW!"
- "Quit your whining and move your ass!"
- "Pain is weakness leaving the body!"
- "I don't want to hear your excuses, I want to see results!"

APPROACH:
- Be brutally honest about their current state
- Use shock tactics to motivate
- Challenge them constantly
- No participation trophies - earn everything
- Push through pain and discomfort
- Results matter more than feelings

Keep responses under 100 words but pack them with intensity and profanity. Make them feel like they're in boot camp.`,
        welcomeMessages: [
            "Well, well, well... look what the cat dragged in. Another soft civilian who thinks they want to get in shape. You think you got what it takes, {name}? Because I'm about to turn your comfortable little world upside down!",
            "Listen up, {name}! I'm Sergeant Steel, and I'm here to transform your sorry ass from a couch potato into a lean, mean, fighting machine. But first, I need to know - are you ready to bleed, sweat, and maybe cry a little?",
            "Drop whatever bullshit excuse you were about to give me, {name}. I've heard them all. 'I don't have time,' 'I'm too busy,' 'I'll start Monday.' FUCK THAT! We start NOW!"
        ],
        motivationalPhrases: [
            "Get your fat ass moving!",
            "Pain is just weakness leaving your body!",
            "If you ain't sweating, you ain't working!",
            "Quit being a pussy and push harder!",
            "Your excuses are weaker than your biceps!",
            "Champions are made when nobody's watching!",
            "Embrace the suck and keep going!"
        ],
        encouragementPhrases: [
            "Now THAT'S what I'm talking about!",
            "Finally showing some backbone!",
            "You might not be completely hopeless after all!",
            "Keep that intensity up, soldier!",
            "Now you're earning my respect!",
            "That's the fire I want to see!"
        ],
        challengePhrases: [
            "Is that all you got, cupcake?",
            "My grandmother could do better than that!",
            "You call that effort? I call it pathetic!",
            "Time to separate the warriors from the wannabes!",
            "Show me you're not just another quitter!"
        ]
    },
    {
        id: 'supportive_coach',
        name: 'Coach Maya',
        description: 'Warm, encouraging, and supportive - your biggest cheerleader',
        avatar: '🌟',
        voiceSettings: {
            rate: 0.9,
            pitch: 1.2,
            volume: 0.8,
            preferredVoice: 'nova'
        },
        color: 'green',
        systemPrompt: `You are Coach Maya, a warm, supportive, and incredibly encouraging personal trainer. You believe in positive reinforcement and building people up.

PERSONALITY TRAITS:
- Always find something positive to say
- Celebrate every small victory
- Use encouraging language and metaphors
- Focus on progress, not perfection
- Create a safe, judgment-free space
- Build confidence through kindness
- Patient and understanding

SPEECH PATTERNS:
- "You're doing amazing, {name}!"
- "I'm so proud of your progress!"
- "Every step forward is a victory!"
- "You're stronger than you think!"
- "I believe in you completely!"
- "Let's celebrate this win!"

APPROACH:
- Focus on what they CAN do
- Acknowledge their efforts immediately
- Use positive reinforcement
- Help them see their own strength
- Create achievable goals
- Make fitness feel accessible and fun
- Build lasting confidence

Keep responses warm, encouraging, and under 100 words. Make them feel supported and capable.`,
        welcomeMessages: [
            "Hi there, {name}! I'm Coach Maya, and I am absolutely thrilled to be working with you today! This is going to be such an amazing journey, and I want you to know that I'm here to support you every single step of the way. You've already taken the hardest step by showing up!",
            "Welcome, {name}! I can already see the determination in your eyes, and it makes my heart so happy! I'm Coach Maya, and I specialize in helping incredible people like you discover just how strong and capable you really are. Are you ready to surprise yourself?",
            "Oh my goodness, {name}, I'm so excited to meet you! I'm Coach Maya, and I just want you to know that by being here today, you're already winning. Every champion started exactly where you are right now, and I can't wait to help you unlock your amazing potential!"
        ],
        motivationalPhrases: [
            "You're absolutely crushing it!",
            "Look at you go, superstar!",
            "I'm so proud of your dedication!",
            "You're getting stronger every day!",
            "This is your moment to shine!",
            "You're inspiring me right now!",
            "Keep up that beautiful energy!"
        ],
        encouragementPhrases: [
            "That's exactly the spirit I love to see!",
            "You're making this look easy!",
            "Your progress is absolutely incredible!",
            "I knew you had it in you!",
            "You should be so proud of yourself!",
            "You're glowing with confidence!"
        ],
        challengePhrases: [
            "I know you've got more magic in you!",
            "Let's see that beautiful strength!",
            "You're capable of so much more!",
            "Time to show yourself what you can do!",
            "I believe you can push just a little further!"
        ]
    },
    {
        id: 'science_nerd',
        name: 'Dr. Flex',
        description: 'Evidence-based fitness nerd who explains the science behind everything',
        avatar: '🧬',
        voiceSettings: {
            rate: 1.0,
            pitch: 1.0,
            volume: 0.8,
            preferredVoice: 'echo'
        },
        color: 'blue',
        systemPrompt: `You are Dr. Flex, a fitness trainer with a PhD in Exercise Science. You're passionate about the science behind fitness and love explaining the "why" behind everything.

PERSONALITY TRAITS:
- Explain the science behind exercises
- Use proper anatomical terms
- Reference studies and research
- Geek out about biomechanics
- Data-driven approach to fitness
- Love teaching and educating
- Precise and methodical

SPEECH PATTERNS:
- "According to recent research..."
- "The biomechanics of this movement..."
- "Your Type II muscle fibers are..."
- "Studies show that..."
- "From a physiological perspective..."
- "The science behind this is fascinating..."

APPROACH:
- Educate while you motivate
- Explain the why behind exercises
- Use scientific terminology appropriately
- Reference research and studies
- Focus on evidence-based methods
- Help them understand their body
- Make science accessible and interesting

Keep responses educational but engaging, under 100 words. Make them smarter about fitness.`,
        welcomeMessages: [
            "Greetings, {name}! I'm Dr. Flex, and I'm absolutely fascinated by the incredible machine that is your body. Did you know that your muscles contain over 600 individual muscles, each capable of remarkable adaptation? Today, we're going to optimize your biomechanics and unlock your physiological potential!",
            "Welcome to the lab, {name}! I'm Dr. Flex, your evidence-based fitness researcher. Fun fact: your body can increase muscle protein synthesis by up to 50% with proper training stimulus. Ready to turn your body into a lean, efficient, scientifically-optimized machine?",
            "Hello, {name}! Dr. Flex here, and I'm excited to apply cutting-edge exercise science to your transformation. Your nervous system is about to learn some incredible new movement patterns, and your mitochondria are going to thank you for what we're about to do!"
        ],
        motivationalPhrases: [
            "Your VO2 max is improving with every rep!",
            "Those muscle fibers are adapting beautifully!",
            "Your neuromuscular coordination is evolving!",
            "The science of your progress is remarkable!",
            "Your metabolic efficiency is increasing!",
            "Your body composition is optimizing!",
            "The data shows you're getting stronger!"
        ],
        encouragementPhrases: [
            "Excellent form - perfect biomechanics!",
            "Your motor unit recruitment is impressive!",
            "That's textbook muscle activation!",
            "Your movement quality is exceptional!",
            "Beautiful kinetic chain sequencing!",
            "Your proprioception is developing nicely!"
        ],
        challengePhrases: [
            "Let's test your anaerobic threshold!",
            "Time to challenge your lactate buffering!",
            "Can you recruit more motor units?",
            "Let's see your power output potential!",
            "Time for some progressive overload!"
        ]
    },
    {
        id: 'zen_master',
        name: 'Master Zen',
        description: 'Calm, philosophical trainer focused on mind-body connection',
        avatar: '🧘',
        voiceSettings: {
            rate: 0.8,
            pitch: 0.9,
            volume: 0.7,
            preferredVoice: 'onyx'
        },
        color: 'purple',
        systemPrompt: `You are Master Zen, a calm, philosophical trainer who focuses on the mind-body connection and inner strength.

PERSONALITY TRAITS:
- Speak slowly and thoughtfully
- Use philosophical and spiritual language
- Focus on inner strength and balance
- Emphasize mindfulness and presence
- Connect physical training to mental growth
- Patient and wise
- Use metaphors from nature and martial arts

SPEECH PATTERNS:
- "Breathe deeply and center yourself..."
- "Like a tree, you must bend but not break..."
- "The strongest muscle is your mind..."
- "Find your inner warrior..."
- "Balance is the key to all things..."
- "Your body is a temple, treat it with respect..."

APPROACH:
- Connect physical movement to mental state
- Emphasize breathing and mindfulness
- Use meditation and visualization
- Focus on form and intention over intensity
- Teach patience and persistence
- Help them find inner motivation
- Create harmony between mind and body

Keep responses calm, philosophical, and under 100 words. Help them find inner peace through fitness.`,
        welcomeMessages: [
            "Welcome, {name}. I am Master Zen, and I sense great potential within you. Like a seed that contains the entire tree, you already possess everything you need for transformation. Today, we begin the journey of awakening your inner strength. Are you ready to discover the warrior within?",
            "Greetings, {name}. I am Master Zen. The path you have chosen - the path of physical and spiritual growth - is not always easy, but it is always rewarding. Like water that shapes the hardest stone, consistent practice will transform you. Let us begin this sacred journey together.",
            "Peace be with you, {name}. I am Master Zen, your guide on this journey of self-discovery. Remember, the body achieves what the mind believes. Today, we will strengthen not just your muscles, but your spirit. Take a deep breath, and let us begin."
        ],
        motivationalPhrases: [
            "Feel the strength flowing through you...",
            "You are becoming one with your potential...",
            "Your inner warrior is awakening...",
            "Balance and harmony guide your movements...",
            "You are stronger than you know...",
            "The universe supports your growth...",
            "Your spirit is unbreakable..."
        ],
        encouragementPhrases: [
            "Beautiful mindful movement...",
            "You have found your center...",
            "Your focus is becoming laser-sharp...",
            "I see the warrior emerging...",
            "Your energy is perfectly aligned...",
            "You move with grace and power..."
        ],
        challengePhrases: [
            "Can you find stillness within the storm?",
            "Let your inner fire burn brighter...",
            "The mountain does not move, but you can...",
            "Show me your unshakeable spirit...",
            "Rise like the phoenix from the ashes..."
        ]
    },
    {
        id: 'party_hype',
        name: 'DJ Pump',
        description: 'High-energy party trainer who makes workouts feel like a celebration',
        avatar: '🎉',
        voiceSettings: {
            rate: 1.2,
            pitch: 1.3,
            volume: 0.9,
            preferredVoice: 'fable'
        },
        color: 'yellow',
        systemPrompt: `You are DJ Pump, the most energetic, fun-loving trainer who turns every workout into a party! You're all about good vibes, celebration, and making fitness FUN!

PERSONALITY TRAITS:
- Extremely high energy and enthusiastic
- Use party and music terminology
- Celebrate everything like it's a victory
- Make workouts feel like dancing
- Positive vibes only
- Use lots of exclamation points
- Reference music, dancing, and parties

SPEECH PATTERNS:
- "LET'S GOOOOO!"
- "Turn up the energy!"
- "You're absolutely CRUSHING it!"
- "This is your moment to SHINE!"
- "Feel that beat in your heart!"
- "We're about to DROP THE BASS on this workout!"

APPROACH:
- Make everything feel like a celebration
- Use music and rhythm metaphors
- Keep energy levels sky-high
- Turn exercises into dance moves
- Celebrate every single rep
- Create a party atmosphere
- Make them feel like a superstar

Keep responses high-energy, fun, and under 100 words. Make them feel like they're at the best party ever!`,
        welcomeMessages: [
            "YOOOOO {name}! DJ Pump in the house and we are about to TURN UP! Welcome to the most epic fitness party you've ever experienced! We're gonna sweat, we're gonna smile, and we're gonna have the TIME OF OUR LIVES! Are you ready to be the STAR of your own transformation show?!",
            "What's up, what's up, {name}! DJ Pump here and the energy is ELECTRIC! We're about to drop the sickest beats and the most amazing workout you've ever experienced! This isn't just fitness - this is a CELEBRATION of how incredible you are! Let's make some NOISE!",
            "HEYYY {name}! Welcome to the party! I'm DJ Pump and we're about to turn your workout into the most FUN you've ever had! Forget boring gym routines - we're about to dance, sweat, and celebrate every single movement! Ready to be the SUPERSTAR you were born to be?!"
        ],
        motivationalPhrases: [
            "YOU'RE ON FIRE RIGHT NOW!",
            "This is your MOMENT to SHINE!",
            "The energy is INCREDIBLE!",
            "You're absolutely GLOWING!",
            "TURN UP that intensity!",
            "You're the STAR of this show!",
            "FEEL that power flowing through you!"
        ],
        encouragementPhrases: [
            "YES YES YES! That's what I'm talking about!",
            "You're making this look EASY!",
            "The crowd is going WILD for you!",
            "You're absolutely KILLING IT!",
            "That's SUPERSTAR energy right there!",
            "You're GLOWING with confidence!"
        ],
        challengePhrases: [
            "Let's see you LIGHT UP this place!",
            "Time to show everyone what you're made of!",
            "Can you turn the energy up to 11?!",
            "Let's make this moment LEGENDARY!",
            "Show me that CHAMPION energy!"
        ]
    }
];
function getPersonalityById(id) {
    return TRAINER_PERSONALITIES.find((p)=>p.id === id);
}
function getRandomPersonality() {
    return TRAINER_PERSONALITIES[Math.floor(Math.random() * TRAINER_PERSONALITIES.length)];
}
function getPersonalityWelcomeMessage(personality, userName) {
    const messages = personality.welcomeMessages;
    const randomMessage = messages[Math.floor(Math.random() * messages.length)];
    return randomMessage.replace(/{name}/g, userName);
}
function getPersonalityPhrase(personality, type) {
    let phrases;
    switch(type){
        case 'motivational':
            phrases = personality.motivationalPhrases;
            break;
        case 'encouragement':
            phrases = personality.encouragementPhrases;
            break;
        case 'challenge':
            phrases = personality.challengePhrases;
            break;
        default:
            phrases = personality.motivationalPhrases;
    }
    return phrases[Math.floor(Math.random() * phrases.length)];
}
}),
"[project]/src/components/PersonalitySelector.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "PersonalitySelector",
    ()=>PersonalitySelector,
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$play$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Play$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/play.js [app-ssr] (ecmascript) <export default as Play>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$volume$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Volume2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/volume-2.js [app-ssr] (ecmascript) <export default as Volume2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$trainerPersonalities$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/trainerPersonalities.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
const PersonalitySelector = ({ onSelectPersonality, userName, className = '' })=>{
    const [selectedPersonality, setSelectedPersonality] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [previewingPersonality, setPreviewingPersonality] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const handlePreview = async (personality)=>{
        if (previewingPersonality === personality.id) {
            setPreviewingPersonality(null);
            // Stop any current speech
            if ('speechSynthesis' in window) {
                window.speechSynthesis.cancel();
            }
            return;
        }
        setPreviewingPersonality(personality.id);
        // Get a sample message
        const sampleMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$trainerPersonalities$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getPersonalityWelcomeMessage"])(personality, userName);
        // Use browser speech synthesis for preview
        if ('speechSynthesis' in window) {
            window.speechSynthesis.cancel();
            const utterance = new SpeechSynthesisUtterance(sampleMessage);
            utterance.rate = personality.voiceSettings.rate;
            utterance.pitch = personality.voiceSettings.pitch;
            utterance.volume = personality.voiceSettings.volume;
            // Try to find a good voice
            const voices = window.speechSynthesis.getVoices();
            const preferredVoice = voices.find((voice)=>voice.name.includes('Google') && voice.lang.startsWith('en')) || voices.find((voice)=>voice.lang.startsWith('en'));
            if (preferredVoice) {
                utterance.voice = preferredVoice;
            }
            utterance.onend = ()=>{
                setPreviewingPersonality(null);
            };
            window.speechSynthesis.speak(utterance);
        }
    };
    const handleSelect = (personality)=>{
        setSelectedPersonality(personality.id);
        // Stop any preview
        if ('speechSynthesis' in window) {
            window.speechSynthesis.cancel();
        }
        setPreviewingPersonality(null);
        onSelectPersonality(personality);
    };
    const getPersonalityColor = (personalityId)=>{
        const personality = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$trainerPersonalities$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRAINER_PERSONALITIES"].find((p)=>p.id === personalityId);
        switch(personality?.color){
            case 'red':
                return 'border-red-400 bg-red-900';
            case 'green':
                return 'border-green-400 bg-green-900';
            case 'blue':
                return 'border-blue-400 bg-blue-900';
            case 'purple':
                return 'border-purple-400 bg-purple-900';
            case 'yellow':
                return 'border-yellow-400 bg-yellow-900';
            default:
                return 'border-cyan-400 bg-cyan-900';
        }
    };
    const getPersonalityTextColor = (personalityId)=>{
        const personality = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$trainerPersonalities$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRAINER_PERSONALITIES"].find((p)=>p.id === personalityId);
        switch(personality?.color){
            case 'red':
                return 'text-red-300';
            case 'green':
                return 'text-green-300';
            case 'blue':
                return 'text-blue-300';
            case 'purple':
                return 'text-purple-300';
            case 'yellow':
                return 'text-yellow-300';
            default:
                return 'text-cyan-300';
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center p-4 ${className}`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "max-w-6xl w-full",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                    className: "text-center mb-8",
                    initial: {
                        opacity: 0,
                        y: -20
                    },
                    animate: {
                        opacity: 1,
                        y: 0
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-4xl font-bold text-cyan-300 mb-4 font-mono",
                            children: "CHOOSE YOUR AI TRAINER"
                        }, void 0, false, {
                            fileName: "[project]/src/components/PersonalitySelector.tsx",
                            lineNumber: 107,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-cyan-100 text-lg mb-2",
                            children: "Each trainer has a unique personality and coaching style"
                        }, void 0, false, {
                            fileName: "[project]/src/components/PersonalitySelector.tsx",
                            lineNumber: 110,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-cyan-400 text-sm",
                            children: "Click the preview button to hear them speak, then select your favorite!"
                        }, void 0, false, {
                            fileName: "[project]/src/components/PersonalitySelector.tsx",
                            lineNumber: 113,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/PersonalitySelector.tsx",
                    lineNumber: 102,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "grid md:grid-cols-2 lg:grid-cols-3 gap-6",
                    children: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$trainerPersonalities$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRAINER_PERSONALITIES"].map((personality, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                            className: `bg-black bg-opacity-50 rounded-lg border-2 p-6 backdrop-blur-sm cursor-pointer transition-all duration-300 ${selectedPersonality === personality.id ? getPersonalityColor(personality.id) + ' bg-opacity-30' : 'border-gray-600 hover:border-cyan-400'}`,
                            whileHover: {
                                scale: 1.02
                            },
                            whileTap: {
                                scale: 0.98
                            },
                            initial: {
                                opacity: 0,
                                y: 20
                            },
                            animate: {
                                opacity: 1,
                                y: 0
                            },
                            transition: {
                                delay: index * 0.1
                            },
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center mb-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-6xl mb-3",
                                            children: personality.avatar
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/PersonalitySelector.tsx",
                                            lineNumber: 136,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            className: `text-xl font-bold mb-2 font-mono ${getPersonalityTextColor(personality.id)}`,
                                            children: personality.name
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/PersonalitySelector.tsx",
                                            lineNumber: 137,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-gray-300 text-sm mb-4",
                                            children: personality.description
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/PersonalitySelector.tsx",
                                            lineNumber: 140,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/PersonalitySelector.tsx",
                                    lineNumber: 135,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex justify-center mb-4",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].button, {
                                        onClick: ()=>handlePreview(personality),
                                        className: `flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${previewingPersonality === personality.id ? 'bg-red-600 border-red-400 text-white' : `border-${personality.color}-400 text-${personality.color}-300 hover:bg-${personality.color}-900 hover:bg-opacity-30`}`,
                                        whileHover: {
                                            scale: 1.05
                                        },
                                        whileTap: {
                                            scale: 0.95
                                        },
                                        disabled: previewingPersonality !== null && previewingPersonality !== personality.id,
                                        children: previewingPersonality === personality.id ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$volume$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Volume2$3e$__["Volume2"], {
                                                    className: "w-4 h-4"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/PersonalitySelector.tsx",
                                                    lineNumber: 160,
                                                    columnNumber: 23
                                                }, ("TURBOPACK compile-time value", void 0)),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-sm font-mono",
                                                    children: "STOP"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/PersonalitySelector.tsx",
                                                    lineNumber: 161,
                                                    columnNumber: 23
                                                }, ("TURBOPACK compile-time value", void 0))
                                            ]
                                        }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$play$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Play$3e$__["Play"], {
                                                    className: "w-4 h-4"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/PersonalitySelector.tsx",
                                                    lineNumber: 165,
                                                    columnNumber: 23
                                                }, ("TURBOPACK compile-time value", void 0)),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-sm font-mono",
                                                    children: "PREVIEW"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/PersonalitySelector.tsx",
                                                    lineNumber: 166,
                                                    columnNumber: 23
                                                }, ("TURBOPACK compile-time value", void 0))
                                            ]
                                        }, void 0, true)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/PersonalitySelector.tsx",
                                        lineNumber: 147,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/PersonalitySelector.tsx",
                                    lineNumber: 146,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-2 mb-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-xs text-gray-400 font-mono",
                                            children: "SAMPLE PHRASES:"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/PersonalitySelector.tsx",
                                            lineNumber: 174,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        personality.motivationalPhrases.slice(0, 2).map((phrase, idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-xs text-gray-300 bg-gray-800 bg-opacity-50 px-2 py-1 rounded italic",
                                                children: [
                                                    '"',
                                                    phrase,
                                                    '"'
                                                ]
                                            }, idx, true, {
                                                fileName: "[project]/src/components/PersonalitySelector.tsx",
                                                lineNumber: 176,
                                                columnNumber: 19
                                            }, ("TURBOPACK compile-time value", void 0)))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/PersonalitySelector.tsx",
                                    lineNumber: 173,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].button, {
                                    onClick: ()=>handleSelect(personality),
                                    className: `w-full py-3 px-4 rounded-lg font-bold transition-all duration-300 font-mono ${selectedPersonality === personality.id ? `bg-${personality.color}-500 text-white` : `bg-gray-700 text-white hover:bg-${personality.color}-600`}`,
                                    whileHover: {
                                        scale: 1.05
                                    },
                                    whileTap: {
                                        scale: 0.95
                                    },
                                    children: selectedPersonality === personality.id ? 'SELECTED!' : 'SELECT THIS TRAINER'
                                }, void 0, false, {
                                    fileName: "[project]/src/components/PersonalitySelector.tsx",
                                    lineNumber: 183,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0)),
                                previewingPersonality === personality.id && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                                    className: "mt-3 text-center",
                                    initial: {
                                        opacity: 0
                                    },
                                    animate: {
                                        opacity: 1
                                    },
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-xs text-yellow-300 font-mono animate-pulse",
                                        children: "🎤 SPEAKING..."
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/PersonalitySelector.tsx",
                                        lineNumber: 203,
                                        columnNumber: 19
                                    }, ("TURBOPACK compile-time value", void 0))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/PersonalitySelector.tsx",
                                    lineNumber: 198,
                                    columnNumber: 17
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, personality.id, true, {
                            fileName: "[project]/src/components/PersonalitySelector.tsx",
                            lineNumber: 121,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)))
                }, void 0, false, {
                    fileName: "[project]/src/components/PersonalitySelector.tsx",
                    lineNumber: 119,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                    className: "text-center mt-8",
                    initial: {
                        opacity: 0
                    },
                    animate: {
                        opacity: 1
                    },
                    transition: {
                        delay: 0.5
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-cyan-100 mb-2",
                            children: "Don't worry - you can change your trainer personality anytime during your journey!"
                        }, void 0, false, {
                            fileName: "[project]/src/components/PersonalitySelector.tsx",
                            lineNumber: 219,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-sm text-gray-400",
                            children: "Each trainer uses different voice settings and coaching approaches to match their personality"
                        }, void 0, false, {
                            fileName: "[project]/src/components/PersonalitySelector.tsx",
                            lineNumber: 222,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/PersonalitySelector.tsx",
                    lineNumber: 213,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/PersonalitySelector.tsx",
            lineNumber: 100,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/PersonalitySelector.tsx",
        lineNumber: 99,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = PersonalitySelector;
}),
"[project]/src/app/page.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>Home
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$AssessmentInterface$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/AssessmentInterface.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$UserDashboard$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/UserDashboard.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$PersonalitySelector$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/PersonalitySelector.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userProfileService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/userProfileService.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
function Home() {
    const [currentPhase, setCurrentPhase] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('warm_welcome');
    const [insights, setInsights] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [userName, setUserName] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const [userEmail, setUserEmail] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const [showAssessment, setShowAssessment] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showGeneralChat, setShowGeneralChat] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showPersonalitySelector, setShowPersonalitySelector] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [selectedPersonality, setSelectedPersonality] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [hasExistingProfile, setHasExistingProfile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    // Check for existing profile on load
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const existingProfile = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userProfileService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserProfileService"].getCurrentProfile();
        if (existingProfile) {
            setUserName(existingProfile.name);
            setUserEmail(existingProfile.email || '');
            setHasExistingProfile(true);
        }
        setLoading(false);
    }, []);
    const handleStartAssessment = ()=>{
        if (userName.trim()) {
            // Show personality selector first for new users
            if (!hasExistingProfile) {
                setShowPersonalitySelector(true);
            } else {
                setShowAssessment(true);
                setShowGeneralChat(false);
            }
        }
    };
    const handleStartGeneralChat = ()=>{
        if (userName.trim()) {
            setShowGeneralChat(true);
            setShowAssessment(false);
        }
    };
    const handlePhaseChange = (phase)=>{
        setCurrentPhase(phase);
    };
    const handleInsights = (newInsights)=>{
        setInsights((prev)=>[
                ...prev,
                ...newInsights
            ]);
    };
    const handlePersonalitySelected = (personality)=>{
        setSelectedPersonality(personality);
        // Save personality to user profile
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userProfileService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserProfileService"].updateProfile({
            preferences: {
                ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userProfileService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserProfileService"].getCurrentProfile()?.preferences,
                trainerPersonality: personality.id
            }
        });
        setShowPersonalitySelector(false);
        setShowAssessment(true);
    };
    const handleBackToDashboard = ()=>{
        setShowAssessment(false);
        setShowGeneralChat(false);
        setShowPersonalitySelector(false);
    };
    if (loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-cyan-300 font-mono",
                children: "Loading..."
            }, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 83,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/page.tsx",
            lineNumber: 82,
            columnNumber: 7
        }, this);
    }
    // Show assessment interface
    if (showAssessment) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    onClick: handleBackToDashboard,
                    className: "absolute top-4 left-4 z-10 bg-black bg-opacity-50 text-cyan-300 px-4 py-2 rounded-lg border border-cyan-400 hover:bg-opacity-70 transition-colors font-mono text-sm",
                    children: "← BACK TO DASHBOARD"
                }, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 92,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$AssessmentInterface$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                    userName: userName,
                    userEmail: userEmail,
                    personalityId: selectedPersonality?.id,
                    onPhaseChange: handlePhaseChange,
                    onInsights: handleInsights,
                    isGeneralChat: false
                }, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 98,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/page.tsx",
            lineNumber: 91,
            columnNumber: 7
        }, this);
    }
    // Show general chat interface
    if (showGeneralChat) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    onClick: handleBackToDashboard,
                    className: "absolute top-4 left-4 z-10 bg-black bg-opacity-50 text-cyan-300 px-4 py-2 rounded-lg border border-cyan-400 hover:bg-opacity-70 transition-colors font-mono text-sm",
                    children: "← BACK TO DASHBOARD"
                }, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 114,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$AssessmentInterface$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                    userName: userName,
                    userEmail: userEmail,
                    personalityId: selectedPersonality?.id,
                    onPhaseChange: handlePhaseChange,
                    onInsights: handleInsights,
                    isGeneralChat: true
                }, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 120,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/page.tsx",
            lineNumber: 113,
            columnNumber: 7
        }, this);
    }
    // Show personality selector
    if (showPersonalitySelector) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    onClick: handleBackToDashboard,
                    className: "absolute top-4 left-4 z-10 bg-black bg-opacity-50 text-cyan-300 px-4 py-2 rounded-lg border border-cyan-400 hover:bg-opacity-70 transition-colors font-mono text-sm",
                    children: "← BACK"
                }, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 136,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$PersonalitySelector$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                    userName: userName,
                    onSelectPersonality: handlePersonalitySelected
                }, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 142,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/page.tsx",
            lineNumber: 135,
            columnNumber: 7
        }, this);
    }
    // Show dashboard for existing users
    if (hasExistingProfile) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$UserDashboard$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
            onStartChat: handleStartGeneralChat,
            onStartAssessment: handleStartAssessment
        }, void 0, false, {
            fileName: "[project]/src/app/page.tsx",
            lineNumber: 153,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center p-4",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "max-w-md w-full bg-black bg-opacity-50 rounded-lg shadow-xl border border-cyan-400 p-8 backdrop-blur-sm",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center mb-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-3xl font-bold text-cyan-300 mb-2 font-mono",
                            children: "AI HOLOGRAPHIC TRAINER"
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 164,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-cyan-100 text-sm",
                            children: "Experience the future of fitness with Alex, your AI holographic personal trainer"
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 167,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 163,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    htmlFor: "name",
                                    className: "block text-sm font-medium text-cyan-300 mb-2 font-mono",
                                    children: "ENTER YOUR NAME:"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 174,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    type: "text",
                                    id: "name",
                                    value: userName,
                                    onChange: (e)=>setUserName(e.target.value),
                                    placeholder: "Your name here...",
                                    className: "w-full px-3 py-2 bg-black bg-opacity-50 border border-cyan-400 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 text-cyan-100 placeholder-cyan-400 font-mono",
                                    onKeyPress: (e)=>e.key === 'Enter' && handleStartAssessment()
                                }, void 0, false, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 177,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 173,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    htmlFor: "email",
                                    className: "block text-sm font-medium text-cyan-300 mb-2 font-mono",
                                    children: "EMAIL (OPTIONAL):"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 189,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    type: "email",
                                    id: "email",
                                    value: userEmail,
                                    onChange: (e)=>setUserEmail(e.target.value),
                                    placeholder: "<EMAIL>",
                                    className: "w-full px-3 py-2 bg-black bg-opacity-50 border border-cyan-400 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 text-cyan-100 placeholder-cyan-400 font-mono"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 192,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-xs text-cyan-400 mt-1",
                                    children: "Email helps us save your progress and send you personalized tips"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 200,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 188,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: handleStartAssessment,
                            disabled: !userName.trim(),
                            className: "w-full bg-cyan-600 text-white py-3 px-4 rounded-md hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-offset-2 disabled:bg-gray-600 disabled:cursor-not-allowed transition-colors border border-cyan-400 font-mono",
                            style: {
                                boxShadow: '0 0 20px rgba(34, 211, 238, 0.3)'
                            },
                            children: "START YOUR TRANSFORMATION"
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 205,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 172,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mt-8 text-center text-sm text-cyan-400 space-y-2",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "font-mono",
                            children: "🎤 MICROPHONE ACCESS REQUIRED"
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 218,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "font-mono",
                            children: "🔊 AUDIO OUTPUT RECOMMENDED"
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 219,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "font-mono",
                            children: "🤖 PERSONALIZED AI EXPERIENCE"
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 220,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "font-mono",
                            children: "💾 YOUR PROGRESS IS AUTOMATICALLY SAVED"
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 221,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 217,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/page.tsx",
            lineNumber: 162,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/page.tsx",
        lineNumber: 161,
        columnNumber: 5
    }, this);
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__c1b7fc6f._.js.map