{"version": 3, "sources": ["../../../../../../src/server/route-modules/app-page/vendored/contexts/image-config-context.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].ImageConfigContext\n"], "names": ["module", "exports", "require", "vendored", "ImageConfigContext"], "mappings": ";AAAAA,OAAOC,OAAO,GAAG,AACfC,QAAQ,yBACRC,QAAQ,CAAC,WAAW,CAACC,kBAAkB", "ignoreList": [0]}