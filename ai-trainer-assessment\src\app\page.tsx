'use client';

import { useState, useEffect } from 'react';
import AssessmentInterface from '@/components/AssessmentInterface';
import UserDashboard from '@/components/UserDashboard';
import PersonalitySelector from '@/components/PersonalitySelector';
import VideoCallInterface from '@/components/VideoCallInterface';
import { AssessmentPhase, AIInsight } from '@/types';
import { UserProfileService } from '@/services/userProfileService';
import { TrainerPersonality, getPersonalityById } from '@/services/trainerPersonalities';

export default function Home() {
  const [currentPhase, setCurrentPhase] = useState<AssessmentPhase>('warm_welcome');
  const [insights, setInsights] = useState<AIInsight[]>([]);
  const [userName, setUserName] = useState('');
  const [userEmail, setUserEmail] = useState('');
  const [showAssessment, setShowAssessment] = useState(false);
  const [showGeneralChat, setShowGeneralChat] = useState(false);
  const [showPersonalitySelector, setShowPersonalitySelector] = useState(false);
  const [showVideoCall, setShowVideoCall] = useState(false);
  const [selectedPersonality, setSelectedPersonality] = useState<TrainerPersonality | null>(null);
  const [hasExistingProfile, setHasExistingProfile] = useState(false);
  const [loading, setLoading] = useState(true);

  // Check for existing profile on load
  useEffect(() => {
    const existingProfile = UserProfileService.getCurrentProfile();
    if (existingProfile) {
      setUserName(existingProfile.name);
      setUserEmail(existingProfile.email || '');
      setHasExistingProfile(true);
    }
    setLoading(false);
  }, []);

  const handleStartAssessment = () => {
    if (userName.trim()) {
      // Show personality selector first for new users
      if (!hasExistingProfile) {
        setShowPersonalitySelector(true);
      } else {
        setShowAssessment(true);
        setShowGeneralChat(false);
      }
    }
  };

  const handleStartGeneralChat = () => {
    if (userName.trim()) {
      setShowGeneralChat(true);
      setShowAssessment(false);
      setShowVideoCall(false);
    }
  };

  const handleStartCall = () => {
    const profile = UserProfileService.getCurrentProfile();
    if (profile) {
      const personality = getPersonalityById(profile.preferences.trainerPersonality);
      setSelectedPersonality(personality || null);
      setShowVideoCall(true);
      setShowAssessment(false);
      setShowGeneralChat(false);
    }
  };

  const handlePhaseChange = (phase: AssessmentPhase) => {
    setCurrentPhase(phase);
  };

  const handleInsights = (newInsights: AIInsight[]) => {
    setInsights(prev => [...prev, ...newInsights]);
  };

  const handlePersonalitySelected = (personality: TrainerPersonality) => {
    setSelectedPersonality(personality);
    // Save personality to user profile
    UserProfileService.updateProfile({
      preferences: {
        ...UserProfileService.getCurrentProfile()?.preferences,
        trainerPersonality: personality.id
      }
    });
    setShowPersonalitySelector(false);
    setShowAssessment(true);
  };

  const handleBackToDashboard = () => {
    setShowAssessment(false);
    setShowGeneralChat(false);
    setShowPersonalitySelector(false);
    setShowVideoCall(false);
  };

  const handleEndCall = () => {
    setShowVideoCall(false);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-white font-mono">Loading...</div>
      </div>
    );
  }

  // Show assessment interface
  if (showAssessment) {
    return (
      <div>
        <button
          onClick={handleBackToDashboard}
          className="absolute top-4 left-4 z-10 bg-black bg-opacity-60 text-white px-4 py-2 rounded-lg border border-red-600 border-opacity-40 hover:bg-opacity-80 hover:border-opacity-60 transition-colors font-mono text-sm"
        >
          ← BACK TO DASHBOARD
        </button>
        <AssessmentInterface
          userName={userName}
          userEmail={userEmail}
          personalityId={selectedPersonality?.id}
          onPhaseChange={handlePhaseChange}
          onInsights={handleInsights}
          isGeneralChat={false}
        />
      </div>
    );
  }

  // Show general chat interface
  if (showGeneralChat) {
    return (
      <div>
        <button
          onClick={handleBackToDashboard}
          className="absolute top-4 left-4 z-10 bg-black bg-opacity-60 text-white px-4 py-2 rounded-lg border border-red-600 border-opacity-40 hover:bg-opacity-80 hover:border-opacity-60 transition-colors font-mono text-sm"
        >
          ← BACK TO DASHBOARD
        </button>
        <AssessmentInterface
          userName={userName}
          userEmail={userEmail}
          personalityId={selectedPersonality?.id}
          onPhaseChange={handlePhaseChange}
          onInsights={handleInsights}
          isGeneralChat={true}
        />
      </div>
    );
  }

  // Show video call interface
  if (showVideoCall && selectedPersonality) {
    return (
      <VideoCallInterface
        trainerPersonality={selectedPersonality}
        userName={userName}
        onEndCall={handleEndCall}
      />
    );
  }

  // Show personality selector
  if (showPersonalitySelector) {
    return (
      <div>
        <button
          onClick={handleBackToDashboard}
          className="absolute top-4 left-4 z-10 bg-black bg-opacity-60 text-white px-4 py-2 rounded-lg border border-red-600 border-opacity-40 hover:bg-opacity-80 hover:border-opacity-60 transition-colors font-mono text-sm"
        >
          ← BACK
        </button>
        <PersonalitySelector
          userName={userName}
          onSelectPersonality={handlePersonalitySelected}
        />
      </div>
    );
  }

  // Show dashboard for existing users
  if (hasExistingProfile) {
    return (
      <UserDashboard
        onStartCall={handleStartCall}
        onStartChat={handleStartGeneralChat}
        onStartAssessment={handleStartAssessment}
      />
    );
  }

  return (
    <div className="min-h-screen bg-black flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-black bg-opacity-60 rounded-lg shadow-xl border border-red-600 border-opacity-40 p-8 backdrop-blur-sm">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-2 font-mono">
            AI HOLOGRAPHIC TRAINER
          </h1>
          <p className="text-white text-opacity-80 text-sm">
            Experience the future of fitness with your AI holographic personal trainer
          </p>
        </div>

        <div className="space-y-6">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-white mb-2 font-mono">
              ENTER YOUR NAME:
            </label>
            <input
              type="text"
              id="name"
              value={userName}
              onChange={(e) => setUserName(e.target.value)}
              placeholder="Your name here..."
              className="w-full px-3 py-2 bg-black bg-opacity-60 border border-red-600 border-opacity-40 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-white placeholder-white placeholder-opacity-40 font-mono"
              onKeyPress={(e) => e.key === 'Enter' && handleStartAssessment()}
            />
          </div>

          <div>
            <label htmlFor="email" className="block text-sm font-medium text-white mb-2 font-mono">
              EMAIL (OPTIONAL):
            </label>
            <input
              type="email"
              id="email"
              value={userEmail}
              onChange={(e) => setUserEmail(e.target.value)}
              placeholder="<EMAIL>"
              className="w-full px-3 py-2 bg-black bg-opacity-60 border border-red-600 border-opacity-40 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-white placeholder-white placeholder-opacity-40 font-mono"
            />
            <p className="text-xs text-white text-opacity-60 mt-1">
              Email helps us save your progress and send you personalized tips
            </p>
          </div>

          <button
            onClick={handleStartAssessment}
            disabled={!userName.trim()}
            className="w-full bg-red-600 text-white py-3 px-4 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:bg-gray-600 disabled:cursor-not-allowed transition-colors border border-red-500 font-mono"
            style={{
              boxShadow: '0 0 20px rgba(220, 20, 60, 0.3)'
            }}
          >
            START YOUR TRANSFORMATION
          </button>
        </div>

        <div className="mt-8 text-center text-sm text-white text-opacity-60 space-y-2">
          <p className="font-mono">🎤 MICROPHONE ACCESS REQUIRED</p>
          <p className="font-mono">🔊 AUDIO OUTPUT RECOMMENDED</p>
          <p className="font-mono">🤖 PERSONALIZED AI EXPERIENCE</p>
          <p className="font-mono">💾 YOUR PROGRESS IS AUTOMATICALLY SAVED</p>
        </div>
      </div>
    </div>
  );
}
