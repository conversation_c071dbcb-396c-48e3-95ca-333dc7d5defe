'use client';

import { useState } from 'react';
import AssessmentInterface from '@/components/AssessmentInterface';
import { AssessmentPhase, AIInsight } from '@/types';

export default function Home() {
  const [currentPhase, setCurrentPhase] = useState<AssessmentPhase>('welcome');
  const [insights, setInsights] = useState<AIInsight[]>([]);
  const [userName, setUserName] = useState('');
  const [showAssessment, setShowAssessment] = useState(false);

  const handleStartAssessment = () => {
    if (userName.trim()) {
      setShowAssessment(true);
    }
  };

  const handlePhaseChange = (phase: AssessmentPhase) => {
    setCurrentPhase(phase);
  };

  const handleInsights = (newInsights: AIInsight[]) => {
    setInsights(prev => [...prev, ...newInsights]);
  };

  if (showAssessment) {
    return (
      <AssessmentInterface
        userName={userName}
        onPhaseChange={handlePhaseChange}
        onInsights={handleInsights}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-black bg-opacity-50 rounded-lg shadow-xl border border-cyan-400 p-8 backdrop-blur-sm">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-cyan-300 mb-2 font-mono">
            AI HOLOGRAPHIC TRAINER
          </h1>
          <p className="text-cyan-100 text-sm">
            Experience the future of fitness with Alex, your AI holographic personal trainer
          </p>
        </div>

        <div className="space-y-6">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-cyan-300 mb-2 font-mono">
              ENTER YOUR NAME:
            </label>
            <input
              type="text"
              id="name"
              value={userName}
              onChange={(e) => setUserName(e.target.value)}
              placeholder="Your name here..."
              className="w-full px-3 py-2 bg-black bg-opacity-50 border border-cyan-400 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 text-cyan-100 placeholder-cyan-400 font-mono"
              onKeyPress={(e) => e.key === 'Enter' && handleStartAssessment()}
            />
          </div>

          <button
            onClick={handleStartAssessment}
            disabled={!userName.trim()}
            className="w-full bg-cyan-600 text-white py-3 px-4 rounded-md hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-offset-2 disabled:bg-gray-600 disabled:cursor-not-allowed transition-colors border border-cyan-400 font-mono"
            style={{
              boxShadow: '0 0 20px rgba(34, 211, 238, 0.3)'
            }}
          >
            INITIALIZE ASSESSMENT
          </button>
        </div>

        <div className="mt-8 text-center text-sm text-cyan-400 space-y-2">
          <p className="font-mono">🎤 MICROPHONE ACCESS REQUIRED</p>
          <p className="font-mono">🔊 AUDIO OUTPUT RECOMMENDED</p>
          <p className="font-mono">🤖 AI POWERED EXPERIENCE</p>
        </div>
      </div>
    </div>
  );
}
