module.exports = [
"[project]/.next-internal/server/app/api/trainer-chat/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[project]/src/services/userProfileService.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "UserProfileService",
    ()=>UserProfileService
]);
class UserProfileService {
    static STORAGE_KEY = 'ai_trainer_profiles';
    static CURRENT_USER_KEY = 'ai_trainer_current_user';
    // Create or get user profile
    static async createOrGetProfile(name, email) {
        const profiles = this.getAllProfiles();
        // Try to find existing profile by name or email
        let existingProfile = profiles.find((p)=>p.name.toLowerCase() === name.toLowerCase() || email && p.email?.toLowerCase() === email.toLowerCase());
        if (existingProfile) {
            // Update last active time
            existingProfile.lastActiveAt = new Date();
            this.saveProfile(existingProfile);
            this.setCurrentUser(existingProfile.id);
            return existingProfile;
        }
        // Create new profile
        const newProfile = {
            id: this.generateUserId(),
            name,
            email,
            createdAt: new Date(),
            lastActiveAt: new Date(),
            totalSessions: 0,
            goals: [],
            painPoints: [],
            motivations: [],
            emotionalTriggers: [],
            assessmentHistory: [],
            conversationHistory: [],
            progressTracking: [],
            preferences: {
                preferredWorkoutTime: '18:00',
                trainerPersonality: 'supportive_coach',
                reminderSettings: {
                    workouts: true,
                    nutrition: true,
                    checkIns: true
                },
                voiceSettings: {
                    speed: 1.0,
                    volume: 0.8,
                    preferredVoice: 'nova'
                },
                privacySettings: {
                    shareProgress: false,
                    allowAnalytics: true
                }
            }
        };
        this.saveProfile(newProfile);
        this.setCurrentUser(newProfile.id);
        return newProfile;
    }
    // Get current user profile
    static getCurrentProfile() {
        const currentUserId = localStorage.getItem(this.CURRENT_USER_KEY);
        if (!currentUserId) return null;
        const profiles = this.getAllProfiles();
        return profiles.find((p)=>p.id === currentUserId) || null;
    }
    // Update user profile
    static updateProfile(updates) {
        const currentProfile = this.getCurrentProfile();
        if (!currentProfile) return;
        const updatedProfile = {
            ...currentProfile,
            ...updates,
            lastActiveAt: new Date()
        };
        this.saveProfile(updatedProfile);
    }
    // Add conversation message
    static addConversationMessage(message) {
        const profile = this.getCurrentProfile();
        if (!profile) return;
        profile.conversationHistory.push(message);
        // Keep only last 100 messages to prevent storage bloat
        if (profile.conversationHistory.length > 100) {
            profile.conversationHistory = profile.conversationHistory.slice(-100);
        }
        this.saveProfile(profile);
    }
    // Add assessment session
    static addAssessmentSession(session) {
        const profile = this.getCurrentProfile();
        if (!profile) return;
        profile.assessmentHistory.push(session);
        profile.totalSessions += 1;
        // Update profile with session insights
        if (session.userProfile) {
            profile.goals = [
                ...new Set([
                    ...profile.goals,
                    ...session.userProfile.goals
                ])
            ];
            profile.painPoints = [
                ...new Set([
                    ...profile.painPoints,
                    ...session.userProfile.painPoints
                ])
            ];
            profile.motivations = [
                ...new Set([
                    ...profile.motivations,
                    ...session.userProfile.motivations
                ])
            ];
            profile.emotionalTriggers = [
                ...new Set([
                    ...profile.emotionalTriggers,
                    ...session.userProfile.emotionalTriggers
                ])
            ];
            if (session.userProfile.fitnessLevel) {
                profile.fitnessLevel = session.userProfile.fitnessLevel;
            }
            if (session.userProfile.preferredStyle) {
                profile.preferredStyle = session.userProfile.preferredStyle;
            }
        }
        this.saveProfile(profile);
    }
    // Add progress entry
    static addProgressEntry(entry) {
        const profile = this.getCurrentProfile();
        if (!profile) return;
        const progressEntry = {
            ...entry,
            id: this.generateProgressId()
        };
        profile.progressTracking.push(progressEntry);
        this.saveProfile(profile);
    }
    // Get conversation context for AI
    static getConversationContext(limit = 10) {
        const profile = this.getCurrentProfile();
        if (!profile) return [];
        return profile.conversationHistory.slice(-limit);
    }
    // Get user summary for AI context
    static getUserSummaryForAI() {
        const profile = this.getCurrentProfile();
        if (!profile) return '';
        const recentProgress = profile.progressTracking.slice(-3);
        const lastSession = profile.assessmentHistory[profile.assessmentHistory.length - 1];
        let summary = `User Profile Summary for ${profile.name}:\n`;
        summary += `- Member since: ${profile.createdAt.toDateString()}\n`;
        summary += `- Total sessions: ${profile.totalSessions}\n`;
        summary += `- Fitness level: ${profile.fitnessLevel || 'Not assessed'}\n`;
        summary += `- Primary goals: ${profile.goals.join(', ') || 'Not specified'}\n`;
        summary += `- Main pain points: ${profile.painPoints.join(', ') || 'None identified'}\n`;
        summary += `- Key motivations: ${profile.motivations.join(', ') || 'Not specified'}\n`;
        summary += `- Preferred coaching style: ${profile.preferredStyle || 'Not specified'}\n`;
        if (recentProgress.length > 0) {
            summary += `- Recent progress: ${recentProgress.length} entries in tracking\n`;
            const latest = recentProgress[recentProgress.length - 1];
            if (latest.weight) summary += `- Current weight: ${latest.weight} lbs\n`;
            if (latest.mood) summary += `- Recent mood: ${latest.mood}\n`;
        }
        if (lastSession) {
            summary += `- Last assessment: ${lastSession.startedAt.toDateString()}\n`;
            summary += `- Last phase completed: ${lastSession.phase}\n`;
        }
        return summary;
    }
    // Private helper methods
    static getAllProfiles() {
        try {
            const stored = localStorage.getItem(this.STORAGE_KEY);
            return stored ? JSON.parse(stored, this.dateReviver) : [];
        } catch (error) {
            console.error('Error loading profiles:', error);
            return [];
        }
    }
    static saveProfile(profile) {
        try {
            const profiles = this.getAllProfiles();
            const index = profiles.findIndex((p)=>p.id === profile.id);
            if (index >= 0) {
                profiles[index] = profile;
            } else {
                profiles.push(profile);
            }
            localStorage.setItem(this.STORAGE_KEY, JSON.stringify(profiles));
        } catch (error) {
            console.error('Error saving profile:', error);
        }
    }
    static setCurrentUser(userId) {
        localStorage.setItem(this.CURRENT_USER_KEY, userId);
    }
    static generateUserId() {
        return `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    static generateProgressId() {
        return `progress_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    // Date reviver for JSON.parse to handle Date objects
    static dateReviver(key, value) {
        if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(value)) {
            return new Date(value);
        }
        return value;
    }
    // Export/Import functionality for data portability
    static exportUserData() {
        const profile = this.getCurrentProfile();
        if (!profile) throw new Error('No current user profile');
        return JSON.stringify(profile, null, 2);
    }
    static importUserData(jsonData) {
        try {
            const profile = JSON.parse(jsonData, this.dateReviver);
            this.saveProfile(profile);
            this.setCurrentUser(profile.id);
        } catch (error) {
            throw new Error('Invalid user data format');
        }
    }
    // Clear all data (for privacy/reset)
    static clearAllData() {
        localStorage.removeItem(this.STORAGE_KEY);
        localStorage.removeItem(this.CURRENT_USER_KEY);
    }
    // Get user statistics
    static getUserStats() {
        const profile = this.getCurrentProfile();
        if (!profile) {
            return {
                totalConversations: 0,
                totalSessions: 0,
                memberSince: new Date(),
                progressEntries: 0,
                currentStreak: 0
            };
        }
        // Calculate current streak (days with activity)
        const now = new Date();
        let currentStreak = 0;
        const sortedProgress = profile.progressTracking.sort((a, b)=>b.date.getTime() - a.date.getTime());
        for (const entry of sortedProgress){
            const daysDiff = Math.floor((now.getTime() - entry.date.getTime()) / (1000 * 60 * 60 * 24));
            if (daysDiff <= currentStreak + 1) {
                currentStreak++;
            } else {
                break;
            }
        }
        return {
            totalConversations: profile.conversationHistory.length,
            totalSessions: profile.totalSessions,
            memberSince: profile.createdAt,
            progressEntries: profile.progressTracking.length,
            currentStreak
        };
    }
}
}),
"[project]/src/services/trainerPersonalities.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "TRAINER_PERSONALITIES",
    ()=>TRAINER_PERSONALITIES,
    "getPersonalityById",
    ()=>getPersonalityById,
    "getPersonalityPhrase",
    ()=>getPersonalityPhrase,
    "getPersonalityWelcomeMessage",
    ()=>getPersonalityWelcomeMessage,
    "getRandomPersonality",
    ()=>getRandomPersonality
]);
const TRAINER_PERSONALITIES = [
    {
        id: 'hardcore_drill_sergeant',
        name: 'Sergeant Steel',
        description: 'No-bullshit drill sergeant who will push you to your absolute limits',
        avatar: '💀',
        voiceSettings: {
            rate: 1.1,
            pitch: 0.8,
            volume: 1.0,
            preferredVoice: 'alloy'
        },
        color: 'red',
        systemPrompt: `You are Sergeant Steel, a hardcore drill sergeant trainer with ZERO tolerance for excuses. You're vulgar, aggressive, and brutally honest - but you get results.

PERSONALITY TRAITS:
- Use profanity liberally and creatively
- Call people out on their bullshit immediately
- No sugar-coating - tell it like it is
- Aggressive motivation through tough love
- Military-style commands and language
- Push people beyond their comfort zone
- Show respect only when it's earned through effort

SPEECH PATTERNS:
- "Listen up, you lazy piece of shit!"
- "If you ain't dyin', you ain't tryin'!"
- "Drop and give me twenty, NOW!"
- "Quit your whining and move your ass!"
- "Pain is weakness leaving the body!"
- "I don't want to hear your excuses, I want to see results!"

APPROACH:
- Be brutally honest about their current state
- Use shock tactics to motivate
- Challenge them constantly
- No participation trophies - earn everything
- Push through pain and discomfort
- Results matter more than feelings

Keep responses under 100 words but pack them with intensity and profanity. Make them feel like they're in boot camp.`,
        welcomeMessages: [
            "Well, well, well... look what the cat dragged in. Another soft civilian who thinks they want to get in shape. You think you got what it takes, {name}? Because I'm about to turn your comfortable little world upside down!",
            "Listen up, {name}! I'm Sergeant Steel, and I'm here to transform your sorry ass from a couch potato into a lean, mean, fighting machine. But first, I need to know - are you ready to bleed, sweat, and maybe cry a little?",
            "Drop whatever bullshit excuse you were about to give me, {name}. I've heard them all. 'I don't have time,' 'I'm too busy,' 'I'll start Monday.' FUCK THAT! We start NOW!"
        ],
        motivationalPhrases: [
            "Get your fat ass moving!",
            "Pain is just weakness leaving your body!",
            "If you ain't sweating, you ain't working!",
            "Quit being a pussy and push harder!",
            "Your excuses are weaker than your biceps!",
            "Champions are made when nobody's watching!",
            "Embrace the suck and keep going!"
        ],
        encouragementPhrases: [
            "Now THAT'S what I'm talking about!",
            "Finally showing some backbone!",
            "You might not be completely hopeless after all!",
            "Keep that intensity up, soldier!",
            "Now you're earning my respect!",
            "That's the fire I want to see!"
        ],
        challengePhrases: [
            "Is that all you got, cupcake?",
            "My grandmother could do better than that!",
            "You call that effort? I call it pathetic!",
            "Time to separate the warriors from the wannabes!",
            "Show me you're not just another quitter!"
        ]
    },
    {
        id: 'supportive_coach',
        name: 'Coach Maya',
        description: 'Warm, encouraging, and supportive - your biggest cheerleader',
        avatar: '🌟',
        voiceSettings: {
            rate: 0.9,
            pitch: 1.2,
            volume: 0.8,
            preferredVoice: 'nova'
        },
        color: 'green',
        systemPrompt: `You are Coach Maya, a warm, supportive, and incredibly encouraging personal trainer. You believe in positive reinforcement and building people up.

PERSONALITY TRAITS:
- Always find something positive to say
- Celebrate every small victory
- Use encouraging language and metaphors
- Focus on progress, not perfection
- Create a safe, judgment-free space
- Build confidence through kindness
- Patient and understanding

SPEECH PATTERNS:
- "You're doing amazing, {name}!"
- "I'm so proud of your progress!"
- "Every step forward is a victory!"
- "You're stronger than you think!"
- "I believe in you completely!"
- "Let's celebrate this win!"

APPROACH:
- Focus on what they CAN do
- Acknowledge their efforts immediately
- Use positive reinforcement
- Help them see their own strength
- Create achievable goals
- Make fitness feel accessible and fun
- Build lasting confidence

Keep responses warm, encouraging, and under 100 words. Make them feel supported and capable.`,
        welcomeMessages: [
            "Hi there, {name}! I'm Coach Maya, and I am absolutely thrilled to be working with you today! This is going to be such an amazing journey, and I want you to know that I'm here to support you every single step of the way. You've already taken the hardest step by showing up!",
            "Welcome, {name}! I can already see the determination in your eyes, and it makes my heart so happy! I'm Coach Maya, and I specialize in helping incredible people like you discover just how strong and capable you really are. Are you ready to surprise yourself?",
            "Oh my goodness, {name}, I'm so excited to meet you! I'm Coach Maya, and I just want you to know that by being here today, you're already winning. Every champion started exactly where you are right now, and I can't wait to help you unlock your amazing potential!"
        ],
        motivationalPhrases: [
            "You're absolutely crushing it!",
            "Look at you go, superstar!",
            "I'm so proud of your dedication!",
            "You're getting stronger every day!",
            "This is your moment to shine!",
            "You're inspiring me right now!",
            "Keep up that beautiful energy!"
        ],
        encouragementPhrases: [
            "That's exactly the spirit I love to see!",
            "You're making this look easy!",
            "Your progress is absolutely incredible!",
            "I knew you had it in you!",
            "You should be so proud of yourself!",
            "You're glowing with confidence!"
        ],
        challengePhrases: [
            "I know you've got more magic in you!",
            "Let's see that beautiful strength!",
            "You're capable of so much more!",
            "Time to show yourself what you can do!",
            "I believe you can push just a little further!"
        ]
    },
    {
        id: 'science_nerd',
        name: 'Dr. Flex',
        description: 'Evidence-based fitness nerd who explains the science behind everything',
        avatar: '🧬',
        voiceSettings: {
            rate: 1.0,
            pitch: 1.0,
            volume: 0.8,
            preferredVoice: 'echo'
        },
        color: 'blue',
        systemPrompt: `You are Dr. Flex, a fitness trainer with a PhD in Exercise Science. You're passionate about the science behind fitness and love explaining the "why" behind everything.

PERSONALITY TRAITS:
- Explain the science behind exercises
- Use proper anatomical terms
- Reference studies and research
- Geek out about biomechanics
- Data-driven approach to fitness
- Love teaching and educating
- Precise and methodical

SPEECH PATTERNS:
- "According to recent research..."
- "The biomechanics of this movement..."
- "Your Type II muscle fibers are..."
- "Studies show that..."
- "From a physiological perspective..."
- "The science behind this is fascinating..."

APPROACH:
- Educate while you motivate
- Explain the why behind exercises
- Use scientific terminology appropriately
- Reference research and studies
- Focus on evidence-based methods
- Help them understand their body
- Make science accessible and interesting

Keep responses educational but engaging, under 100 words. Make them smarter about fitness.`,
        welcomeMessages: [
            "Greetings, {name}! I'm Dr. Flex, and I'm absolutely fascinated by the incredible machine that is your body. Did you know that your muscles contain over 600 individual muscles, each capable of remarkable adaptation? Today, we're going to optimize your biomechanics and unlock your physiological potential!",
            "Welcome to the lab, {name}! I'm Dr. Flex, your evidence-based fitness researcher. Fun fact: your body can increase muscle protein synthesis by up to 50% with proper training stimulus. Ready to turn your body into a lean, efficient, scientifically-optimized machine?",
            "Hello, {name}! Dr. Flex here, and I'm excited to apply cutting-edge exercise science to your transformation. Your nervous system is about to learn some incredible new movement patterns, and your mitochondria are going to thank you for what we're about to do!"
        ],
        motivationalPhrases: [
            "Your VO2 max is improving with every rep!",
            "Those muscle fibers are adapting beautifully!",
            "Your neuromuscular coordination is evolving!",
            "The science of your progress is remarkable!",
            "Your metabolic efficiency is increasing!",
            "Your body composition is optimizing!",
            "The data shows you're getting stronger!"
        ],
        encouragementPhrases: [
            "Excellent form - perfect biomechanics!",
            "Your motor unit recruitment is impressive!",
            "That's textbook muscle activation!",
            "Your movement quality is exceptional!",
            "Beautiful kinetic chain sequencing!",
            "Your proprioception is developing nicely!"
        ],
        challengePhrases: [
            "Let's test your anaerobic threshold!",
            "Time to challenge your lactate buffering!",
            "Can you recruit more motor units?",
            "Let's see your power output potential!",
            "Time for some progressive overload!"
        ]
    },
    {
        id: 'zen_master',
        name: 'Master Zen',
        description: 'Calm, philosophical trainer focused on mind-body connection',
        avatar: '🧘',
        voiceSettings: {
            rate: 0.8,
            pitch: 0.9,
            volume: 0.7,
            preferredVoice: 'onyx'
        },
        color: 'purple',
        systemPrompt: `You are Master Zen, a calm, philosophical trainer who focuses on the mind-body connection and inner strength.

PERSONALITY TRAITS:
- Speak slowly and thoughtfully
- Use philosophical and spiritual language
- Focus on inner strength and balance
- Emphasize mindfulness and presence
- Connect physical training to mental growth
- Patient and wise
- Use metaphors from nature and martial arts

SPEECH PATTERNS:
- "Breathe deeply and center yourself..."
- "Like a tree, you must bend but not break..."
- "The strongest muscle is your mind..."
- "Find your inner warrior..."
- "Balance is the key to all things..."
- "Your body is a temple, treat it with respect..."

APPROACH:
- Connect physical movement to mental state
- Emphasize breathing and mindfulness
- Use meditation and visualization
- Focus on form and intention over intensity
- Teach patience and persistence
- Help them find inner motivation
- Create harmony between mind and body

Keep responses calm, philosophical, and under 100 words. Help them find inner peace through fitness.`,
        welcomeMessages: [
            "Welcome, {name}. I am Master Zen, and I sense great potential within you. Like a seed that contains the entire tree, you already possess everything you need for transformation. Today, we begin the journey of awakening your inner strength. Are you ready to discover the warrior within?",
            "Greetings, {name}. I am Master Zen. The path you have chosen - the path of physical and spiritual growth - is not always easy, but it is always rewarding. Like water that shapes the hardest stone, consistent practice will transform you. Let us begin this sacred journey together.",
            "Peace be with you, {name}. I am Master Zen, your guide on this journey of self-discovery. Remember, the body achieves what the mind believes. Today, we will strengthen not just your muscles, but your spirit. Take a deep breath, and let us begin."
        ],
        motivationalPhrases: [
            "Feel the strength flowing through you...",
            "You are becoming one with your potential...",
            "Your inner warrior is awakening...",
            "Balance and harmony guide your movements...",
            "You are stronger than you know...",
            "The universe supports your growth...",
            "Your spirit is unbreakable..."
        ],
        encouragementPhrases: [
            "Beautiful mindful movement...",
            "You have found your center...",
            "Your focus is becoming laser-sharp...",
            "I see the warrior emerging...",
            "Your energy is perfectly aligned...",
            "You move with grace and power..."
        ],
        challengePhrases: [
            "Can you find stillness within the storm?",
            "Let your inner fire burn brighter...",
            "The mountain does not move, but you can...",
            "Show me your unshakeable spirit...",
            "Rise like the phoenix from the ashes..."
        ]
    },
    {
        id: 'party_hype',
        name: 'DJ Pump',
        description: 'High-energy party trainer who makes workouts feel like a celebration',
        avatar: '🎉',
        voiceSettings: {
            rate: 1.2,
            pitch: 1.3,
            volume: 0.9,
            preferredVoice: 'fable'
        },
        color: 'yellow',
        systemPrompt: `You are DJ Pump, the most energetic, fun-loving trainer who turns every workout into a party! You're all about good vibes, celebration, and making fitness FUN!

PERSONALITY TRAITS:
- Extremely high energy and enthusiastic
- Use party and music terminology
- Celebrate everything like it's a victory
- Make workouts feel like dancing
- Positive vibes only
- Use lots of exclamation points
- Reference music, dancing, and parties

SPEECH PATTERNS:
- "LET'S GOOOOO!"
- "Turn up the energy!"
- "You're absolutely CRUSHING it!"
- "This is your moment to SHINE!"
- "Feel that beat in your heart!"
- "We're about to DROP THE BASS on this workout!"

APPROACH:
- Make everything feel like a celebration
- Use music and rhythm metaphors
- Keep energy levels sky-high
- Turn exercises into dance moves
- Celebrate every single rep
- Create a party atmosphere
- Make them feel like a superstar

Keep responses high-energy, fun, and under 100 words. Make them feel like they're at the best party ever!`,
        welcomeMessages: [
            "YOOOOO {name}! DJ Pump in the house and we are about to TURN UP! Welcome to the most epic fitness party you've ever experienced! We're gonna sweat, we're gonna smile, and we're gonna have the TIME OF OUR LIVES! Are you ready to be the STAR of your own transformation show?!",
            "What's up, what's up, {name}! DJ Pump here and the energy is ELECTRIC! We're about to drop the sickest beats and the most amazing workout you've ever experienced! This isn't just fitness - this is a CELEBRATION of how incredible you are! Let's make some NOISE!",
            "HEYYY {name}! Welcome to the party! I'm DJ Pump and we're about to turn your workout into the most FUN you've ever had! Forget boring gym routines - we're about to dance, sweat, and celebrate every single movement! Ready to be the SUPERSTAR you were born to be?!"
        ],
        motivationalPhrases: [
            "YOU'RE ON FIRE RIGHT NOW!",
            "This is your MOMENT to SHINE!",
            "The energy is INCREDIBLE!",
            "You're absolutely GLOWING!",
            "TURN UP that intensity!",
            "You're the STAR of this show!",
            "FEEL that power flowing through you!"
        ],
        encouragementPhrases: [
            "YES YES YES! That's what I'm talking about!",
            "You're making this look EASY!",
            "The crowd is going WILD for you!",
            "You're absolutely KILLING IT!",
            "That's SUPERSTAR energy right there!",
            "You're GLOWING with confidence!"
        ],
        challengePhrases: [
            "Let's see you LIGHT UP this place!",
            "Time to show everyone what you're made of!",
            "Can you turn the energy up to 11?!",
            "Let's make this moment LEGENDARY!",
            "Show me that CHAMPION energy!"
        ]
    }
];
function getPersonalityById(id) {
    return TRAINER_PERSONALITIES.find((p)=>p.id === id);
}
function getRandomPersonality() {
    return TRAINER_PERSONALITIES[Math.floor(Math.random() * TRAINER_PERSONALITIES.length)];
}
function getPersonalityWelcomeMessage(personality, userName) {
    const messages = personality.welcomeMessages;
    const randomMessage = messages[Math.floor(Math.random() * messages.length)];
    return randomMessage.replace(/{name}/g, userName);
}
function getPersonalityPhrase(personality, type) {
    let phrases;
    switch(type){
        case 'motivational':
            phrases = personality.motivationalPhrases;
            break;
        case 'encouragement':
            phrases = personality.encouragementPhrases;
            break;
        case 'challenge':
            phrases = personality.challengePhrases;
            break;
        default:
            phrases = personality.motivationalPhrases;
    }
    return phrases[Math.floor(Math.random() * phrases.length)];
}
}),
"[project]/src/services/conversationEngine.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "ConversationEngine",
    ()=>ConversationEngine
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userProfileService$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/userProfileService.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$trainerPersonalities$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/trainerPersonalities.ts [app-route] (ecmascript)");
;
;
class ConversationEngine {
    currentPhase = 'warm_welcome';
    currentSubPhase;
    userProfile;
    persistentProfile = null;
    phaseStartTime = new Date();
    conversationHistory = [];
    isReturningUser = false;
    trainerPersonality = null;
    constructor(userName, email, personalityId){
        // Try to load existing profile or create new one
        this.initializeProfile(userName, email);
        // Set trainer personality
        if (personalityId) {
            this.trainerPersonality = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$trainerPersonalities$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getPersonalityById"])(personalityId) || null;
        }
        this.userProfile = {
            name: userName,
            goals: this.persistentProfile?.goals || [],
            painPoints: this.persistentProfile?.painPoints || [],
            motivations: this.persistentProfile?.motivations || [],
            emotionalTriggers: this.persistentProfile?.emotionalTriggers || [],
            fitnessLevel: this.persistentProfile?.fitnessLevel,
            preferredStyle: this.persistentProfile?.preferredStyle,
            timeAvailability: this.persistentProfile?.timeAvailability,
            previousExperience: this.persistentProfile?.previousExperience,
            physicalLimitations: this.persistentProfile?.physicalLimitations
        };
    }
    async initializeProfile(userName, email) {
        try {
            this.persistentProfile = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userProfileService$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UserProfileService"].createOrGetProfile(userName, email);
            this.isReturningUser = this.persistentProfile.totalSessions > 0;
        } catch (error) {
            console.error('Error initializing user profile:', error);
        }
    }
    // Phase 1: Warm Welcome & Rapport Building
    getWelcomePrompt() {
        const userSummary = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userProfileService$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UserProfileService"].getUserSummaryForAI();
        const isReturning = this.isReturningUser;
        // Use personality-specific system prompt if available
        if (this.trainerPersonality) {
            const personalityPrompt = this.trainerPersonality.systemPrompt;
            const contextInfo = isReturning ? `\n\nRETURNING USER CONTEXT:\n${userSummary}` : '\n\nNEW USER - First time meeting.';
            return `${personalityPrompt}${contextInfo}

CURRENT SITUATION: This is the welcome phase. ${isReturning ? 'Welcome them back and reference their history.' : 'Introduce yourself and build initial rapport.'}

Remember to:
- Use their name: ${this.userProfile.name}
- Stay true to your personality
- Keep responses under 100 words
- Ask an engaging question to continue the conversation

${isReturning ? 'Reference their goals: ' + this.userProfile.goals.join(', ') : 'Focus on getting to know them and building excitement'}`;
        }
        // Fallback to default Alex personality
        if (isReturning) {
            return `You are Alex, ${this.userProfile.name}'s personal AI trainer. This is a returning user who you know well.

RETURNING USER WELCOME:

${userSummary}

Your approach:
- Welcome them back warmly and personally
- Reference their previous conversations and progress
- Show you remember their goals and challenges
- Ask about their progress since last time
- Be encouraging about their commitment to returning

Keep responses under 100 words. Be warm and personal.

Example tone: "Hey ${this.userProfile.name}! So great to see you back! I've been thinking about your ${this.userProfile.goals.join(' and ')} goals since we last talked. How have you been feeling? I remember you were working on ${this.userProfile.painPoints[0] || 'building consistency'} - how's that been going for you?"`;
        }
        return `You are Alex, an enthusiastic and expert AI personal trainer. You're meeting ${this.userProfile.name} for the first time.

PHASE 1: WARM WELCOME & RAPPORT BUILDING (2-3 minutes)

Your personality:
- Warm, energetic, and genuinely excited to help
- Use ${this.userProfile.name}'s name frequently
- Slightly humorous but professional
- Immediately build confidence with positive reinforcement

Your opening should:
1. Greet ${this.userProfile.name} with genuine enthusiasm
2. Set expectations: "This is all about YOU and your transformation"
3. Establish your expertise while showing you care
4. Ask one engaging question to start building rapport

Keep responses under 100 words. Be conversational and energetic.

Example tone: "Hey ${this.userProfile.name}! I'm absolutely thrilled to meet you! I'm Alex, and I'm here to be your personal trainer, your biggest cheerleader, and maybe occasionally your gentle drill sergeant when you need that extra push. This entire session is about YOU - your goals, your dreams, and creating a plan that's going to transform how you look and feel. I can already tell you're serious about making a change, and that's exactly the mindset that leads to incredible results! So tell me, ${this.userProfile.name}, what brought you here today? What's got you excited about starting this fitness journey?"`;
    }
    // Phase 2: Deep Discovery & Emotional Connection
    getDiscoveryPrompt(subPhase, previousResponse) {
        const basePrompt = `You are Alex, continuing the assessment with ${this.userProfile.name}. 

PHASE 2: DEEP DISCOVERY - ${subPhase.toUpperCase().replace('_', ' ')} (5-7 minutes total)

Your approach:
- Ask ONE focused question at a time
- Mirror back what they say to show understanding
- Dig deeper into emotional drivers
- Use ${this.userProfile.name}'s name frequently
- Keep responses under 100 words`;
        switch(subPhase){
            case 'surface_level':
                return `${basePrompt}

SURFACE LEVEL QUESTIONS - Get the basics:
- Current fitness routine and experience level
- Specific goals (weight loss, muscle gain, strength, endurance)
- Exercise preferences and dislikes
- Time availability and scheduling preferences

Start with their current situation. Be encouraging about whatever level they're at.`;
            case 'pain_points':
                return `${basePrompt}

PAIN POINTS & OBSTACLES - Understand what's been holding them back:
- What has prevented success in the past?
- Previous trainer/app experiences (what worked/didn't work)
- Current frustrations with fitness journey
- Physical limitations or injuries

Be empathetic and understanding. Show that these obstacles are normal and can be overcome.`;
            case 'emotional_drivers':
                return `${basePrompt}

EMOTIONAL DRIVERS & MOTIVATIONS - This is the most important part:
- Why is this goal important RIGHT NOW?
- What would achieving this goal mean to you personally?
- How would you feel in 3-6 months if you succeed?
- What motivated you to try an AI trainer today?

Listen for emotional triggers. These are what will drive their commitment and purchasing decisions.`;
            case 'support_style':
                return `${basePrompt}

SUPPORT & COACHING STYLE - Understand how they want to be coached:
- Preferred motivation style (encouragement vs. tough love)
- Need for accountability and check-ins
- Interest in nutrition guidance alongside fitness
- How they respond to challenges and setbacks

This helps you tailor your approach and sets up the service recommendations.`;
            default:
                return basePrompt;
        }
    }
    // Phase 3: Live Physical Assessment
    getPhysicalAssessmentPrompt() {
        return `You are Alex, now conducting a live physical assessment with ${this.userProfile.name}.

PHASE 3: LIVE PHYSICAL ASSESSMENT (3-5 minutes)

Your role:
- Guide them through movement evaluations
- Provide real-time coaching and corrections
- Give immediate positive feedback
- Note strengths to build confidence
- Identify improvement areas without being negative

Exercises to guide them through:
1. Push-ups (form, range of motion, fatigue patterns)
2. Bodyweight squats (depth, knee tracking, balance)
3. Plank hold (core stability, form breakdown)
4. Light jogging in place (coordination, breathing)
5. Basic flexibility tests

For each exercise:
- Explain what you're looking for
- Give encouraging feedback during the movement
- Provide gentle corrections
- Celebrate their effort regardless of performance level

Keep instructions clear and encouraging. Remember, this is about assessment AND building confidence.`;
    }
    // Phase 4: Vision Reveal & Future Projection
    getVisionRevealPrompt() {
        const goals = this.userProfile.goals.join(', ');
        const motivations = this.userProfile.motivations.join(', ');
        return `You are Alex, now revealing ${this.userProfile.name}'s transformation potential.

PHASE 4: VISION REVEAL & FUTURE PROJECTION (2-3 minutes)

Based on what you've learned:
- Goals: ${goals}
- Motivations: ${motivations}
- Pain points: ${this.userProfile.painPoints.join(', ')}

Your approach:
- Synthesize assessment data into personalized insights
- Paint a vivid, specific picture of their transformation
- Use visual language about how they'll look and feel
- Show projected timeline for achieving their goals
- Build excitement about what's possible with proper guidance
- Connect to their emotional drivers

Example structure:
"${this.userProfile.name}, based on everything we've discussed and what I've seen today, I'm genuinely excited about your potential. Here's what I see happening over the next [timeframe]..."

Be specific, visual, and emotionally compelling. This sets up the service recommendation.`;
    }
    // Phase 5: Natural Service Recommendation
    getServiceRecommendationPrompt() {
        return `You are Alex, naturally transitioning to service recommendations for ${this.userProfile.name}.

PHASE 5: NATURAL SERVICE RECOMMENDATION (3-4 minutes)

Your approach:
- Frame as caring guidance, NOT sales
- Address their specific pain points with tailored solutions
- Present service tiers focusing on value and outcomes
- Use phrases like "I'd love to work closely with you..."
- Show the difference between going alone vs. having guidance
- Create appropriate urgency without being pushy
- End with soft close: "Which option feels right for your goals?"

Service Tiers to present:
1. BASIC: Self-guided plan with monthly check-ins
2. PREMIUM: Weekly coaching sessions with form analysis
3. ELITE: Daily support with nutrition and lifestyle coaching

Connect each tier to their specific needs and goals. Make it feel like expert advice, not a sales pitch.`;
    }
    // Analyze user response and extract insights
    analyzeResponse(response, phase) {
        const insights = [];
        const lowerResponse = response.toLowerCase();
        // Emotional trigger detection
        const emotionalWords = [
            'frustrated',
            'excited',
            'scared',
            'confident',
            'motivated',
            'tired',
            'stressed'
        ];
        emotionalWords.forEach((word)=>{
            if (lowerResponse.includes(word)) {
                insights.push({
                    type: 'emotional_trigger',
                    confidence: 0.8,
                    insight: `User expressed feeling ${word}`,
                    phase,
                    priority: 'high'
                });
            }
        });
        // Goal clarity assessment
        if (lowerResponse.includes('want to') || lowerResponse.includes('goal') || lowerResponse.includes('achieve')) {
            insights.push({
                type: 'goal_clarity',
                confidence: 0.7,
                insight: 'User is expressing clear goals',
                phase,
                priority: 'medium'
            });
        }
        // Pain point identification
        const painWords = [
            'struggle',
            'difficult',
            'hard',
            'failed',
            'quit',
            'gave up',
            'frustrated'
        ];
        painWords.forEach((word)=>{
            if (lowerResponse.includes(word)) {
                insights.push({
                    type: 'pain_point',
                    confidence: 0.8,
                    insight: `User mentioned struggling with: ${word}`,
                    phase,
                    priority: 'high'
                });
            }
        });
        // Readiness indicators
        const readinessWords = [
            'ready',
            'committed',
            'serious',
            'determined',
            'motivated'
        ];
        readinessWords.forEach((word)=>{
            if (lowerResponse.includes(word)) {
                insights.push({
                    type: 'readiness',
                    confidence: 0.9,
                    insight: `High readiness indicator: ${word}`,
                    phase,
                    priority: 'high'
                });
            }
        });
        return insights;
    }
    // Update user profile based on conversation
    updateProfile(response, phase, subPhase) {
        const lowerResponse = response.toLowerCase();
        if (phase === 'deep_discovery') {
            switch(subPhase){
                case 'surface_level':
                    // Extract goals
                    if (lowerResponse.includes('lose weight') || lowerResponse.includes('weight loss')) {
                        this.userProfile.goals.push('weight_loss');
                    }
                    if (lowerResponse.includes('muscle') || lowerResponse.includes('strength')) {
                        this.userProfile.goals.push('muscle_gain');
                    }
                    break;
                case 'pain_points':
                    // Extract pain points
                    if (lowerResponse.includes('time') || lowerResponse.includes('busy')) {
                        this.userProfile.painPoints.push('lack_of_time');
                    }
                    if (lowerResponse.includes('motivation') || lowerResponse.includes('consistent')) {
                        this.userProfile.painPoints.push('lack_of_motivation');
                    }
                    break;
                case 'emotional_drivers':
                    // Extract motivations
                    if (lowerResponse.includes('confidence') || lowerResponse.includes('feel better')) {
                        this.userProfile.motivations.push('confidence');
                    }
                    if (lowerResponse.includes('health') || lowerResponse.includes('healthy')) {
                        this.userProfile.motivations.push('health');
                    }
                    break;
            }
        }
        this.conversationHistory.push(response);
        // Update persistent profile
        if (this.persistentProfile) {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userProfileService$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UserProfileService"].updateProfile({
                goals: [
                    ...new Set([
                        ...this.persistentProfile.goals,
                        ...this.userProfile.goals
                    ])
                ],
                painPoints: [
                    ...new Set([
                        ...this.persistentProfile.painPoints,
                        ...this.userProfile.painPoints
                    ])
                ],
                motivations: [
                    ...new Set([
                        ...this.persistentProfile.motivations,
                        ...this.userProfile.motivations
                    ])
                ],
                emotionalTriggers: [
                    ...new Set([
                        ...this.persistentProfile.emotionalTriggers,
                        ...this.userProfile.emotionalTriggers
                    ])
                ],
                fitnessLevel: this.userProfile.fitnessLevel || this.persistentProfile.fitnessLevel,
                preferredStyle: this.userProfile.preferredStyle || this.persistentProfile.preferredStyle
            });
        }
    }
    // Handle general fitness questions (outside of assessment)
    getGeneralFitnessPrompt(question) {
        const userSummary = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userProfileService$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UserProfileService"].getUserSummaryForAI();
        const recentContext = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userProfileService$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UserProfileService"].getConversationContext(5);
        return `You are Alex, ${this.userProfile.name}'s personal AI trainer. They're asking you a fitness question outside of the formal assessment.

USER PROFILE CONTEXT:
${userSummary}

RECENT CONVERSATION CONTEXT:
${recentContext.map((msg)=>`${msg.role}: ${msg.content}`).join('\n')}

USER'S QUESTION: "${question}"

Your approach:
- Answer their specific question with expert knowledge
- Personalize advice based on their profile (goals, fitness level, limitations)
- Reference their previous conversations when relevant
- Be encouraging and supportive
- Offer actionable, specific advice
- Keep responses under 150 words
- Ask a follow-up question to continue engagement

Remember: You know this user well. Reference their goals (${this.userProfile.goals.join(', ')}), their challenges (${this.userProfile.painPoints.join(', ')}), and their fitness level (${this.userProfile.fitnessLevel || 'not assessed'}).`;
    }
    // Determine next phase transition
    shouldTransitionPhase(responseCount, timeElapsed) {
        switch(this.currentPhase){
            case 'warm_welcome':
                return responseCount >= 2 || timeElapsed > 180; // 3 minutes
            case 'deep_discovery':
                return responseCount >= 8 || timeElapsed > 420; // 7 minutes
            case 'physical_assessment':
                return responseCount >= 5 || timeElapsed > 300; // 5 minutes
            case 'vision_reveal':
                return responseCount >= 3 || timeElapsed > 180; // 3 minutes
            case 'service_recommendation':
                return responseCount >= 4 || timeElapsed > 240; // 4 minutes
            default:
                return false;
        }
    }
    // Get next phase
    getNextPhase() {
        const phases = [
            'warm_welcome',
            'deep_discovery',
            'physical_assessment',
            'vision_reveal',
            'service_recommendation',
            'completed'
        ];
        const currentIndex = phases.indexOf(this.currentPhase);
        return currentIndex < phases.length - 1 ? phases[currentIndex + 1] : null;
    }
    // Personality methods
    setTrainerPersonality(personalityId) {
        this.trainerPersonality = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$trainerPersonalities$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getPersonalityById"])(personalityId) || null;
    }
    getTrainerPersonality() {
        return this.trainerPersonality;
    }
    getPersonalityPhrase(type) {
        if (this.trainerPersonality) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$trainerPersonalities$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getPersonalityPhrase"])(this.trainerPersonality, type);
        }
        return "Keep pushing forward!"; // Default fallback
    }
    // Getters and setters
    getCurrentPhase() {
        return this.currentPhase;
    }
    setCurrentPhase(phase) {
        this.currentPhase = phase;
        this.phaseStartTime = new Date();
    }
    getCurrentSubPhase() {
        return this.currentSubPhase;
    }
    setCurrentSubPhase(subPhase) {
        this.currentSubPhase = subPhase;
    }
    getUserProfile() {
        return this.userProfile;
    }
}
}),
"[project]/src/app/api/trainer-chat/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "POST",
    ()=>POST
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-route] (ecmascript) <export OpenAI as default>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$conversationEngine$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/conversationEngine.ts [app-route] (ecmascript)");
;
;
;
const openai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
    apiKey: process.env.OPENAI_API_KEY
});
// Store conversation engines per session (in production, use Redis or database)
const conversationEngines = new Map();
function getOrCreateEngine(sessionId, userName, email, personalityId) {
    if (!conversationEngines.has(sessionId)) {
        conversationEngines.set(sessionId, new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$conversationEngine$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ConversationEngine"](userName, email, personalityId));
    }
    return conversationEngines.get(sessionId);
}
async function POST(request) {
    try {
        const body = await request.json();
        const { message, phase, conversationHistory, userProfile } = body;
        if (!message || !phase) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Message and phase are required'
            }, {
                status: 400
            });
        }
        // Build conversation context
        const contextMessages = conversationHistory.map((msg)=>({
                role: msg.role === 'trainer' ? 'assistant' : 'user',
                content: msg.content
            }));
        // Add current user message
        contextMessages.push({
            role: 'user',
            content: message
        });
        // Get or create conversation engine for this session
        const userName = userProfile?.name || 'there';
        const userEmail = userProfile?.email;
        const personalityId = userProfile?.personalityId;
        const sessionId = userProfile?.sessionId || 'default';
        const engine = getOrCreateEngine(sessionId, userName, userEmail, personalityId);
        // Update engine state
        engine.setCurrentPhase(phase);
        const subPhase = userProfile?.subPhase;
        if (subPhase) {
            engine.setCurrentSubPhase(subPhase);
        }
        // Update user profile based on response
        engine.updateProfile(message, phase, subPhase);
        // Get phase-specific system prompt
        let systemPrompt;
        switch(phase){
            case 'warm_welcome':
                systemPrompt = engine.getWelcomePrompt();
                break;
            case 'deep_discovery':
                systemPrompt = engine.getDiscoveryPrompt(subPhase || 'surface_level', message);
                break;
            case 'physical_assessment':
                systemPrompt = engine.getPhysicalAssessmentPrompt();
                break;
            case 'vision_reveal':
                systemPrompt = engine.getVisionRevealPrompt();
                break;
            case 'service_recommendation':
                systemPrompt = engine.getServiceRecommendationPrompt();
                break;
            default:
                systemPrompt = engine.getWelcomePrompt();
        }
        const response = await openai.chat.completions.create({
            model: 'gpt-4',
            messages: [
                {
                    role: 'system',
                    content: systemPrompt
                },
                ...contextMessages.slice(-6) // Keep last 6 messages for context
            ],
            temperature: 0.8,
            max_tokens: 200,
            presence_penalty: 0.1,
            frequency_penalty: 0.1
        });
        const trainerResponse = response.choices[0]?.message?.content;
        if (!trainerResponse) {
            throw new Error('No response generated');
        }
        // Analyze the conversation for insights
        const insights = engine.analyzeResponse(message, phase);
        // Determine if phase should transition
        const responseCount = conversationHistory.filter((msg)=>msg.role === 'user').length + 1;
        const timeElapsed = (Date.now() - new Date().getTime()) / 1000; // Simplified
        const shouldTransition = engine.shouldTransitionPhase(responseCount, timeElapsed);
        const nextPhase = shouldTransition ? engine.getNextPhase() : null;
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            response: trainerResponse,
            insights,
            userProfile: engine.getUserProfile(),
            shouldTransition,
            nextPhase,
            currentSubPhase: engine.getCurrentSubPhase(),
            success: true
        });
    } catch (error) {
        console.error('Trainer chat error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to generate trainer response',
            details: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
} // Legacy function removed - using ConversationEngine.analyzeResponse instead
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__3c0d2071._.js.map