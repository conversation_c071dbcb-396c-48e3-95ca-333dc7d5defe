import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';
import { ConversationMessage } from '@/types';
import { ConversationEngine } from '@/services/conversationEngine';
import { UserProfileService } from '@/services/userProfileService';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      message, 
      conversationHistory, 
      userProfile
    }: {
      message: string;
      conversationHistory: ConversationMessage[];
      userProfile?: any;
    } = body;

    if (!message) {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      );
    }

    // Get user context for personalized responses
    const userName = userProfile?.name || 'there';
    const userSummary = UserProfileService.getUserSummaryForAI();
    const recentContext = UserProfileService.getConversationContext(5);

    // Create personalized system prompt for general fitness questions
    const systemPrompt = `You are Alex, ${userName}'s personal AI trainer. They're asking you a fitness question outside of the formal assessment.

USER PROFILE CONTEXT:
${userSummary}

RECENT CONVERSATION CONTEXT:
${recentContext.map(msg => `${msg.role}: ${msg.content}`).join('\n')}

Your approach:
- Answer their specific question with expert fitness knowledge
- Personalize advice based on their profile (goals, fitness level, limitations, preferences)
- Reference their previous conversations and progress when relevant
- Be encouraging, supportive, and motivational
- Offer actionable, specific advice they can implement immediately
- Keep responses conversational and under 150 words
- Ask a follow-up question to continue engagement and show you care
- Use their name naturally in conversation

Remember: You know this user well. You're their personal trainer who remembers their journey, goals, challenges, and progress. Be warm, professional, and genuinely helpful.

Current user goals: ${userProfile?.goals?.join(', ') || 'general fitness'}
Current challenges: ${userProfile?.painPoints?.join(', ') || 'none identified'}
Fitness level: ${userProfile?.fitnessLevel || 'not assessed'}
Preferred style: ${userProfile?.preferredStyle || 'balanced encouragement'}`;

    // Build conversation context (keep last 6 messages for context)
    const contextMessages = conversationHistory.slice(-6).map(msg => ({
      role: msg.role === 'trainer' ? 'assistant' : 'user',
      content: msg.content
    }));

    // Add current user message
    contextMessages.push({
      role: 'user',
      content: message
    });

    const response = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: systemPrompt
        },
        ...contextMessages
      ],
      temperature: 0.8, // Slightly creative for personality
      max_tokens: 200, // Keep responses concise but informative
      presence_penalty: 0.1,
      frequency_penalty: 0.1
    });

    const trainerResponse = response.choices[0]?.message?.content;

    if (!trainerResponse) {
      throw new Error('No response generated');
    }

    // Simple analysis for general chat (less complex than assessment)
    const insights = analyzeGeneralResponse(message, trainerResponse);

    return NextResponse.json({
      response: trainerResponse,
      insights,
      success: true
    });

  } catch (error) {
    console.error('General fitness chat error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to generate trainer response',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Simple analysis for general fitness questions
function analyzeGeneralResponse(userMessage: string, trainerResponse: string) {
  const insights = [];
  const lowerMessage = userMessage.toLowerCase();

  // Detect question types
  if (lowerMessage.includes('workout') || lowerMessage.includes('exercise')) {
    insights.push({
      type: 'workout_interest',
      confidence: 0.8,
      insight: 'User is asking about workouts/exercises',
      phase: 'general_chat',
      priority: 'medium'
    });
  }

  if (lowerMessage.includes('nutrition') || lowerMessage.includes('diet') || lowerMessage.includes('eat')) {
    insights.push({
      type: 'nutrition_interest',
      confidence: 0.8,
      insight: 'User is asking about nutrition/diet',
      phase: 'general_chat',
      priority: 'medium'
    });
  }

  if (lowerMessage.includes('motivation') || lowerMessage.includes('struggling') || lowerMessage.includes('hard')) {
    insights.push({
      type: 'motivation_needed',
      confidence: 0.9,
      insight: 'User may need motivational support',
      phase: 'general_chat',
      priority: 'high'
    });
  }

  if (lowerMessage.includes('progress') || lowerMessage.includes('results') || lowerMessage.includes('change')) {
    insights.push({
      type: 'progress_inquiry',
      confidence: 0.8,
      insight: 'User is asking about progress/results',
      phase: 'general_chat',
      priority: 'medium'
    });
  }

  return insights;
}
