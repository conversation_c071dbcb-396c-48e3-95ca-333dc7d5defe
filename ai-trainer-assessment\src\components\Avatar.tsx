'use client';

import React, { useEffect, useRef, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AvatarState, MouthSyncData } from '@/types';

interface HologramProps {
  state: AvatarState;
  className?: string;
  size?: 'small' | 'medium' | 'large';
}

export const Hologram: React.FC<HologramProps> = ({
  state,
  className = '',
  size = 'medium'
}) => {
  const [currentMouthShape, setCurrentMouthShape] = useState<'closed' | 'mid' | 'open'>('closed');
  const animationRef = useRef<number>();

  // Size configurations
  const sizeConfig = {
    small: { width: 120, height: 120, scale: 0.8 },
    medium: { width: 200, height: 200, scale: 1 },
    large: { width: 300, height: 300, scale: 1.2 }
  };

  const config = sizeConfig[size];

  // Animate mouth based on speech data
  useEffect(() => {
    if (state.mouthSyncData && state.currentAnimation === 'talking') {
      const animateMouth = () => {
        const currentTime = Date.now() - (state.mouthSyncData?.currentTime || 0);
        const currentPhoneme = state.mouthSyncData?.phonemes.find(
          p => currentTime >= p.start * 1000 && currentTime <= p.end * 1000
        );

        if (currentPhoneme) {
          setCurrentMouthShape(currentPhoneme.phoneme as 'closed' | 'mid' | 'open');
        } else {
          setCurrentMouthShape('closed');
        }

        if (currentTime < (state.mouthSyncData?.duration || 0) * 1000) {
          animationRef.current = requestAnimationFrame(animateMouth);
        }
      };

      animationRef.current = requestAnimationFrame(animateMouth);
    } else {
      setCurrentMouthShape('closed');
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [state.mouthSyncData, state.currentAnimation]);

  // Animation variants for the orb
  const containerVariants = {
    idle: {
      y: [0, -10, 0],
      rotate: [0, 2, -2, 0],
      transition: { duration: 4, repeat: Infinity, ease: 'easeInOut' }
    },
    talking: {
      y: [0, -5, 0],
      scale: [1, 1.03, 1],
      transition: { duration: 0.6, repeat: Infinity, ease: 'easeInOut' }
    },
    listening: {
      y: [0, -8, 0],
      rotate: [0, 3, -3, 0],
      transition: { duration: 2.5, repeat: Infinity, ease: 'easeInOut' }
    },
    thinking: {
      y: [0, -12, 0],
      rotate: [0, 10, -10, 0],
      transition: { duration: 3, repeat: Infinity, ease: 'easeInOut' }
    }
  };

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <motion.div
        className="relative"
        style={{ width: config.width, height: config.height }}
        variants={containerVariants}
        animate={state.currentAnimation}
        initial="idle"
      >
        {/* Main Orb - Translucent Core */}
        <motion.div
          className="absolute inset-0 rounded-full"
          style={{
            background: 'radial-gradient(circle at 30% 30%, rgba(34, 211, 238, 0.4), rgba(59, 130, 246, 0.3), rgba(147, 51, 234, 0.2), transparent)',
            backdropFilter: 'blur(2px)',
            border: '1px solid rgba(34, 211, 238, 0.3)',
            boxShadow: '0 0 60px rgba(34, 211, 238, 0.4), inset 0 0 60px rgba(59, 130, 246, 0.2)'
          }}
          animate={{
            opacity: [0.6, 0.9, 0.6],
            scale: state.currentAnimation === 'talking'
              ? [1, 1.05, 1]
              : [1, 1.02, 1]
          }}
          transition={{
            duration: state.currentAnimation === 'talking' ? 0.5 : 2,
            repeat: Infinity,
            ease: 'easeInOut'
          }}
        />

        {/* Inner Energy Core */}
        <motion.div
          className="absolute inset-8 rounded-full"
          style={{
            background: 'radial-gradient(circle, rgba(34, 211, 238, 0.6), rgba(59, 130, 246, 0.4), transparent)',
            filter: 'blur(1px)'
          }}
          animate={{
            opacity: [0.4, 0.8, 0.4],
            scale: [0.8, 1.2, 0.8],
            rotate: [0, 360]
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: 'linear'
          }}
        />

        {/* Floating Particles */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-cyan-400 rounded-full"
            style={{
              left: `${20 + (i * 10)}%`,
              top: `${30 + (i * 8)}%`,
              boxShadow: '0 0 10px rgba(34, 211, 238, 0.8)'
            }}
            animate={{
              y: [-10, 10, -10],
              x: [-5, 5, -5],
              opacity: [0.3, 1, 0.3],
              scale: [0.5, 1, 0.5]
            }}
            transition={{
              duration: 3 + (i * 0.5),
              repeat: Infinity,
              ease: 'easeInOut',
              delay: i * 0.3
            }}
          />
        ))}

        {/* Hologram Scan Lines */}
        <motion.div
          className="absolute inset-0 overflow-hidden rounded-full"
          style={{
            background: 'repeating-linear-gradient(0deg, transparent, transparent 3px, rgba(34, 211, 238, 0.1) 3px, rgba(34, 211, 238, 0.1) 6px)'
          }}
          animate={{
            y: [-30, 30, -30]
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: 'linear'
          }}
        />

        {/* Outer Glow Ring */}
        <motion.div
          className="absolute -inset-4 rounded-full border border-cyan-400 opacity-30"
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.2, 0.5, 0.2],
            rotate: [0, 360]
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: 'linear'
          }}
        />

        {/* Speaking - Intense Energy Pulses */}
        <AnimatePresence>
          {state.currentAnimation === 'talking' && (
            <>
              {[...Array(4)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute rounded-full border border-cyan-400"
                  style={{
                    inset: `${-8 - (i * 8)}px`,
                    boxShadow: '0 0 30px rgba(34, 211, 238, 0.4)'
                  }}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{
                    opacity: [0, 0.8, 0],
                    scale: [0.8, 1.4, 1.8]
                  }}
                  exit={{ opacity: 0 }}
                  transition={{
                    duration: 1.2,
                    repeat: Infinity,
                    delay: i * 0.15,
                    ease: 'easeOut'
                  }}
                />
              ))}
              {/* Central energy burst */}
              <motion.div
                className="absolute inset-4 rounded-full bg-cyan-400"
                style={{
                  filter: 'blur(2px)',
                  opacity: 0.3
                }}
                animate={{
                  scale: [0.5, 1.5, 0.5],
                  opacity: [0.1, 0.4, 0.1]
                }}
                transition={{
                  duration: 0.8,
                  repeat: Infinity,
                  ease: 'easeInOut'
                }}
              />
            </>
          )}
        </AnimatePresence>

        {/* Listening - Gentle Ripples */}
        <AnimatePresence>
          {state.currentAnimation === 'listening' && (
            <>
              {[...Array(5)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute rounded-full border border-green-400"
                  style={{
                    inset: `${-12 - (i * 6)}px`,
                    boxShadow: '0 0 20px rgba(34, 197, 94, 0.3)'
                  }}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{
                    opacity: [0, 0.6, 0],
                    scale: [0.9, 1.6, 2.2]
                  }}
                  exit={{ opacity: 0 }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    delay: i * 0.4,
                    ease: 'easeOut'
                  }}
                />
              ))}
              {/* Listening indicator dots */}
              {[...Array(3)].map((_, i) => (
                <motion.div
                  key={`dot-${i}`}
                  className="absolute w-3 h-3 bg-green-400 rounded-full"
                  style={{
                    left: `${40 + (i * 8)}%`,
                    top: '45%',
                    boxShadow: '0 0 15px rgba(34, 197, 94, 0.8)'
                  }}
                  animate={{
                    opacity: [0.3, 1, 0.3],
                    scale: [0.8, 1.2, 0.8]
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    delay: i * 0.2,
                    ease: 'easeInOut'
                  }}
                />
              ))}
            </>
          )}
        </AnimatePresence>

        {/* Thinking - Swirling Energy */}
        <AnimatePresence>
          {state.currentAnimation === 'thinking' && (
            <>
              {/* Orbital thinking particles */}
              {[...Array(8)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-2 h-2 bg-purple-400 rounded-full"
                  style={{
                    boxShadow: '0 0 12px rgba(147, 51, 234, 0.8)'
                  }}
                  animate={{
                    rotate: [0, 360],
                    scale: [0.5, 1.2, 0.5],
                    opacity: [0.4, 1, 0.4]
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    delay: i * 0.2,
                    ease: 'linear'
                  }}
                  initial={{
                    left: '50%',
                    top: '50%',
                    x: `${Math.cos((i * 45) * Math.PI / 180) * 60}px`,
                    y: `${Math.sin((i * 45) * Math.PI / 180) * 60}px`
                  }}
                />
              ))}
              {/* Central thinking core */}
              <motion.div
                className="absolute inset-6 rounded-full bg-purple-400"
                style={{
                  filter: 'blur(3px)',
                  opacity: 0.2
                }}
                animate={{
                  scale: [0.8, 1.3, 0.8],
                  opacity: [0.1, 0.3, 0.1],
                  rotate: [0, 180, 360]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: 'easeInOut'
                }}
              />
            </>
          )}
        </AnimatePresence>

        {/* Subtle Glitch Effect */}
        <motion.div
          className="absolute inset-0 rounded-full"
          style={{
            background: 'linear-gradient(90deg, transparent 0%, rgba(34, 211, 238, 0.05) 50%, transparent 100%)'
          }}
          animate={{
            x: [-150, 150, -150],
            opacity: [0, 0.2, 0]
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: 'linear'
          }}
        />
      </motion.div>

      {/* Holographic Status Display */}
      <motion.div
        className="absolute -bottom-16 text-center"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
      >
        <div className="bg-black bg-opacity-60 px-6 py-3 rounded-lg border border-cyan-400 backdrop-blur-sm">
          <motion.p
            className="text-sm text-cyan-300 font-mono"
            animate={{
              textShadow: state.currentAnimation === 'talking'
                ? ['0 0 5px rgba(34, 211, 238, 0.8)', '0 0 15px rgba(34, 211, 238, 1)', '0 0 5px rgba(34, 211, 238, 0.8)']
                : '0 0 5px rgba(34, 211, 238, 0.6)'
            }}
            transition={{ duration: 0.8, repeat: Infinity }}
          >
            {state.currentAnimation === 'idle' && '◦ ALEX ONLINE ◦'}
            {state.currentAnimation === 'talking' && '◦ TRANSMITTING ◦'}
            {state.currentAnimation === 'listening' && '◦ RECEIVING ◦'}
            {state.currentAnimation === 'thinking' && '◦ PROCESSING ◦'}
          </motion.p>
        </div>
      </motion.div>
    </div>
  );
};

export default Hologram;
