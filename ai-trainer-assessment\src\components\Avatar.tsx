'use client';

import React, { useEffect, useRef, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AvatarState, MouthSyncData } from '@/types';

interface HologramProps {
  state: AvatarState;
  className?: string;
  size?: 'small' | 'medium' | 'large';
}

export const Hologram: React.FC<HologramProps> = ({
  state,
  className = '',
  size = 'medium'
}) => {
  const [currentMouthShape, setCurrentMouthShape] = useState<'closed' | 'mid' | 'open'>('closed');
  const animationRef = useRef<number>();

  // Size configurations
  const sizeConfig = {
    small: { width: 120, height: 120, scale: 0.8 },
    medium: { width: 200, height: 200, scale: 1 },
    large: { width: 300, height: 300, scale: 1.2 }
  };

  const config = sizeConfig[size];

  // Animate mouth based on speech data
  useEffect(() => {
    if (state.mouthSyncData && state.currentAnimation === 'talking') {
      const animateMouth = () => {
        const currentTime = Date.now() - (state.mouthSyncData?.currentTime || 0);
        const currentPhoneme = state.mouthSyncData?.phonemes.find(
          p => currentTime >= p.start * 1000 && currentTime <= p.end * 1000
        );

        if (currentPhoneme) {
          setCurrentMouthShape(currentPhoneme.phoneme as 'closed' | 'mid' | 'open');
        } else {
          setCurrentMouthShape('closed');
        }

        if (currentTime < (state.mouthSyncData?.duration || 0) * 1000) {
          animationRef.current = requestAnimationFrame(animateMouth);
        }
      };

      animationRef.current = requestAnimationFrame(animateMouth);
    } else {
      setCurrentMouthShape('closed');
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [state.mouthSyncData, state.currentAnimation]);

  // Animation variants for different states
  const containerVariants = {
    idle: {
      scale: 1,
      rotate: 0,
      transition: { duration: 0.5, ease: 'easeInOut' }
    },
    talking: {
      scale: [1, 1.02, 1],
      transition: { duration: 0.3, repeat: Infinity, repeatType: 'reverse' as const }
    },
    listening: {
      scale: [1, 1.05, 1],
      rotate: [0, 1, -1, 0],
      transition: { duration: 2, repeat: Infinity, ease: 'easeInOut' }
    },
    thinking: {
      rotate: [0, 5, -5, 0],
      transition: { duration: 1.5, repeat: Infinity, ease: 'easeInOut' }
    }
  };

  const eyeVariants = {
    idle: { scaleY: 1 },
    talking: { scaleY: [1, 0.8, 1], transition: { duration: 0.2, repeat: Infinity } },
    listening: { scaleY: [1, 1.2, 1], transition: { duration: 1, repeat: Infinity } },
    thinking: { scaleY: [1, 0.5, 1], transition: { duration: 0.8, repeat: Infinity } }
  };

  const mouthVariants = {
    closed: { scaleY: 0.3, scaleX: 1 },
    mid: { scaleY: 0.6, scaleX: 1.1 },
    open: { scaleY: 1, scaleX: 1.2 }
  };

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <motion.div
        className="relative"
        style={{ width: config.width, height: config.height }}
        variants={containerVariants}
        animate={state.currentAnimation}
        initial="idle"
      >
        {/* Hologram Base Platform */}
        <motion.div
          className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-32 h-4 bg-gradient-to-r from-transparent via-cyan-400 to-transparent rounded-full opacity-60"
          animate={{
            scaleX: [1, 1.1, 1],
            opacity: [0.6, 0.8, 0.6]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: 'easeInOut'
          }}
        />

        {/* Hologram Figure */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-b from-cyan-300 via-blue-400 to-purple-500 rounded-full opacity-80"
          style={{
            background: 'linear-gradient(45deg, rgba(34, 211, 238, 0.8), rgba(59, 130, 246, 0.6), rgba(147, 51, 234, 0.8))',
            filter: 'blur(1px)',
            boxShadow: '0 0 40px rgba(34, 211, 238, 0.6), inset 0 0 40px rgba(59, 130, 246, 0.4)'
          }}
          animate={{
            opacity: [0.7, 0.9, 0.7],
            filter: state.currentAnimation === 'talking'
              ? ['blur(1px)', 'blur(0.5px)', 'blur(1px)']
              : 'blur(1px)'
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: 'easeInOut'
          }}
        />

        {/* Hologram Scan Lines */}
        <motion.div
          className="absolute inset-0 overflow-hidden rounded-full"
          style={{
            background: 'repeating-linear-gradient(0deg, transparent, transparent 2px, rgba(34, 211, 238, 0.1) 2px, rgba(34, 211, 238, 0.1) 4px)'
          }}
          animate={{
            y: [-20, 20, -20]
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: 'linear'
          }}
        />

        {/* Holographic Face Features */}
        <div className="absolute inset-4 flex flex-col items-center justify-center">
          {/* Eyes */}
          <div className="flex space-x-4 mb-4">
            <motion.div
              className="w-6 h-6 bg-cyan-300 rounded-full flex items-center justify-center shadow-lg"
              style={{
                boxShadow: '0 0 15px rgba(34, 211, 238, 0.8)'
              }}
              variants={eyeVariants}
              animate={state.currentAnimation}
            >
              <div className="w-3 h-3 bg-blue-900 rounded-full opacity-80" />
            </motion.div>
            <motion.div
              className="w-6 h-6 bg-cyan-300 rounded-full flex items-center justify-center shadow-lg"
              style={{
                boxShadow: '0 0 15px rgba(34, 211, 238, 0.8)'
              }}
              variants={eyeVariants}
              animate={state.currentAnimation}
            >
              <div className="w-3 h-3 bg-blue-900 rounded-full opacity-80" />
            </motion.div>
          </div>

          {/* Mouth */}
          <motion.div
            className="w-8 h-4 bg-cyan-400 rounded-full shadow-lg"
            style={{
              boxShadow: '0 0 10px rgba(34, 211, 238, 0.6)'
            }}
            variants={mouthVariants}
            animate={currentMouthShape}
            transition={{ duration: 0.1, ease: 'easeOut' }}
          />
        </div>

        {/* Holographic Energy Rings - Speaking */}
        <AnimatePresence>
          {state.currentAnimation === 'talking' && (
            <motion.div
              className="absolute -inset-4 rounded-full"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              {[...Array(3)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute inset-0 rounded-full border-2 border-cyan-400"
                  style={{
                    boxShadow: '0 0 20px rgba(34, 211, 238, 0.6)'
                  }}
                  animate={{
                    scale: [1, 1.3, 1],
                    opacity: [0.8, 0.2, 0.8],
                    rotate: [0, 180, 360]
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    delay: i * 0.2,
                    ease: 'easeInOut'
                  }}
                />
              ))}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Holographic Listening Waves */}
        <AnimatePresence>
          {state.currentAnimation === 'listening' && (
            <motion.div
              className="absolute -inset-6 rounded-full"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              {[...Array(4)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute inset-0 rounded-full border border-green-400"
                  style={{
                    boxShadow: '0 0 15px rgba(34, 197, 94, 0.5)'
                  }}
                  animate={{
                    scale: [1, 1.8, 1],
                    opacity: [0.9, 0, 0.9]
                  }}
                  transition={{
                    duration: 2.5,
                    repeat: Infinity,
                    delay: i * 0.3,
                    ease: 'easeOut'
                  }}
                />
              ))}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Holographic Thinking Particles */}
        <AnimatePresence>
          {state.currentAnimation === 'thinking' && (
            <motion.div
              className="absolute -top-12 -right-8 flex space-x-2"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
            >
              {[...Array(5)].map((_, i) => (
                <motion.div
                  key={i}
                  className="w-3 h-3 bg-purple-400 rounded-full"
                  style={{
                    boxShadow: '0 0 10px rgba(147, 51, 234, 0.8)'
                  }}
                  animate={{
                    y: [0, -15, 0],
                    opacity: [0.3, 1, 0.3],
                    scale: [0.5, 1, 0.5]
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    delay: i * 0.15,
                    ease: 'easeInOut'
                  }}
                />
              ))}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Holographic Glitch Effect */}
        <motion.div
          className="absolute inset-0 rounded-full"
          style={{
            background: 'linear-gradient(90deg, transparent 0%, rgba(34, 211, 238, 0.1) 50%, transparent 100%)'
          }}
          animate={{
            x: [-100, 100, -100],
            opacity: [0, 0.3, 0]
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: 'linear'
          }}
        />
      </motion.div>

      {/* Holographic Status Display */}
      <motion.div
        className="absolute -bottom-12 text-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
      >
        <div className="bg-black bg-opacity-50 px-4 py-2 rounded-lg border border-cyan-400">
          <p className="text-sm text-cyan-300 font-mono">
            {state.currentAnimation === 'idle' && '> READY TO ASSIST'}
            {state.currentAnimation === 'talking' && '> TRANSMITTING...'}
            {state.currentAnimation === 'listening' && '> RECEIVING INPUT...'}
            {state.currentAnimation === 'thinking' && '> PROCESSING...'}
          </p>
        </div>
      </motion.div>
    </div>
  );
};

export default Hologram;
