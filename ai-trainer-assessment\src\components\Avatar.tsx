'use client';

import React, { useEffect, useRef, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AvatarState, MouthSyncData } from '@/types';

interface AvatarProps {
  state: AvatarState;
  className?: string;
  size?: 'small' | 'medium' | 'large';
}

export const Avatar: React.FC<AvatarProps> = ({ 
  state, 
  className = '', 
  size = 'medium' 
}) => {
  const [currentMouthShape, setCurrentMouthShape] = useState<'closed' | 'mid' | 'open'>('closed');
  const animationRef = useRef<number>();

  // Size configurations
  const sizeConfig = {
    small: { width: 120, height: 120, scale: 0.8 },
    medium: { width: 200, height: 200, scale: 1 },
    large: { width: 300, height: 300, scale: 1.2 }
  };

  const config = sizeConfig[size];

  // Animate mouth based on speech data
  useEffect(() => {
    if (state.mouthSyncData && state.currentAnimation === 'talking') {
      const animateMouth = () => {
        const currentTime = Date.now() - (state.mouthSyncData?.currentTime || 0);
        const currentPhoneme = state.mouthSyncData?.phonemes.find(
          p => currentTime >= p.start * 1000 && currentTime <= p.end * 1000
        );

        if (currentPhoneme) {
          setCurrentMouthShape(currentPhoneme.phoneme as 'closed' | 'mid' | 'open');
        } else {
          setCurrentMouthShape('closed');
        }

        if (currentTime < (state.mouthSyncData?.duration || 0) * 1000) {
          animationRef.current = requestAnimationFrame(animateMouth);
        }
      };

      animationRef.current = requestAnimationFrame(animateMouth);
    } else {
      setCurrentMouthShape('closed');
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [state.mouthSyncData, state.currentAnimation]);

  // Animation variants for different states
  const containerVariants = {
    idle: {
      scale: 1,
      rotate: 0,
      transition: { duration: 0.5, ease: 'easeInOut' }
    },
    talking: {
      scale: [1, 1.02, 1],
      transition: { duration: 0.3, repeat: Infinity, repeatType: 'reverse' as const }
    },
    listening: {
      scale: [1, 1.05, 1],
      rotate: [0, 1, -1, 0],
      transition: { duration: 2, repeat: Infinity, ease: 'easeInOut' }
    },
    thinking: {
      rotate: [0, 5, -5, 0],
      transition: { duration: 1.5, repeat: Infinity, ease: 'easeInOut' }
    }
  };

  const eyeVariants = {
    idle: { scaleY: 1 },
    talking: { scaleY: [1, 0.8, 1], transition: { duration: 0.2, repeat: Infinity } },
    listening: { scaleY: [1, 1.2, 1], transition: { duration: 1, repeat: Infinity } },
    thinking: { scaleY: [1, 0.5, 1], transition: { duration: 0.8, repeat: Infinity } }
  };

  const mouthVariants = {
    closed: { scaleY: 0.3, scaleX: 1 },
    mid: { scaleY: 0.6, scaleX: 1.1 },
    open: { scaleY: 1, scaleX: 1.2 }
  };

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <motion.div
        className="relative"
        style={{ width: config.width, height: config.height }}
        variants={containerVariants}
        animate={state.currentAnimation}
        initial="idle"
      >
        {/* Avatar Background Circle */}
        <motion.div
          className="absolute inset-0 rounded-full bg-gradient-to-br from-blue-400 to-purple-600 shadow-lg"
          animate={{
            boxShadow: state.currentAnimation === 'talking' 
              ? '0 0 30px rgba(59, 130, 246, 0.5)' 
              : '0 10px 25px rgba(0, 0, 0, 0.2)'
          }}
        />

        {/* Face Container */}
        <div className="absolute inset-4 flex flex-col items-center justify-center">
          {/* Eyes */}
          <div className="flex space-x-4 mb-4">
            <motion.div
              className="w-6 h-6 bg-white rounded-full flex items-center justify-center"
              variants={eyeVariants}
              animate={state.currentAnimation}
            >
              <div className="w-3 h-3 bg-gray-800 rounded-full" />
            </motion.div>
            <motion.div
              className="w-6 h-6 bg-white rounded-full flex items-center justify-center"
              variants={eyeVariants}
              animate={state.currentAnimation}
            >
              <div className="w-3 h-3 bg-gray-800 rounded-full" />
            </motion.div>
          </div>

          {/* Mouth */}
          <motion.div
            className="w-8 h-4 bg-gray-800 rounded-full"
            variants={mouthVariants}
            animate={currentMouthShape}
            transition={{ duration: 0.1, ease: 'easeOut' }}
          />
        </div>

        {/* Speaking Indicator */}
        <AnimatePresence>
          {state.currentAnimation === 'talking' && (
            <motion.div
              className="absolute -inset-2 rounded-full border-2 border-blue-400"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ 
                opacity: [0.5, 1, 0.5], 
                scale: [0.8, 1.1, 0.8] 
              }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ 
                duration: 1, 
                repeat: Infinity, 
                ease: 'easeInOut' 
              }}
            />
          )}
        </AnimatePresence>

        {/* Listening Indicator */}
        <AnimatePresence>
          {state.currentAnimation === 'listening' && (
            <motion.div
              className="absolute -inset-4 rounded-full"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              {[...Array(3)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute inset-0 rounded-full border border-green-400"
                  animate={{
                    scale: [1, 1.5, 1],
                    opacity: [0.8, 0, 0.8]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    delay: i * 0.4,
                    ease: 'easeOut'
                  }}
                />
              ))}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Thinking Indicator */}
        <AnimatePresence>
          {state.currentAnimation === 'thinking' && (
            <motion.div
              className="absolute -top-8 -right-4 flex space-x-1"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
            >
              {[...Array(3)].map((_, i) => (
                <motion.div
                  key={i}
                  className="w-2 h-2 bg-gray-400 rounded-full"
                  animate={{
                    y: [0, -8, 0],
                    opacity: [0.5, 1, 0.5]
                  }}
                  transition={{
                    duration: 1,
                    repeat: Infinity,
                    delay: i * 0.2,
                    ease: 'easeInOut'
                  }}
                />
              ))}
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* Status Text */}
      <motion.div
        className="absolute -bottom-8 text-center text-sm text-gray-600"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
      >
        {state.currentAnimation === 'idle' && 'Ready to help'}
        {state.currentAnimation === 'talking' && 'Speaking...'}
        {state.currentAnimation === 'listening' && 'Listening...'}
        {state.currentAnimation === 'thinking' && 'Thinking...'}
      </motion.div>
    </div>
  );
};

export default Avatar;
