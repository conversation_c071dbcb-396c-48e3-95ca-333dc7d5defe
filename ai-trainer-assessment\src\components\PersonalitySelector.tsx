'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Play, Volume2 } from 'lucide-react';
import { TRAINER_PERSONALITIES, TrainerPersonality, getPersonalityWelcomeMessage } from '@/services/trainerPersonalities';

interface PersonalitySelectorProps {
  onSelectPersonality: (personality: TrainerPersonality) => void;
  userName: string;
  className?: string;
}

export const PersonalitySelector: React.FC<PersonalitySelectorProps> = ({
  onSelectPersonality,
  userName,
  className = ''
}) => {
  const [selectedPersonality, setSelectedPersonality] = useState<string | null>(null);
  const [previewingPersonality, setPreviewingPersonality] = useState<string | null>(null);

  const handlePreview = async (personality: TrainerPersonality) => {
    if (previewingPersonality === personality.id) {
      setPreviewingPersonality(null);
      // Stop any current speech
      if ('speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
      return;
    }

    setPreviewingPersonality(personality.id);
    
    // Get a sample message
    const sampleMessage = getPersonalityWelcomeMessage(personality, userName);
    
    // Use browser speech synthesis for preview
    if ('speechSynthesis' in window) {
      window.speechSynthesis.cancel();
      
      const utterance = new SpeechSynthesisUtterance(sampleMessage);
      utterance.rate = personality.voiceSettings.rate;
      utterance.pitch = personality.voiceSettings.pitch;
      utterance.volume = personality.voiceSettings.volume;
      
      // Try to find a good voice
      const voices = window.speechSynthesis.getVoices();
      const preferredVoice = voices.find(voice => 
        voice.name.includes('Google') && voice.lang.startsWith('en')
      ) || voices.find(voice => voice.lang.startsWith('en'));
      
      if (preferredVoice) {
        utterance.voice = preferredVoice;
      }

      utterance.onend = () => {
        setPreviewingPersonality(null);
      };

      window.speechSynthesis.speak(utterance);
    }
  };

  const handleSelect = (personality: TrainerPersonality) => {
    setSelectedPersonality(personality.id);
    // Stop any preview
    if ('speechSynthesis' in window) {
      window.speechSynthesis.cancel();
    }
    setPreviewingPersonality(null);
    onSelectPersonality(personality);
  };

  const getPersonalityColor = (personalityId: string) => {
    const personality = TRAINER_PERSONALITIES.find(p => p.id === personalityId);
    switch (personality?.color) {
      case 'red': return 'border-red-400 bg-red-900';
      case 'green': return 'border-green-400 bg-green-900';
      case 'blue': return 'border-blue-400 bg-blue-900';
      case 'purple': return 'border-purple-400 bg-purple-900';
      case 'yellow': return 'border-yellow-400 bg-yellow-900';
      default: return 'border-cyan-400 bg-cyan-900';
    }
  };

  const getPersonalityTextColor = (personalityId: string) => {
    const personality = TRAINER_PERSONALITIES.find(p => p.id === personalityId);
    switch (personality?.color) {
      case 'red': return 'text-red-300';
      case 'green': return 'text-green-300';
      case 'blue': return 'text-blue-300';
      case 'purple': return 'text-purple-300';
      case 'yellow': return 'text-yellow-300';
      default: return 'text-cyan-300';
    }
  };

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center p-4 ${className}`}>
      <div className="max-w-6xl w-full">
        {/* Header */}
        <motion.div
          className="text-center mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <h1 className="text-4xl font-bold text-cyan-300 mb-4 font-mono">
            CHOOSE YOUR AI TRAINER
          </h1>
          <p className="text-cyan-100 text-lg mb-2">
            Each trainer has a unique personality and coaching style
          </p>
          <p className="text-cyan-400 text-sm">
            Click the preview button to hear them speak, then select your favorite!
          </p>
        </motion.div>

        {/* Personality Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {TRAINER_PERSONALITIES.map((personality, index) => (
            <motion.div
              key={personality.id}
              className={`bg-black bg-opacity-50 rounded-lg border-2 p-6 backdrop-blur-sm cursor-pointer transition-all duration-300 ${
                selectedPersonality === personality.id
                  ? getPersonalityColor(personality.id) + ' bg-opacity-30'
                  : 'border-gray-600 hover:border-cyan-400'
              }`}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              {/* Personality Header */}
              <div className="text-center mb-4">
                <div className="text-6xl mb-3">{personality.avatar}</div>
                <h3 className={`text-xl font-bold mb-2 font-mono ${getPersonalityTextColor(personality.id)}`}>
                  {personality.name}
                </h3>
                <p className="text-gray-300 text-sm mb-4">
                  {personality.description}
                </p>
              </div>

              {/* Preview Button */}
              <div className="flex justify-center mb-4">
                <motion.button
                  onClick={() => handlePreview(personality)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${
                    previewingPersonality === personality.id
                      ? 'bg-red-600 border-red-400 text-white'
                      : `border-${personality.color}-400 text-${personality.color}-300 hover:bg-${personality.color}-900 hover:bg-opacity-30`
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  disabled={previewingPersonality !== null && previewingPersonality !== personality.id}
                >
                  {previewingPersonality === personality.id ? (
                    <>
                      <Volume2 className="w-4 h-4" />
                      <span className="text-sm font-mono">STOP</span>
                    </>
                  ) : (
                    <>
                      <Play className="w-4 h-4" />
                      <span className="text-sm font-mono">PREVIEW</span>
                    </>
                  )}
                </motion.button>
              </div>

              {/* Sample Phrases */}
              <div className="space-y-2 mb-4">
                <div className="text-xs text-gray-400 font-mono">SAMPLE PHRASES:</div>
                {personality.motivationalPhrases.slice(0, 2).map((phrase, idx) => (
                  <div key={idx} className="text-xs text-gray-300 bg-gray-800 bg-opacity-50 px-2 py-1 rounded italic">
                    "{phrase}"
                  </div>
                ))}
              </div>

              {/* Select Button */}
              <motion.button
                onClick={() => handleSelect(personality)}
                className={`w-full py-3 px-4 rounded-lg font-bold transition-all duration-300 font-mono ${
                  selectedPersonality === personality.id
                    ? `bg-${personality.color}-500 text-white`
                    : `bg-gray-700 text-white hover:bg-${personality.color}-600`
                }`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {selectedPersonality === personality.id ? 'SELECTED!' : 'SELECT THIS TRAINER'}
              </motion.button>

              {/* Personality Indicator */}
              {previewingPersonality === personality.id && (
                <motion.div
                  className="mt-3 text-center"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                >
                  <div className="text-xs text-yellow-300 font-mono animate-pulse">
                    🎤 SPEAKING...
                  </div>
                </motion.div>
              )}
            </motion.div>
          ))}
        </div>

        {/* Bottom Info */}
        <motion.div
          className="text-center mt-8"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
        >
          <p className="text-cyan-100 mb-2">
            Don't worry - you can change your trainer personality anytime during your journey!
          </p>
          <div className="text-sm text-gray-400">
            Each trainer uses different voice settings and coaching approaches to match their personality
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default PersonalitySelector;
