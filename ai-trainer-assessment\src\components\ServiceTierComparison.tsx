'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Check, X, Star, Zap, Crown } from 'lucide-react';
import { ServiceTier } from '@/types';
import { SERVICE_TIERS, getComparisonData } from '@/services/serviceTiers';

interface ServiceTierComparisonProps {
  recommendedTier?: ServiceTier;
  onSelectTier: (tier: ServiceTier) => void;
  className?: string;
}

export const ServiceTierComparison: React.FC<ServiceTierComparisonProps> = ({
  recommendedTier,
  onSelectTier,
  className = ''
}) => {
  const [selectedTier, setSelectedTier] = useState<string | null>(null);
  const comparisonData = getComparisonData();

  const getTierIcon = (tierId: string) => {
    switch (tierId) {
      case 'basic': return <Star className="w-6 h-6" />;
      case 'premium': return <Zap className="w-6 h-6" />;
      case 'elite': return <Crown className="w-6 h-6" />;
      default: return <Star className="w-6 h-6" />;
    }
  };

  const getTierColor = (tierId: string) => {
    switch (tierId) {
      case 'basic': return 'from-blue-500 to-cyan-500';
      case 'premium': return 'from-purple-500 to-pink-500';
      case 'elite': return 'from-yellow-500 to-orange-500';
      default: return 'from-blue-500 to-cyan-500';
    }
  };

  const isRecommended = (tier: ServiceTier) => {
    return recommendedTier?.id === tier.id;
  };

  return (
    <div className={`w-full max-w-6xl mx-auto ${className}`}>
      {/* Header */}
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-cyan-300 mb-4 font-mono">
          CHOOSE YOUR TRANSFORMATION PATH
        </h2>
        <p className="text-cyan-100 text-lg">
          Select the program that matches your commitment level and goals
        </p>
      </div>

      {/* Tier Cards */}
      <div className="grid md:grid-cols-3 gap-6 mb-8">
        {SERVICE_TIERS.map((tier, index) => (
          <motion.div
            key={tier.id}
            className={`relative bg-black bg-opacity-50 rounded-lg border-2 p-6 backdrop-blur-sm cursor-pointer transition-all duration-300 ${
              isRecommended(tier) 
                ? 'border-yellow-400 shadow-yellow-400/50' 
                : selectedTier === tier.id
                ? 'border-cyan-400 shadow-cyan-400/50'
                : 'border-gray-600 hover:border-cyan-400'
            }`}
            style={{
              boxShadow: isRecommended(tier) 
                ? '0 0 30px rgba(251, 191, 36, 0.3)' 
                : selectedTier === tier.id
                ? '0 0 20px rgba(34, 211, 238, 0.3)'
                : undefined
            }}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => setSelectedTier(tier.id)}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            {/* Recommended Badge */}
            {isRecommended(tier) && (
              <motion.div
                className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-yellow-400 text-black px-4 py-1 rounded-full text-sm font-bold"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.5 }}
              >
                RECOMMENDED FOR YOU
              </motion.div>
            )}

            {/* Tier Header */}
            <div className="text-center mb-6">
              <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r ${getTierColor(tier.id)} mb-4`}>
                {getTierIcon(tier.id)}
              </div>
              <h3 className="text-xl font-bold text-white mb-2 font-mono">
                {tier.name}
              </h3>
              <div className="text-3xl font-bold text-cyan-300 mb-1">
                ${tier.price}
              </div>
              <div className="text-sm text-gray-400">
                {tier.duration} weeks
              </div>
            </div>

            {/* Success Rate */}
            <div className="text-center mb-4 p-3 bg-gray-800 bg-opacity-50 rounded-lg">
              <div className="text-lg font-bold text-green-400">
                {comparisonData.successRates[tier.id as keyof typeof comparisonData.successRates]} Success Rate
              </div>
              <div className="text-sm text-gray-300">
                {comparisonData.averageResults[tier.id as keyof typeof comparisonData.averageResults]}
              </div>
            </div>

            {/* Features */}
            <div className="space-y-2 mb-6">
              {tier.features.slice(0, 5).map((feature, idx) => (
                <div key={idx} className="flex items-start space-x-2">
                  <Check className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                  <span className="text-sm text-gray-300">{feature}</span>
                </div>
              ))}
              {tier.features.length > 5 && (
                <div className="text-sm text-cyan-400 font-medium">
                  +{tier.features.length - 5} more features
                </div>
              )}
            </div>

            {/* Urgency Factors */}
            <div className="space-y-1 mb-6">
              {tier.urgencyFactors.map((factor, idx) => (
                <div key={idx} className="text-xs text-yellow-300 bg-yellow-900 bg-opacity-30 px-2 py-1 rounded">
                  {factor}
                </div>
              ))}
            </div>

            {/* Select Button */}
            <motion.button
              className={`w-full py-3 px-4 rounded-lg font-bold transition-all duration-300 ${
                selectedTier === tier.id
                  ? 'bg-cyan-500 text-white'
                  : isRecommended(tier)
                  ? 'bg-yellow-500 text-black hover:bg-yellow-400'
                  : 'bg-gray-700 text-white hover:bg-gray-600'
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={(e) => {
                e.stopPropagation();
                onSelectTier(tier);
              }}
            >
              {selectedTier === tier.id ? 'SELECTED' : 'SELECT THIS PLAN'}
            </motion.button>
          </motion.div>
        ))}
      </div>

      {/* Feature Comparison Table */}
      <motion.div
        className="bg-black bg-opacity-50 rounded-lg border border-cyan-400 p-6 backdrop-blur-sm"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
      >
        <h3 className="text-xl font-bold text-cyan-300 mb-4 text-center font-mono">
          DETAILED FEATURE COMPARISON
        </h3>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-600">
                <th className="text-left py-3 px-4 text-cyan-300 font-mono">FEATURE</th>
                <th className="text-center py-3 px-4 text-cyan-300 font-mono">BASIC</th>
                <th className="text-center py-3 px-4 text-cyan-300 font-mono">PREMIUM</th>
                <th className="text-center py-3 px-4 text-cyan-300 font-mono">ELITE</th>
              </tr>
            </thead>
            <tbody>
              {comparisonData.features.map((feature, idx) => (
                <tr key={idx} className="border-b border-gray-700">
                  <td className="py-3 px-4 text-gray-300">{feature.name}</td>
                  <td className="text-center py-3 px-4">
                    {feature.basic ? (
                      <Check className="w-5 h-5 text-green-400 mx-auto" />
                    ) : (
                      <X className="w-5 h-5 text-red-400 mx-auto" />
                    )}
                  </td>
                  <td className="text-center py-3 px-4">
                    {feature.premium ? (
                      <Check className="w-5 h-5 text-green-400 mx-auto" />
                    ) : (
                      <X className="w-5 h-5 text-red-400 mx-auto" />
                    )}
                  </td>
                  <td className="text-center py-3 px-4">
                    {feature.elite ? (
                      <Check className="w-5 h-5 text-green-400 mx-auto" />
                    ) : (
                      <X className="w-5 h-5 text-red-400 mx-auto" />
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </motion.div>

      {/* Bottom CTA */}
      <div className="text-center mt-8">
        <p className="text-cyan-100 mb-4">
          Ready to transform your life? Choose your path and let's get started!
        </p>
        <div className="text-sm text-gray-400">
          All plans include a 7-day money-back guarantee
        </div>
      </div>
    </div>
  );
};

export default ServiceTierComparison;
