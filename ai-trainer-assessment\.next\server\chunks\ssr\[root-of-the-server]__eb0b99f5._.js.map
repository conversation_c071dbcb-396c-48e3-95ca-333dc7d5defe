{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trainer%20test/ai-trainer-assessment/src/components/Avatar.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useRef, useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { AvatarState, MouthSyncData } from '@/types';\n\ninterface HologramProps {\n  state: AvatarState;\n  className?: string;\n  size?: 'small' | 'medium' | 'large';\n}\n\nexport const Hologram: React.FC<HologramProps> = ({\n  state,\n  className = '',\n  size = 'medium'\n}) => {\n  const [currentMouthShape, setCurrentMouthShape] = useState<'closed' | 'mid' | 'open'>('closed');\n  const animationRef = useRef<number>();\n\n  // Size configurations\n  const sizeConfig = {\n    small: { width: 120, height: 120, scale: 0.8 },\n    medium: { width: 200, height: 200, scale: 1 },\n    large: { width: 300, height: 300, scale: 1.2 }\n  };\n\n  const config = sizeConfig[size];\n\n  // Animate mouth based on speech data\n  useEffect(() => {\n    if (state.mouthSyncData && state.currentAnimation === 'talking') {\n      const animateMouth = () => {\n        const currentTime = Date.now() - (state.mouthSyncData?.currentTime || 0);\n        const currentPhoneme = state.mouthSyncData?.phonemes.find(\n          p => currentTime >= p.start * 1000 && currentTime <= p.end * 1000\n        );\n\n        if (currentPhoneme) {\n          setCurrentMouthShape(currentPhoneme.phoneme as 'closed' | 'mid' | 'open');\n        } else {\n          setCurrentMouthShape('closed');\n        }\n\n        if (currentTime < (state.mouthSyncData?.duration || 0) * 1000) {\n          animationRef.current = requestAnimationFrame(animateMouth);\n        }\n      };\n\n      animationRef.current = requestAnimationFrame(animateMouth);\n    } else {\n      setCurrentMouthShape('closed');\n    }\n\n    return () => {\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n    };\n  }, [state.mouthSyncData, state.currentAnimation]);\n\n  // Animation variants for the orb\n  const containerVariants = {\n    idle: {\n      y: [0, -10, 0],\n      rotate: [0, 2, -2, 0],\n      transition: { duration: 4, repeat: Infinity, ease: 'easeInOut' }\n    },\n    talking: {\n      y: [0, -5, 0],\n      scale: [1, 1.03, 1],\n      transition: { duration: 0.6, repeat: Infinity, ease: 'easeInOut' }\n    },\n    listening: {\n      y: [0, -8, 0],\n      rotate: [0, 3, -3, 0],\n      transition: { duration: 2.5, repeat: Infinity, ease: 'easeInOut' }\n    },\n    thinking: {\n      y: [0, -12, 0],\n      rotate: [0, 10, -10, 0],\n      transition: { duration: 3, repeat: Infinity, ease: 'easeInOut' }\n    }\n  };\n\n  return (\n    <div className={`flex items-center justify-center ${className}`}>\n      <motion.div\n        className=\"relative\"\n        style={{ width: config.width, height: config.height }}\n        variants={containerVariants}\n        animate={state.currentAnimation}\n        initial=\"idle\"\n      >\n        {/* Main Orb - Translucent Core */}\n        <motion.div\n          className=\"absolute inset-0 rounded-full\"\n          style={{\n            background: 'radial-gradient(circle at 30% 30%, rgba(34, 211, 238, 0.4), rgba(59, 130, 246, 0.3), rgba(147, 51, 234, 0.2), transparent)',\n            backdropFilter: 'blur(2px)',\n            border: '1px solid rgba(34, 211, 238, 0.3)',\n            boxShadow: '0 0 60px rgba(34, 211, 238, 0.4), inset 0 0 60px rgba(59, 130, 246, 0.2)'\n          }}\n          animate={{\n            opacity: [0.6, 0.9, 0.6],\n            scale: state.currentAnimation === 'talking'\n              ? [1, 1.05, 1]\n              : [1, 1.02, 1]\n          }}\n          transition={{\n            duration: state.currentAnimation === 'talking' ? 0.5 : 2,\n            repeat: Infinity,\n            ease: 'easeInOut'\n          }}\n        />\n\n        {/* Inner Energy Core */}\n        <motion.div\n          className=\"absolute inset-8 rounded-full\"\n          style={{\n            background: 'radial-gradient(circle, rgba(34, 211, 238, 0.6), rgba(59, 130, 246, 0.4), transparent)',\n            filter: 'blur(1px)'\n          }}\n          animate={{\n            opacity: [0.4, 0.8, 0.4],\n            scale: [0.8, 1.2, 0.8],\n            rotate: [0, 360]\n          }}\n          transition={{\n            duration: 4,\n            repeat: Infinity,\n            ease: 'linear'\n          }}\n        />\n\n        {/* Floating Particles */}\n        {[...Array(6)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-2 h-2 bg-cyan-400 rounded-full\"\n            style={{\n              left: `${20 + (i * 10)}%`,\n              top: `${30 + (i * 8)}%`,\n              boxShadow: '0 0 10px rgba(34, 211, 238, 0.8)'\n            }}\n            animate={{\n              y: [-10, 10, -10],\n              x: [-5, 5, -5],\n              opacity: [0.3, 1, 0.3],\n              scale: [0.5, 1, 0.5]\n            }}\n            transition={{\n              duration: 3 + (i * 0.5),\n              repeat: Infinity,\n              ease: 'easeInOut',\n              delay: i * 0.3\n            }}\n          />\n        ))}\n\n        {/* Hologram Scan Lines */}\n        <motion.div\n          className=\"absolute inset-0 overflow-hidden rounded-full\"\n          style={{\n            background: 'repeating-linear-gradient(0deg, transparent, transparent 3px, rgba(34, 211, 238, 0.1) 3px, rgba(34, 211, 238, 0.1) 6px)'\n          }}\n          animate={{\n            y: [-30, 30, -30]\n          }}\n          transition={{\n            duration: 4,\n            repeat: Infinity,\n            ease: 'linear'\n          }}\n        />\n\n        {/* Outer Glow Ring */}\n        <motion.div\n          className=\"absolute -inset-4 rounded-full border border-cyan-400 opacity-30\"\n          animate={{\n            scale: [1, 1.1, 1],\n            opacity: [0.2, 0.5, 0.2],\n            rotate: [0, 360]\n          }}\n          transition={{\n            duration: 6,\n            repeat: Infinity,\n            ease: 'linear'\n          }}\n        />\n\n        {/* Speaking - Intense Energy Pulses */}\n        <AnimatePresence>\n          {state.currentAnimation === 'talking' && (\n            <>\n              {[...Array(4)].map((_, i) => (\n                <motion.div\n                  key={i}\n                  className=\"absolute rounded-full border border-cyan-400\"\n                  style={{\n                    inset: `${-8 - (i * 8)}px`,\n                    boxShadow: '0 0 30px rgba(34, 211, 238, 0.4)'\n                  }}\n                  initial={{ opacity: 0, scale: 0.8 }}\n                  animate={{\n                    opacity: [0, 0.8, 0],\n                    scale: [0.8, 1.4, 1.8]\n                  }}\n                  exit={{ opacity: 0 }}\n                  transition={{\n                    duration: 1.2,\n                    repeat: Infinity,\n                    delay: i * 0.15,\n                    ease: 'easeOut'\n                  }}\n                />\n              ))}\n              {/* Central energy burst */}\n              <motion.div\n                className=\"absolute inset-4 rounded-full bg-cyan-400\"\n                style={{\n                  filter: 'blur(2px)',\n                  opacity: 0.3\n                }}\n                animate={{\n                  scale: [0.5, 1.5, 0.5],\n                  opacity: [0.1, 0.4, 0.1]\n                }}\n                transition={{\n                  duration: 0.8,\n                  repeat: Infinity,\n                  ease: 'easeInOut'\n                }}\n              />\n            </>\n          )}\n        </AnimatePresence>\n\n        {/* Listening - Gentle Ripples */}\n        <AnimatePresence>\n          {state.currentAnimation === 'listening' && (\n            <>\n              {[...Array(5)].map((_, i) => (\n                <motion.div\n                  key={i}\n                  className=\"absolute rounded-full border border-green-400\"\n                  style={{\n                    inset: `${-12 - (i * 6)}px`,\n                    boxShadow: '0 0 20px rgba(34, 197, 94, 0.3)'\n                  }}\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{\n                    opacity: [0, 0.6, 0],\n                    scale: [0.9, 1.6, 2.2]\n                  }}\n                  exit={{ opacity: 0 }}\n                  transition={{\n                    duration: 3,\n                    repeat: Infinity,\n                    delay: i * 0.4,\n                    ease: 'easeOut'\n                  }}\n                />\n              ))}\n              {/* Listening indicator dots */}\n              {[...Array(3)].map((_, i) => (\n                <motion.div\n                  key={`dot-${i}`}\n                  className=\"absolute w-3 h-3 bg-green-400 rounded-full\"\n                  style={{\n                    left: `${40 + (i * 8)}%`,\n                    top: '45%',\n                    boxShadow: '0 0 15px rgba(34, 197, 94, 0.8)'\n                  }}\n                  animate={{\n                    opacity: [0.3, 1, 0.3],\n                    scale: [0.8, 1.2, 0.8]\n                  }}\n                  transition={{\n                    duration: 1.5,\n                    repeat: Infinity,\n                    delay: i * 0.2,\n                    ease: 'easeInOut'\n                  }}\n                />\n              ))}\n            </>\n          )}\n        </AnimatePresence>\n\n        {/* Thinking - Swirling Energy */}\n        <AnimatePresence>\n          {state.currentAnimation === 'thinking' && (\n            <>\n              {/* Orbital thinking particles */}\n              {[...Array(8)].map((_, i) => (\n                <motion.div\n                  key={i}\n                  className=\"absolute w-2 h-2 bg-purple-400 rounded-full\"\n                  style={{\n                    boxShadow: '0 0 12px rgba(147, 51, 234, 0.8)'\n                  }}\n                  animate={{\n                    rotate: [0, 360],\n                    scale: [0.5, 1.2, 0.5],\n                    opacity: [0.4, 1, 0.4]\n                  }}\n                  transition={{\n                    duration: 4,\n                    repeat: Infinity,\n                    delay: i * 0.2,\n                    ease: 'linear'\n                  }}\n                  initial={{\n                    left: '50%',\n                    top: '50%',\n                    x: `${Math.cos((i * 45) * Math.PI / 180) * 60}px`,\n                    y: `${Math.sin((i * 45) * Math.PI / 180) * 60}px`\n                  }}\n                />\n              ))}\n              {/* Central thinking core */}\n              <motion.div\n                className=\"absolute inset-6 rounded-full bg-purple-400\"\n                style={{\n                  filter: 'blur(3px)',\n                  opacity: 0.2\n                }}\n                animate={{\n                  scale: [0.8, 1.3, 0.8],\n                  opacity: [0.1, 0.3, 0.1],\n                  rotate: [0, 180, 360]\n                }}\n                transition={{\n                  duration: 2,\n                  repeat: Infinity,\n                  ease: 'easeInOut'\n                }}\n              />\n            </>\n          )}\n        </AnimatePresence>\n\n        {/* Subtle Glitch Effect */}\n        <motion.div\n          className=\"absolute inset-0 rounded-full\"\n          style={{\n            background: 'linear-gradient(90deg, transparent 0%, rgba(34, 211, 238, 0.05) 50%, transparent 100%)'\n          }}\n          animate={{\n            x: [-150, 150, -150],\n            opacity: [0, 0.2, 0]\n          }}\n          transition={{\n            duration: 6,\n            repeat: Infinity,\n            ease: 'linear'\n          }}\n        />\n      </motion.div>\n\n      {/* Holographic Status Display */}\n      <motion.div\n        className=\"absolute -bottom-16 text-center\"\n        initial={{ opacity: 0, y: 10 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.5 }}\n      >\n        <div className=\"bg-black bg-opacity-60 px-6 py-3 rounded-lg border border-cyan-400 backdrop-blur-sm\">\n          <motion.p\n            className=\"text-sm text-cyan-300 font-mono\"\n            animate={{\n              textShadow: state.currentAnimation === 'talking'\n                ? ['0 0 5px rgba(34, 211, 238, 0.8)', '0 0 15px rgba(34, 211, 238, 1)', '0 0 5px rgba(34, 211, 238, 0.8)']\n                : '0 0 5px rgba(34, 211, 238, 0.6)'\n            }}\n            transition={{ duration: 0.8, repeat: Infinity }}\n          >\n            {state.currentAnimation === 'idle' && '◦ ALEX ONLINE ◦'}\n            {state.currentAnimation === 'talking' && '◦ TRANSMITTING ◦'}\n            {state.currentAnimation === 'listening' && '◦ RECEIVING ◦'}\n            {state.currentAnimation === 'thinking' && '◦ PROCESSING ◦'}\n          </motion.p>\n        </div>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default Hologram;\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAAA;AAHA;;;;AAYO,MAAM,WAAoC,CAAC,EAChD,KAAK,EACL,YAAY,EAAE,EACd,OAAO,QAAQ,EAChB;IACC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,IAAA,iNAAQ,EAA4B;IACtF,MAAM,eAAe,IAAA,+MAAM;IAE3B,sBAAsB;IACtB,MAAM,aAAa;QACjB,OAAO;YAAE,OAAO;YAAK,QAAQ;YAAK,OAAO;QAAI;QAC7C,QAAQ;YAAE,OAAO;YAAK,QAAQ;YAAK,OAAO;QAAE;QAC5C,OAAO;YAAE,OAAO;YAAK,QAAQ;YAAK,OAAO;QAAI;IAC/C;IAEA,MAAM,SAAS,UAAU,CAAC,KAAK;IAE/B,qCAAqC;IACrC,IAAA,kNAAS,EAAC;QACR,IAAI,MAAM,aAAa,IAAI,MAAM,gBAAgB,KAAK,WAAW;YAC/D,MAAM,eAAe;gBACnB,MAAM,cAAc,KAAK,GAAG,KAAK,CAAC,MAAM,aAAa,EAAE,eAAe,CAAC;gBACvE,MAAM,iBAAiB,MAAM,aAAa,EAAE,SAAS,KACnD,CAAA,IAAK,eAAe,EAAE,KAAK,GAAG,QAAQ,eAAe,EAAE,GAAG,GAAG;gBAG/D,IAAI,gBAAgB;oBAClB,qBAAqB,eAAe,OAAO;gBAC7C,OAAO;oBACL,qBAAqB;gBACvB;gBAEA,IAAI,cAAc,CAAC,MAAM,aAAa,EAAE,YAAY,CAAC,IAAI,MAAM;oBAC7D,aAAa,OAAO,GAAG,sBAAsB;gBAC/C;YACF;YAEA,aAAa,OAAO,GAAG,sBAAsB;QAC/C,OAAO;YACL,qBAAqB;QACvB;QAEA,OAAO;YACL,IAAI,aAAa,OAAO,EAAE;gBACxB,qBAAqB,aAAa,OAAO;YAC3C;QACF;IACF,GAAG;QAAC,MAAM,aAAa;QAAE,MAAM,gBAAgB;KAAC;IAEhD,iCAAiC;IACjC,MAAM,oBAAoB;QACxB,MAAM;YACJ,GAAG;gBAAC;gBAAG,CAAC;gBAAI;aAAE;YACd,QAAQ;gBAAC;gBAAG;gBAAG,CAAC;gBAAG;aAAE;YACrB,YAAY;gBAAE,UAAU;gBAAG,QAAQ;gBAAU,MAAM;YAAY;QACjE;QACA,SAAS;YACP,GAAG;gBAAC;gBAAG,CAAC;gBAAG;aAAE;YACb,OAAO;gBAAC;gBAAG;gBAAM;aAAE;YACnB,YAAY;gBAAE,UAAU;gBAAK,QAAQ;gBAAU,MAAM;YAAY;QACnE;QACA,WAAW;YACT,GAAG;gBAAC;gBAAG,CAAC;gBAAG;aAAE;YACb,QAAQ;gBAAC;gBAAG;gBAAG,CAAC;gBAAG;aAAE;YACrB,YAAY;gBAAE,UAAU;gBAAK,QAAQ;gBAAU,MAAM;YAAY;QACnE;QACA,UAAU;YACR,GAAG;gBAAC;gBAAG,CAAC;gBAAI;aAAE;YACd,QAAQ;gBAAC;gBAAG;gBAAI,CAAC;gBAAI;aAAE;YACvB,YAAY;gBAAE,UAAU;gBAAG,QAAQ;gBAAU,MAAM;YAAY;QACjE;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,iCAAiC,EAAE,WAAW;;0BAC7D,8OAAC,oMAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBAAE,OAAO,OAAO,KAAK;oBAAE,QAAQ,OAAO,MAAM;gBAAC;gBACpD,UAAU;gBACV,SAAS,MAAM,gBAAgB;gBAC/B,SAAQ;;kCAGR,8OAAC,oMAAM,CAAC,GAAG;wBACT,WAAU;wBACV,OAAO;4BACL,YAAY;4BACZ,gBAAgB;4BAChB,QAAQ;4BACR,WAAW;wBACb;wBACA,SAAS;4BACP,SAAS;gCAAC;gCAAK;gCAAK;6BAAI;4BACxB,OAAO,MAAM,gBAAgB,KAAK,YAC9B;gCAAC;gCAAG;gCAAM;6BAAE,GACZ;gCAAC;gCAAG;gCAAM;6BAAE;wBAClB;wBACA,YAAY;4BACV,UAAU,MAAM,gBAAgB,KAAK,YAAY,MAAM;4BACvD,QAAQ;4BACR,MAAM;wBACR;;;;;;kCAIF,8OAAC,oMAAM,CAAC,GAAG;wBACT,WAAU;wBACV,OAAO;4BACL,YAAY;4BACZ,QAAQ;wBACV;wBACA,SAAS;4BACP,SAAS;gCAAC;gCAAK;gCAAK;6BAAI;4BACxB,OAAO;gCAAC;gCAAK;gCAAK;6BAAI;4BACtB,QAAQ;gCAAC;gCAAG;6BAAI;wBAClB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;oBAID;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,oMAAM,CAAC,GAAG;4BAET,WAAU;4BACV,OAAO;gCACL,MAAM,GAAG,KAAM,IAAI,GAAI,CAAC,CAAC;gCACzB,KAAK,GAAG,KAAM,IAAI,EAAG,CAAC,CAAC;gCACvB,WAAW;4BACb;4BACA,SAAS;gCACP,GAAG;oCAAC,CAAC;oCAAI;oCAAI,CAAC;iCAAG;gCACjB,GAAG;oCAAC,CAAC;oCAAG;oCAAG,CAAC;iCAAE;gCACd,SAAS;oCAAC;oCAAK;oCAAG;iCAAI;gCACtB,OAAO;oCAAC;oCAAK;oCAAG;iCAAI;4BACtB;4BACA,YAAY;gCACV,UAAU,IAAK,IAAI;gCACnB,QAAQ;gCACR,MAAM;gCACN,OAAO,IAAI;4BACb;2BAlBK;;;;;kCAuBT,8OAAC,oMAAM,CAAC,GAAG;wBACT,WAAU;wBACV,OAAO;4BACL,YAAY;wBACd;wBACA,SAAS;4BACP,GAAG;gCAAC,CAAC;gCAAI;gCAAI,CAAC;6BAAG;wBACnB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;kCAIF,8OAAC,oMAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;4BAClB,SAAS;gCAAC;gCAAK;gCAAK;6BAAI;4BACxB,QAAQ;gCAAC;gCAAG;6BAAI;wBAClB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;kCAIF,8OAAC,4MAAe;kCACb,MAAM,gBAAgB,KAAK,2BAC1B;;gCACG;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,oMAAM,CAAC,GAAG;wCAET,WAAU;wCACV,OAAO;4CACL,OAAO,GAAG,CAAC,IAAK,IAAI,EAAG,EAAE,CAAC;4CAC1B,WAAW;wCACb;wCACA,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,SAAS;4CACP,SAAS;gDAAC;gDAAG;gDAAK;6CAAE;4CACpB,OAAO;gDAAC;gDAAK;gDAAK;6CAAI;wCACxB;wCACA,MAAM;4CAAE,SAAS;wCAAE;wCACnB,YAAY;4CACV,UAAU;4CACV,QAAQ;4CACR,OAAO,IAAI;4CACX,MAAM;wCACR;uCAjBK;;;;;8CAqBT,8OAAC,oMAAM,CAAC,GAAG;oCACT,WAAU;oCACV,OAAO;wCACL,QAAQ;wCACR,SAAS;oCACX;oCACA,SAAS;wCACP,OAAO;4CAAC;4CAAK;4CAAK;yCAAI;wCACtB,SAAS;4CAAC;4CAAK;4CAAK;yCAAI;oCAC1B;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,MAAM;oCACR;;;;;;;;;;;;;kCAOR,8OAAC,4MAAe;kCACb,MAAM,gBAAgB,KAAK,6BAC1B;;gCACG;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,oMAAM,CAAC,GAAG;wCAET,WAAU;wCACV,OAAO;4CACL,OAAO,GAAG,CAAC,KAAM,IAAI,EAAG,EAAE,CAAC;4CAC3B,WAAW;wCACb;wCACA,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,SAAS;4CACP,SAAS;gDAAC;gDAAG;gDAAK;6CAAE;4CACpB,OAAO;gDAAC;gDAAK;gDAAK;6CAAI;wCACxB;wCACA,MAAM;4CAAE,SAAS;wCAAE;wCACnB,YAAY;4CACV,UAAU;4CACV,QAAQ;4CACR,OAAO,IAAI;4CACX,MAAM;wCACR;uCAjBK;;;;;gCAqBR;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,oMAAM,CAAC,GAAG;wCAET,WAAU;wCACV,OAAO;4CACL,MAAM,GAAG,KAAM,IAAI,EAAG,CAAC,CAAC;4CACxB,KAAK;4CACL,WAAW;wCACb;wCACA,SAAS;4CACP,SAAS;gDAAC;gDAAK;gDAAG;6CAAI;4CACtB,OAAO;gDAAC;gDAAK;gDAAK;6CAAI;wCACxB;wCACA,YAAY;4CACV,UAAU;4CACV,QAAQ;4CACR,OAAO,IAAI;4CACX,MAAM;wCACR;uCAhBK,CAAC,IAAI,EAAE,GAAG;;;;;;;;;;;;kCAwBzB,8OAAC,4MAAe;kCACb,MAAM,gBAAgB,KAAK,4BAC1B;;gCAEG;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,oMAAM,CAAC,GAAG;wCAET,WAAU;wCACV,OAAO;4CACL,WAAW;wCACb;wCACA,SAAS;4CACP,QAAQ;gDAAC;gDAAG;6CAAI;4CAChB,OAAO;gDAAC;gDAAK;gDAAK;6CAAI;4CACtB,SAAS;gDAAC;gDAAK;gDAAG;6CAAI;wCACxB;wCACA,YAAY;4CACV,UAAU;4CACV,QAAQ;4CACR,OAAO,IAAI;4CACX,MAAM;wCACR;wCACA,SAAS;4CACP,MAAM;4CACN,KAAK;4CACL,GAAG,GAAG,KAAK,GAAG,CAAC,AAAC,IAAI,KAAM,KAAK,EAAE,GAAG,OAAO,GAAG,EAAE,CAAC;4CACjD,GAAG,GAAG,KAAK,GAAG,CAAC,AAAC,IAAI,KAAM,KAAK,EAAE,GAAG,OAAO,GAAG,EAAE,CAAC;wCACnD;uCArBK;;;;;8CAyBT,8OAAC,oMAAM,CAAC,GAAG;oCACT,WAAU;oCACV,OAAO;wCACL,QAAQ;wCACR,SAAS;oCACX;oCACA,SAAS;wCACP,OAAO;4CAAC;4CAAK;4CAAK;yCAAI;wCACtB,SAAS;4CAAC;4CAAK;4CAAK;yCAAI;wCACxB,QAAQ;4CAAC;4CAAG;4CAAK;yCAAI;oCACvB;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,MAAM;oCACR;;;;;;;;;;;;;kCAOR,8OAAC,oMAAM,CAAC,GAAG;wBACT,WAAU;wBACV,OAAO;4BACL,YAAY;wBACd;wBACA,SAAS;4BACP,GAAG;gCAAC,CAAC;gCAAK;gCAAK,CAAC;6BAAI;4BACpB,SAAS;gCAAC;gCAAG;gCAAK;6BAAE;wBACtB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;;;;;;;0BAKJ,8OAAC,oMAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;0BAEzB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,oMAAM,CAAC,CAAC;wBACP,WAAU;wBACV,SAAS;4BACP,YAAY,MAAM,gBAAgB,KAAK,YACnC;gCAAC;gCAAmC;gCAAkC;6BAAkC,GACxG;wBACN;wBACA,YAAY;4BAAE,UAAU;4BAAK,QAAQ;wBAAS;;4BAE7C,MAAM,gBAAgB,KAAK,UAAU;4BACrC,MAAM,gBAAgB,KAAK,aAAa;4BACxC,MAAM,gBAAgB,KAAK,eAAe;4BAC1C,MAAM,gBAAgB,KAAK,cAAc;;;;;;;;;;;;;;;;;;;;;;;AAMtD;uCAEe", "debugId": null}}, {"offset": {"line": 652, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trainer%20test/ai-trainer-assessment/src/services/speechService.ts"], "sourcesContent": ["import { SpeechState, Phoneme, MouthSyncData } from '@/types';\n\nexport class SpeechService {\n  private mediaRecorder: MediaRecorder | null = null;\n  private audioChunks: Blob[] = [];\n  private recognition: SpeechRecognition | null = null;\n  private synthesis: SpeechSynthesis;\n  private currentUtterance: SpeechSynthesisUtterance | null = null;\n  private currentAudio: HTMLAudioElement | null = null;\n  private isUsingOpenAITTS: boolean = false;\n  private isSpeaking: boolean = false;\n\n  constructor() {\n    // Only initialize in browser environment\n    if (typeof window !== 'undefined') {\n      this.synthesis = window.speechSynthesis;\n      this.initializeSpeechRecognition();\n    }\n  }\n\n  private initializeSpeechRecognition() {\n    if (typeof window !== 'undefined' && ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {\n      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n      this.recognition = new SpeechRecognition();\n\n      this.recognition.continuous = true;\n      this.recognition.interimResults = true;\n      this.recognition.lang = 'en-US';\n    }\n  }\n\n  // Start listening for speech\n  async startListening(\n    onTranscript: (transcript: string, isFinal: boolean) => void,\n    onError: (error: string) => void\n  ): Promise<void> {\n    if (!this.recognition) {\n      onError('Speech recognition not supported');\n      return;\n    }\n\n    try {\n      this.recognition.onresult = (event) => {\n        let transcript = '';\n        let isFinal = false;\n\n        for (let i = event.resultIndex; i < event.results.length; i++) {\n          const result = event.results[i];\n          transcript += result[0].transcript;\n          if (result.isFinal) {\n            isFinal = true;\n          }\n        }\n\n        onTranscript(transcript, isFinal);\n      };\n\n      this.recognition.onerror = (event) => {\n        onError(`Speech recognition error: ${event.error}`);\n      };\n\n      this.recognition.start();\n    } catch (error) {\n      onError(`Failed to start speech recognition: ${error}`);\n    }\n  }\n\n  // Stop listening\n  stopListening(): void {\n    if (this.recognition) {\n      this.recognition.stop();\n    }\n  }\n\n  // Convert speech to text using Whisper API\n  async transcribeAudio(audioBlob: Blob): Promise<string> {\n    try {\n      const formData = new FormData();\n      formData.append('file', audioBlob, 'audio.wav');\n      formData.append('model', 'whisper-1');\n\n      const response = await fetch('/api/transcribe', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        throw new Error(`Transcription failed: ${response.statusText}`);\n      }\n\n      const result = await response.json();\n      return result.text;\n    } catch (error) {\n      console.error('Transcription error:', error);\n      throw error;\n    }\n  }\n\n  // Text-to-speech using OpenAI TTS API\n  async speak(\n    text: string,\n    onStart?: () => void,\n    onEnd?: () => void,\n    onMouthSync?: (data: MouthSyncData) => void\n  ): Promise<void> {\n    console.log('🎤 SPEAK CALLED:', { text: text.substring(0, 50) + '...', isSpeaking: this.isSpeaking });\n\n    // CRITICAL: Stop any ongoing speech first\n    this.stopSpeaking();\n\n    // Set speaking state IMMEDIATELY\n    this.isSpeaking = true;\n    console.log('🎤 Speaking state set to TRUE');\n\n    try {\n      // Try OpenAI TTS first\n      this.isUsingOpenAITTS = true;\n      console.log('🎤 Attempting OpenAI TTS...');\n\n      // Call onStart immediately\n      onStart?.();\n      console.log('🎤 onStart called');\n\n      // Generate mouth sync data immediately\n      const mouthSyncData = this.generateMouthSyncData(text);\n      onMouthSync?.(mouthSyncData);\n      console.log('🎤 onMouthSync called');\n\n      // Call OpenAI TTS API\n      const response = await fetch('/api/text-to-speech', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ text })\n      });\n\n      if (!response.ok) {\n        console.warn('❌ OpenAI TTS API failed:', response.statusText);\n        this.isUsingOpenAITTS = false;\n        this.isSpeaking = false; // Reset before fallback\n        return this.fallbackSpeak(text, onStart, onEnd, onMouthSync);\n      }\n\n      console.log('✅ OpenAI TTS API success, creating audio...');\n\n      // Get audio blob from response\n      const audioBlob = await response.blob();\n\n      // CRITICAL: Check if we're still supposed to be speaking\n      if (!this.isSpeaking) {\n        console.log('🛑 Speaking was cancelled, aborting audio creation');\n        return;\n      }\n\n      // Create audio element and play\n      this.currentAudio = new Audio();\n      const audioUrl = URL.createObjectURL(audioBlob);\n      this.currentAudio.src = audioUrl;\n      this.currentAudio.volume = 0.8;\n\n      return new Promise((resolve, reject) => {\n        if (!this.currentAudio || !this.isSpeaking) {\n          console.log('🛑 Audio creation failed or speaking cancelled');\n          this.isSpeaking = false;\n          this.isUsingOpenAITTS = false;\n          return reject(new Error('Audio creation failed or cancelled'));\n        }\n\n        this.currentAudio.onended = () => {\n          console.log('✅ OpenAI TTS audio ended normally');\n          URL.revokeObjectURL(audioUrl);\n          this.currentAudio = null;\n          this.isSpeaking = false;\n          this.isUsingOpenAITTS = false;\n          onEnd?.();\n          resolve();\n        };\n\n        this.currentAudio.onerror = (e) => {\n          console.warn('❌ OpenAI TTS audio error:', e);\n          URL.revokeObjectURL(audioUrl);\n          this.currentAudio = null;\n          this.isUsingOpenAITTS = false;\n          this.isSpeaking = false;\n          // Don't fallback on audio error - just fail\n          onEnd?.();\n          reject(new Error('Audio playback failed'));\n        };\n\n        console.log('🎵 Starting OpenAI TTS audio playback...');\n        this.currentAudio.play().catch((playError) => {\n          console.warn('❌ OpenAI TTS play error:', playError);\n          URL.revokeObjectURL(audioUrl);\n          this.currentAudio = null;\n          this.isUsingOpenAITTS = false;\n          this.isSpeaking = false;\n          // Don't fallback on play error - just fail\n          onEnd?.();\n          reject(playError);\n        });\n      });\n\n    } catch (error) {\n      console.warn('❌ OpenAI TTS error:', error);\n      this.isUsingOpenAITTS = false;\n      this.isSpeaking = false;\n      // Only fallback on network/API errors, not audio errors\n      return this.fallbackSpeak(text, onStart, onEnd, onMouthSync);\n    }\n  }\n\n  // Fallback to browser speech synthesis\n  private async fallbackSpeak(\n    text: string,\n    onStart?: () => void,\n    onEnd?: () => void,\n    onMouthSync?: (data: MouthSyncData) => void\n  ): Promise<void> {\n    console.log('🔄 FALLBACK SPEAK called');\n\n    return new Promise((resolve, reject) => {\n      if (!this.synthesis) {\n        console.log('❌ Speech synthesis not supported');\n        this.isSpeaking = false;\n        reject(new Error('Speech synthesis not supported'));\n        return;\n      }\n\n      // CRITICAL: Only proceed if OpenAI TTS is not active\n      if (this.isUsingOpenAITTS) {\n        console.log('🛑 OpenAI TTS is active, aborting browser synthesis');\n        this.isSpeaking = false;\n        resolve();\n        return;\n      }\n\n      // Set speaking state for browser synthesis\n      this.isSpeaking = true;\n      console.log('🎤 Browser synthesis starting...');\n\n      // Cancel any ongoing browser speech\n      this.synthesis.cancel();\n\n      this.currentUtterance = new SpeechSynthesisUtterance(text);\n\n      // Configure voice settings\n      this.currentUtterance.rate = 0.9;\n      this.currentUtterance.pitch = 1.1;\n      this.currentUtterance.volume = 0.8;\n\n      // Try to find a good voice\n      const voices = this.synthesis.getVoices();\n      const preferredVoice = voices.find(voice =>\n        voice.name.includes('Google') && voice.lang.startsWith('en')\n      ) || voices.find(voice => voice.lang.startsWith('en'));\n\n      if (preferredVoice) {\n        this.currentUtterance.voice = preferredVoice;\n      }\n\n      this.currentUtterance.onstart = () => {\n        console.log('🎵 Browser synthesis started');\n        onStart?.();\n        if (onMouthSync) {\n          const mouthSyncData = this.generateMouthSyncData(text);\n          onMouthSync(mouthSyncData);\n        }\n      };\n\n      this.currentUtterance.onend = () => {\n        console.log('✅ Browser synthesis ended');\n        this.isSpeaking = false;\n        this.currentUtterance = null;\n        onEnd?.();\n        resolve();\n      };\n\n      this.currentUtterance.onerror = (event) => {\n        console.log('❌ Browser synthesis error:', event.error);\n        this.isSpeaking = false;\n        this.currentUtterance = null;\n        reject(new Error(`Speech synthesis error: ${event.error}`));\n      };\n\n      console.log('🎵 Starting browser speech synthesis...');\n      this.synthesis.speak(this.currentUtterance);\n    });\n  }\n\n  // Stop current speech\n  stopSpeaking(): void {\n    console.log('🛑 STOP SPEAKING called');\n\n    // Reset flags FIRST\n    const wasUsingSpeech = this.isSpeaking;\n    this.isSpeaking = false;\n    this.isUsingOpenAITTS = false;\n\n    if (wasUsingSpeech) {\n      console.log('🛑 Stopping active speech...');\n    }\n\n    // Stop OpenAI TTS audio\n    if (this.currentAudio) {\n      try {\n        this.currentAudio.pause();\n        this.currentAudio.currentTime = 0;\n        this.currentAudio.src = '';\n        this.currentAudio.onended = null;\n        this.currentAudio.onerror = null;\n        this.currentAudio = null;\n        console.log('🛑 OpenAI TTS audio stopped');\n      } catch (error) {\n        console.log('⚠️ Error stopping OpenAI audio:', error);\n      }\n    }\n\n    // Stop browser speech synthesis\n    if (this.synthesis) {\n      try {\n        this.synthesis.cancel();\n        console.log('🛑 Browser speech synthesis cancelled');\n      } catch (error) {\n        console.log('⚠️ Error stopping browser synthesis:', error);\n      }\n    }\n\n    // Clear current utterance\n    if (this.currentUtterance) {\n      this.currentUtterance.onstart = null;\n      this.currentUtterance.onend = null;\n      this.currentUtterance.onerror = null;\n      this.currentUtterance = null;\n      console.log('🛑 Current utterance cleared');\n    }\n\n    console.log('🛑 All speech stopped');\n  }\n\n  // Check if currently speaking\n  isSpeakingNow(): boolean {\n    return this.isSpeaking;\n  }\n\n  // Get current speech method\n  getCurrentSpeechMethod(): 'openai' | 'browser' | 'none' {\n    if (!this.isSpeaking) return 'none';\n    return this.isUsingOpenAITTS ? 'openai' : 'browser';\n  }\n\n  // Generate mouth sync data for avatar animation\n  private generateMouthSyncData(text: string): MouthSyncData {\n    const words = text.split(' ');\n    const phonemes: Phoneme[] = [];\n    let currentTime = 0;\n    const averageWordDuration = 0.6; // seconds per word\n\n    words.forEach((word, index) => {\n      const wordDuration = averageWordDuration * (word.length / 5); // Adjust based on word length\n      \n      // Simple phoneme mapping (in a real app, you'd use a proper phoneme library)\n      const wordPhonemes = this.mapWordToPhonemes(word);\n      const phonemeDuration = wordDuration / wordPhonemes.length;\n\n      wordPhonemes.forEach((phoneme, pIndex) => {\n        phonemes.push({\n          phoneme,\n          start: currentTime + (pIndex * phonemeDuration),\n          end: currentTime + ((pIndex + 1) * phonemeDuration),\n          intensity: this.getPhonemeIntensity(phoneme)\n        });\n      });\n\n      currentTime += wordDuration + 0.1; // Small pause between words\n    });\n\n    return {\n      phonemes,\n      duration: currentTime,\n      currentTime: 0\n    };\n  }\n\n  // Simple phoneme mapping (simplified for demo)\n  private mapWordToPhonemes(word: string): string[] {\n    // This is a very simplified mapping. In production, use a proper phoneme library\n    const vowels = ['a', 'e', 'i', 'o', 'u'];\n    const phonemes: string[] = [];\n    \n    for (let i = 0; i < word.length; i++) {\n      const char = word[i].toLowerCase();\n      if (vowels.includes(char)) {\n        phonemes.push('open'); // Open mouth for vowels\n      } else if (char === 'm' || char === 'p' || char === 'b') {\n        phonemes.push('closed'); // Closed mouth for bilabials\n      } else {\n        phonemes.push('mid'); // Mid position for other consonants\n      }\n    }\n\n    return phonemes.length > 0 ? phonemes : ['mid'];\n  }\n\n  // Get intensity for mouth animation\n  private getPhonemeIntensity(phoneme: string): number {\n    switch (phoneme) {\n      case 'open': return 0.8;\n      case 'closed': return 0.1;\n      case 'mid': return 0.5;\n      default: return 0.5;\n    }\n  }\n\n  // Record audio for Whisper transcription\n  async startRecording(): Promise<void> {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\n      this.mediaRecorder = new MediaRecorder(stream);\n      this.audioChunks = [];\n\n      this.mediaRecorder.ondataavailable = (event) => {\n        this.audioChunks.push(event.data);\n      };\n\n      this.mediaRecorder.start();\n    } catch (error) {\n      throw new Error(`Failed to start recording: ${error}`);\n    }\n  }\n\n  async stopRecording(): Promise<Blob> {\n    return new Promise((resolve, reject) => {\n      if (!this.mediaRecorder) {\n        reject(new Error('No active recording'));\n        return;\n      }\n\n      this.mediaRecorder.onstop = () => {\n        const audioBlob = new Blob(this.audioChunks, { type: 'audio/wav' });\n        resolve(audioBlob);\n      };\n\n      this.mediaRecorder.stop();\n      \n      // Stop all tracks to release microphone\n      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n    });\n  }\n\n  // Check if speech services are available\n  isAvailable(): boolean {\n    return typeof window !== 'undefined' && !!(this.recognition && this.synthesis);\n  }\n}\n\n// Global speech service instance (only in browser)\nexport const speechService = typeof window !== 'undefined' ? new SpeechService() : null;\n"], "names": [], "mappings": ";;;;;;AAEO,MAAM;IACH,gBAAsC,KAAK;IAC3C,cAAsB,EAAE,CAAC;IACzB,cAAwC,KAAK;IAC7C,UAA2B;IAC3B,mBAAoD,KAAK;IACzD,eAAwC,KAAK;IAC7C,mBAA4B,MAAM;IAClC,aAAsB,MAAM;IAEpC,aAAc;QACZ,yCAAyC;QACzC;;IAIF;IAEQ,8BAA8B;QACpC,IAAI,gBAAkB,eAAe,CAAC,6BAA6B,UAAU,uBAAuB,MAAM;;IAQ5G;IAEA,6BAA6B;IAC7B,MAAM,eACJ,YAA4D,EAC5D,OAAgC,EACjB;QACf,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,QAAQ;YACR;QACF;QAEA,IAAI;YACF,IAAI,CAAC,WAAW,CAAC,QAAQ,GAAG,CAAC;gBAC3B,IAAI,aAAa;gBACjB,IAAI,UAAU;gBAEd,IAAK,IAAI,IAAI,MAAM,WAAW,EAAE,IAAI,MAAM,OAAO,CAAC,MAAM,EAAE,IAAK;oBAC7D,MAAM,SAAS,MAAM,OAAO,CAAC,EAAE;oBAC/B,cAAc,MAAM,CAAC,EAAE,CAAC,UAAU;oBAClC,IAAI,OAAO,OAAO,EAAE;wBAClB,UAAU;oBACZ;gBACF;gBAEA,aAAa,YAAY;YAC3B;YAEA,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG,CAAC;gBAC1B,QAAQ,CAAC,0BAA0B,EAAE,MAAM,KAAK,EAAE;YACpD;YAEA,IAAI,CAAC,WAAW,CAAC,KAAK;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,CAAC,oCAAoC,EAAE,OAAO;QACxD;IACF;IAEA,iBAAiB;IACjB,gBAAsB;QACpB,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,WAAW,CAAC,IAAI;QACvB;IACF;IAEA,2CAA2C;IAC3C,MAAM,gBAAgB,SAAe,EAAmB;QACtD,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ,WAAW;YACnC,SAAS,MAAM,CAAC,SAAS;YAEzB,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,SAAS,UAAU,EAAE;YAChE;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,OAAO,OAAO,IAAI;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR;IACF;IAEA,sCAAsC;IACtC,MAAM,MACJ,IAAY,EACZ,OAAoB,EACpB,KAAkB,EAClB,WAA2C,EAC5B;QACf,QAAQ,GAAG,CAAC,oBAAoB;YAAE,MAAM,KAAK,SAAS,CAAC,GAAG,MAAM;YAAO,YAAY,IAAI,CAAC,UAAU;QAAC;QAEnG,0CAA0C;QAC1C,IAAI,CAAC,YAAY;QAEjB,iCAAiC;QACjC,IAAI,CAAC,UAAU,GAAG;QAClB,QAAQ,GAAG,CAAC;QAEZ,IAAI;YACF,uBAAuB;YACvB,IAAI,CAAC,gBAAgB,GAAG;YACxB,QAAQ,GAAG,CAAC;YAEZ,2BAA2B;YAC3B;YACA,QAAQ,GAAG,CAAC;YAEZ,uCAAuC;YACvC,MAAM,gBAAgB,IAAI,CAAC,qBAAqB,CAAC;YACjD,cAAc;YACd,QAAQ,GAAG,CAAC;YAEZ,sBAAsB;YACtB,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAK;YAC9B;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,QAAQ,IAAI,CAAC,4BAA4B,SAAS,UAAU;gBAC5D,IAAI,CAAC,gBAAgB,GAAG;gBACxB,IAAI,CAAC,UAAU,GAAG,OAAO,wBAAwB;gBACjD,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,SAAS,OAAO;YAClD;YAEA,QAAQ,GAAG,CAAC;YAEZ,+BAA+B;YAC/B,MAAM,YAAY,MAAM,SAAS,IAAI;YAErC,yDAAyD;YACzD,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;gBACpB,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,gCAAgC;YAChC,IAAI,CAAC,YAAY,GAAG,IAAI;YACxB,MAAM,WAAW,IAAI,eAAe,CAAC;YACrC,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG;YACxB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG;YAE3B,OAAO,IAAI,QAAQ,CAAC,SAAS;gBAC3B,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;oBAC1C,QAAQ,GAAG,CAAC;oBACZ,IAAI,CAAC,UAAU,GAAG;oBAClB,IAAI,CAAC,gBAAgB,GAAG;oBACxB,OAAO,OAAO,IAAI,MAAM;gBAC1B;gBAEA,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG;oBAC1B,QAAQ,GAAG,CAAC;oBACZ,IAAI,eAAe,CAAC;oBACpB,IAAI,CAAC,YAAY,GAAG;oBACpB,IAAI,CAAC,UAAU,GAAG;oBAClB,IAAI,CAAC,gBAAgB,GAAG;oBACxB;oBACA;gBACF;gBAEA,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,CAAC;oBAC3B,QAAQ,IAAI,CAAC,6BAA6B;oBAC1C,IAAI,eAAe,CAAC;oBACpB,IAAI,CAAC,YAAY,GAAG;oBACpB,IAAI,CAAC,gBAAgB,GAAG;oBACxB,IAAI,CAAC,UAAU,GAAG;oBAClB,4CAA4C;oBAC5C;oBACA,OAAO,IAAI,MAAM;gBACnB;gBAEA,QAAQ,GAAG,CAAC;gBACZ,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;oBAC9B,QAAQ,IAAI,CAAC,4BAA4B;oBACzC,IAAI,eAAe,CAAC;oBACpB,IAAI,CAAC,YAAY,GAAG;oBACpB,IAAI,CAAC,gBAAgB,GAAG;oBACxB,IAAI,CAAC,UAAU,GAAG;oBAClB,2CAA2C;oBAC3C;oBACA,OAAO;gBACT;YACF;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,uBAAuB;YACpC,IAAI,CAAC,gBAAgB,GAAG;YACxB,IAAI,CAAC,UAAU,GAAG;YAClB,wDAAwD;YACxD,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,SAAS,OAAO;QAClD;IACF;IAEA,uCAAuC;IACvC,MAAc,cACZ,IAAY,EACZ,OAAoB,EACpB,KAAkB,EAClB,WAA2C,EAC5B;QACf,QAAQ,GAAG,CAAC;QAEZ,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACnB,QAAQ,GAAG,CAAC;gBACZ,IAAI,CAAC,UAAU,GAAG;gBAClB,OAAO,IAAI,MAAM;gBACjB;YACF;YAEA,qDAAqD;YACrD,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACzB,QAAQ,GAAG,CAAC;gBACZ,IAAI,CAAC,UAAU,GAAG;gBAClB;gBACA;YACF;YAEA,2CAA2C;YAC3C,IAAI,CAAC,UAAU,GAAG;YAClB,QAAQ,GAAG,CAAC;YAEZ,oCAAoC;YACpC,IAAI,CAAC,SAAS,CAAC,MAAM;YAErB,IAAI,CAAC,gBAAgB,GAAG,IAAI,yBAAyB;YAErD,2BAA2B;YAC3B,IAAI,CAAC,gBAAgB,CAAC,IAAI,GAAG;YAC7B,IAAI,CAAC,gBAAgB,CAAC,KAAK,GAAG;YAC9B,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG;YAE/B,2BAA2B;YAC3B,MAAM,SAAS,IAAI,CAAC,SAAS,CAAC,SAAS;YACvC,MAAM,iBAAiB,OAAO,IAAI,CAAC,CAAA,QACjC,MAAM,IAAI,CAAC,QAAQ,CAAC,aAAa,MAAM,IAAI,CAAC,UAAU,CAAC,UACpD,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,IAAI,CAAC,UAAU,CAAC;YAEhD,IAAI,gBAAgB;gBAClB,IAAI,CAAC,gBAAgB,CAAC,KAAK,GAAG;YAChC;YAEA,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG;gBAC9B,QAAQ,GAAG,CAAC;gBACZ;gBACA,IAAI,aAAa;oBACf,MAAM,gBAAgB,IAAI,CAAC,qBAAqB,CAAC;oBACjD,YAAY;gBACd;YACF;YAEA,IAAI,CAAC,gBAAgB,CAAC,KAAK,GAAG;gBAC5B,QAAQ,GAAG,CAAC;gBACZ,IAAI,CAAC,UAAU,GAAG;gBAClB,IAAI,CAAC,gBAAgB,GAAG;gBACxB;gBACA;YACF;YAEA,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG,CAAC;gBAC/B,QAAQ,GAAG,CAAC,8BAA8B,MAAM,KAAK;gBACrD,IAAI,CAAC,UAAU,GAAG;gBAClB,IAAI,CAAC,gBAAgB,GAAG;gBACxB,OAAO,IAAI,MAAM,CAAC,wBAAwB,EAAE,MAAM,KAAK,EAAE;YAC3D;YAEA,QAAQ,GAAG,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB;QAC5C;IACF;IAEA,sBAAsB;IACtB,eAAqB;QACnB,QAAQ,GAAG,CAAC;QAEZ,oBAAoB;QACpB,MAAM,iBAAiB,IAAI,CAAC,UAAU;QACtC,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,gBAAgB,GAAG;QAExB,IAAI,gBAAgB;YAClB,QAAQ,GAAG,CAAC;QACd;QAEA,wBAAwB;QACxB,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,IAAI;gBACF,IAAI,CAAC,YAAY,CAAC,KAAK;gBACvB,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG;gBAChC,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG;gBACxB,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG;gBAC5B,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG;gBAC5B,IAAI,CAAC,YAAY,GAAG;gBACpB,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC,mCAAmC;YACjD;QACF;QAEA,gCAAgC;QAChC,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI;gBACF,IAAI,CAAC,SAAS,CAAC,MAAM;gBACrB,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC,wCAAwC;YACtD;QACF;QAEA,0BAA0B;QAC1B,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG;YAChC,IAAI,CAAC,gBAAgB,CAAC,KAAK,GAAG;YAC9B,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG;YAChC,IAAI,CAAC,gBAAgB,GAAG;YACxB,QAAQ,GAAG,CAAC;QACd;QAEA,QAAQ,GAAG,CAAC;IACd;IAEA,8BAA8B;IAC9B,gBAAyB;QACvB,OAAO,IAAI,CAAC,UAAU;IACxB;IAEA,4BAA4B;IAC5B,yBAAwD;QACtD,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO;QAC7B,OAAO,IAAI,CAAC,gBAAgB,GAAG,WAAW;IAC5C;IAEA,gDAAgD;IACxC,sBAAsB,IAAY,EAAiB;QACzD,MAAM,QAAQ,KAAK,KAAK,CAAC;QACzB,MAAM,WAAsB,EAAE;QAC9B,IAAI,cAAc;QAClB,MAAM,sBAAsB,KAAK,mBAAmB;QAEpD,MAAM,OAAO,CAAC,CAAC,MAAM;YACnB,MAAM,eAAe,sBAAsB,CAAC,KAAK,MAAM,GAAG,CAAC,GAAG,8BAA8B;YAE5F,6EAA6E;YAC7E,MAAM,eAAe,IAAI,CAAC,iBAAiB,CAAC;YAC5C,MAAM,kBAAkB,eAAe,aAAa,MAAM;YAE1D,aAAa,OAAO,CAAC,CAAC,SAAS;gBAC7B,SAAS,IAAI,CAAC;oBACZ;oBACA,OAAO,cAAe,SAAS;oBAC/B,KAAK,cAAe,CAAC,SAAS,CAAC,IAAI;oBACnC,WAAW,IAAI,CAAC,mBAAmB,CAAC;gBACtC;YACF;YAEA,eAAe,eAAe,KAAK,4BAA4B;QACjE;QAEA,OAAO;YACL;YACA,UAAU;YACV,aAAa;QACf;IACF;IAEA,+CAA+C;IACvC,kBAAkB,IAAY,EAAY;QAChD,iFAAiF;QACjF,MAAM,SAAS;YAAC;YAAK;YAAK;YAAK;YAAK;SAAI;QACxC,MAAM,WAAqB,EAAE;QAE7B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YACpC,MAAM,OAAO,IAAI,CAAC,EAAE,CAAC,WAAW;YAChC,IAAI,OAAO,QAAQ,CAAC,OAAO;gBACzB,SAAS,IAAI,CAAC,SAAS,wBAAwB;YACjD,OAAO,IAAI,SAAS,OAAO,SAAS,OAAO,SAAS,KAAK;gBACvD,SAAS,IAAI,CAAC,WAAW,6BAA6B;YACxD,OAAO;gBACL,SAAS,IAAI,CAAC,QAAQ,oCAAoC;YAC5D;QACF;QAEA,OAAO,SAAS,MAAM,GAAG,IAAI,WAAW;YAAC;SAAM;IACjD;IAEA,oCAAoC;IAC5B,oBAAoB,OAAe,EAAU;QACnD,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,yCAAyC;IACzC,MAAM,iBAAgC;QACpC,IAAI;YACF,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBAAE,OAAO;YAAK;YACvE,IAAI,CAAC,aAAa,GAAG,IAAI,cAAc;YACvC,IAAI,CAAC,WAAW,GAAG,EAAE;YAErB,IAAI,CAAC,aAAa,CAAC,eAAe,GAAG,CAAC;gBACpC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,IAAI;YAClC;YAEA,IAAI,CAAC,aAAa,CAAC,KAAK;QAC1B,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,OAAO;QACvD;IACF;IAEA,MAAM,gBAA+B;QACnC,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;gBACvB,OAAO,IAAI,MAAM;gBACjB;YACF;YAEA,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG;gBAC1B,MAAM,YAAY,IAAI,KAAK,IAAI,CAAC,WAAW,EAAE;oBAAE,MAAM;gBAAY;gBACjE,QAAQ;YACV;YAEA,IAAI,CAAC,aAAa,CAAC,IAAI;YAEvB,wCAAwC;YACxC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,GAAG,OAAO,CAAC,CAAA,QAAS,MAAM,IAAI;QACnE;IACF;IAEA,yCAAyC;IACzC,cAAuB;QACrB,OAAO,gBAAkB,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,SAAS;IAC/E;AACF;AAGO,MAAM,gBAAgB,sCAAgC,0BAAsB", "debugId": null}}, {"offset": {"line": 1044, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trainer%20test/ai-trainer-assessment/src/services/userProfileService.ts"], "sourcesContent": ["import { UserAssessmentProfile, ConversationMessage, AssessmentSession, AIInsight, PhysicalAssessment } from '@/types';\n\nexport interface PersistentUserProfile extends UserAssessmentProfile {\n  id: string;\n  email?: string;\n  createdAt: Date;\n  lastActiveAt: Date;\n  totalSessions: number;\n  assessmentHistory: AssessmentSession[];\n  conversationHistory: ConversationMessage[];\n  progressTracking: ProgressEntry[];\n  preferences: UserPreferences;\n  subscription?: SubscriptionInfo;\n}\n\nexport interface ProgressEntry {\n  id: string;\n  date: Date;\n  weight?: number;\n  bodyFat?: number;\n  measurements?: Record<string, number>;\n  photos?: string[];\n  notes?: string;\n  workoutCompleted?: boolean;\n  mood?: 'excellent' | 'good' | 'okay' | 'struggling';\n}\n\nexport interface UserPreferences {\n  preferredWorkoutTime: string;\n  trainerPersonality: string; // personality ID\n  reminderSettings: {\n    workouts: boolean;\n    nutrition: boolean;\n    checkIns: boolean;\n  };\n  voiceSettings: {\n    speed: number;\n    volume: number;\n    preferredVoice: string;\n  };\n  privacySettings: {\n    shareProgress: boolean;\n    allowAnalytics: boolean;\n  };\n}\n\nexport interface SubscriptionInfo {\n  tier: 'basic' | 'premium' | 'elite';\n  startDate: Date;\n  endDate: Date;\n  status: 'active' | 'cancelled' | 'expired';\n  autoRenew: boolean;\n}\n\nexport class UserProfileService {\n  private static readonly STORAGE_KEY = 'ai_trainer_profiles';\n  private static readonly CURRENT_USER_KEY = 'ai_trainer_current_user';\n\n  // Create or get user profile\n  static async createOrGetProfile(name: string, email?: string): Promise<PersistentUserProfile> {\n    const profiles = this.getAllProfiles();\n    \n    // Try to find existing profile by name or email\n    let existingProfile = profiles.find(p => \n      p.name.toLowerCase() === name.toLowerCase() || \n      (email && p.email?.toLowerCase() === email.toLowerCase())\n    );\n\n    if (existingProfile) {\n      // Update last active time\n      existingProfile.lastActiveAt = new Date();\n      this.saveProfile(existingProfile);\n      this.setCurrentUser(existingProfile.id);\n      return existingProfile;\n    }\n\n    // Create new profile\n    const newProfile: PersistentUserProfile = {\n      id: this.generateUserId(),\n      name,\n      email,\n      createdAt: new Date(),\n      lastActiveAt: new Date(),\n      totalSessions: 0,\n      goals: [],\n      painPoints: [],\n      motivations: [],\n      emotionalTriggers: [],\n      assessmentHistory: [],\n      conversationHistory: [],\n      progressTracking: [],\n      preferences: {\n        preferredWorkoutTime: '18:00',\n        trainerPersonality: 'supportive_coach', // Default to supportive\n        reminderSettings: {\n          workouts: true,\n          nutrition: true,\n          checkIns: true\n        },\n        voiceSettings: {\n          speed: 1.0,\n          volume: 0.8,\n          preferredVoice: 'nova'\n        },\n        privacySettings: {\n          shareProgress: false,\n          allowAnalytics: true\n        }\n      }\n    };\n\n    this.saveProfile(newProfile);\n    this.setCurrentUser(newProfile.id);\n    return newProfile;\n  }\n\n  // Get current user profile\n  static getCurrentProfile(): PersistentUserProfile | null {\n    const currentUserId = localStorage.getItem(this.CURRENT_USER_KEY);\n    if (!currentUserId) return null;\n\n    const profiles = this.getAllProfiles();\n    return profiles.find(p => p.id === currentUserId) || null;\n  }\n\n  // Update user profile\n  static updateProfile(updates: Partial<PersistentUserProfile>): void {\n    const currentProfile = this.getCurrentProfile();\n    if (!currentProfile) return;\n\n    const updatedProfile = { ...currentProfile, ...updates, lastActiveAt: new Date() };\n    this.saveProfile(updatedProfile);\n  }\n\n  // Add conversation message\n  static addConversationMessage(message: ConversationMessage): void {\n    const profile = this.getCurrentProfile();\n    if (!profile) return;\n\n    profile.conversationHistory.push(message);\n    \n    // Keep only last 100 messages to prevent storage bloat\n    if (profile.conversationHistory.length > 100) {\n      profile.conversationHistory = profile.conversationHistory.slice(-100);\n    }\n\n    this.saveProfile(profile);\n  }\n\n  // Add assessment session\n  static addAssessmentSession(session: AssessmentSession): void {\n    const profile = this.getCurrentProfile();\n    if (!profile) return;\n\n    profile.assessmentHistory.push(session);\n    profile.totalSessions += 1;\n    \n    // Update profile with session insights\n    if (session.userProfile) {\n      profile.goals = [...new Set([...profile.goals, ...session.userProfile.goals])];\n      profile.painPoints = [...new Set([...profile.painPoints, ...session.userProfile.painPoints])];\n      profile.motivations = [...new Set([...profile.motivations, ...session.userProfile.motivations])];\n      profile.emotionalTriggers = [...new Set([...profile.emotionalTriggers, ...session.userProfile.emotionalTriggers])];\n      \n      if (session.userProfile.fitnessLevel) {\n        profile.fitnessLevel = session.userProfile.fitnessLevel;\n      }\n      if (session.userProfile.preferredStyle) {\n        profile.preferredStyle = session.userProfile.preferredStyle;\n      }\n    }\n\n    this.saveProfile(profile);\n  }\n\n  // Add progress entry\n  static addProgressEntry(entry: Omit<ProgressEntry, 'id'>): void {\n    const profile = this.getCurrentProfile();\n    if (!profile) return;\n\n    const progressEntry: ProgressEntry = {\n      ...entry,\n      id: this.generateProgressId()\n    };\n\n    profile.progressTracking.push(progressEntry);\n    this.saveProfile(profile);\n  }\n\n  // Get conversation context for AI\n  static getConversationContext(limit: number = 10): ConversationMessage[] {\n    const profile = this.getCurrentProfile();\n    if (!profile) return [];\n\n    return profile.conversationHistory.slice(-limit);\n  }\n\n  // Get user summary for AI context\n  static getUserSummaryForAI(): string {\n    const profile = this.getCurrentProfile();\n    if (!profile) return '';\n\n    const recentProgress = profile.progressTracking.slice(-3);\n    const lastSession = profile.assessmentHistory[profile.assessmentHistory.length - 1];\n\n    let summary = `User Profile Summary for ${profile.name}:\\n`;\n    summary += `- Member since: ${profile.createdAt.toDateString()}\\n`;\n    summary += `- Total sessions: ${profile.totalSessions}\\n`;\n    summary += `- Fitness level: ${profile.fitnessLevel || 'Not assessed'}\\n`;\n    summary += `- Primary goals: ${profile.goals.join(', ') || 'Not specified'}\\n`;\n    summary += `- Main pain points: ${profile.painPoints.join(', ') || 'None identified'}\\n`;\n    summary += `- Key motivations: ${profile.motivations.join(', ') || 'Not specified'}\\n`;\n    summary += `- Preferred coaching style: ${profile.preferredStyle || 'Not specified'}\\n`;\n\n    if (recentProgress.length > 0) {\n      summary += `- Recent progress: ${recentProgress.length} entries in tracking\\n`;\n      const latest = recentProgress[recentProgress.length - 1];\n      if (latest.weight) summary += `- Current weight: ${latest.weight} lbs\\n`;\n      if (latest.mood) summary += `- Recent mood: ${latest.mood}\\n`;\n    }\n\n    if (lastSession) {\n      summary += `- Last assessment: ${lastSession.startedAt.toDateString()}\\n`;\n      summary += `- Last phase completed: ${lastSession.phase}\\n`;\n    }\n\n    return summary;\n  }\n\n  // Private helper methods\n  private static getAllProfiles(): PersistentUserProfile[] {\n    try {\n      const stored = localStorage.getItem(this.STORAGE_KEY);\n      return stored ? JSON.parse(stored, this.dateReviver) : [];\n    } catch (error) {\n      console.error('Error loading profiles:', error);\n      return [];\n    }\n  }\n\n  private static saveProfile(profile: PersistentUserProfile): void {\n    try {\n      const profiles = this.getAllProfiles();\n      const index = profiles.findIndex(p => p.id === profile.id);\n      \n      if (index >= 0) {\n        profiles[index] = profile;\n      } else {\n        profiles.push(profile);\n      }\n\n      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(profiles));\n    } catch (error) {\n      console.error('Error saving profile:', error);\n    }\n  }\n\n  private static setCurrentUser(userId: string): void {\n    localStorage.setItem(this.CURRENT_USER_KEY, userId);\n  }\n\n  private static generateUserId(): string {\n    return `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  private static generateProgressId(): string {\n    return `progress_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  // Date reviver for JSON.parse to handle Date objects\n  private static dateReviver(key: string, value: any): any {\n    if (typeof value === 'string' && /^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}/.test(value)) {\n      return new Date(value);\n    }\n    return value;\n  }\n\n  // Export/Import functionality for data portability\n  static exportUserData(): string {\n    const profile = this.getCurrentProfile();\n    if (!profile) throw new Error('No current user profile');\n\n    return JSON.stringify(profile, null, 2);\n  }\n\n  static importUserData(jsonData: string): void {\n    try {\n      const profile = JSON.parse(jsonData, this.dateReviver) as PersistentUserProfile;\n      this.saveProfile(profile);\n      this.setCurrentUser(profile.id);\n    } catch (error) {\n      throw new Error('Invalid user data format');\n    }\n  }\n\n  // Clear all data (for privacy/reset)\n  static clearAllData(): void {\n    localStorage.removeItem(this.STORAGE_KEY);\n    localStorage.removeItem(this.CURRENT_USER_KEY);\n  }\n\n  // Get user statistics\n  static getUserStats(): {\n    totalConversations: number;\n    totalSessions: number;\n    memberSince: Date;\n    progressEntries: number;\n    currentStreak: number;\n  } {\n    const profile = this.getCurrentProfile();\n    if (!profile) {\n      return {\n        totalConversations: 0,\n        totalSessions: 0,\n        memberSince: new Date(),\n        progressEntries: 0,\n        currentStreak: 0\n      };\n    }\n\n    // Calculate current streak (days with activity)\n    const now = new Date();\n    let currentStreak = 0;\n    const sortedProgress = profile.progressTracking\n      .sort((a, b) => b.date.getTime() - a.date.getTime());\n\n    for (const entry of sortedProgress) {\n      const daysDiff = Math.floor((now.getTime() - entry.date.getTime()) / (1000 * 60 * 60 * 24));\n      if (daysDiff <= currentStreak + 1) {\n        currentStreak++;\n      } else {\n        break;\n      }\n    }\n\n    return {\n      totalConversations: profile.conversationHistory.length,\n      totalSessions: profile.totalSessions,\n      memberSince: profile.createdAt,\n      progressEntries: profile.progressTracking.length,\n      currentStreak\n    };\n  }\n}\n"], "names": [], "mappings": ";;;;AAsDO,MAAM;IACX,OAAwB,cAAc,sBAAsB;IAC5D,OAAwB,mBAAmB,0BAA0B;IAErE,6BAA6B;IAC7B,aAAa,mBAAmB,IAAY,EAAE,KAAc,EAAkC;QAC5F,MAAM,WAAW,IAAI,CAAC,cAAc;QAEpC,gDAAgD;QAChD,IAAI,kBAAkB,SAAS,IAAI,CAAC,CAAA,IAClC,EAAE,IAAI,CAAC,WAAW,OAAO,KAAK,WAAW,MACxC,SAAS,EAAE,KAAK,EAAE,kBAAkB,MAAM,WAAW;QAGxD,IAAI,iBAAiB;YACnB,0BAA0B;YAC1B,gBAAgB,YAAY,GAAG,IAAI;YACnC,IAAI,CAAC,WAAW,CAAC;YACjB,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE;YACtC,OAAO;QACT;QAEA,qBAAqB;QACrB,MAAM,aAAoC;YACxC,IAAI,IAAI,CAAC,cAAc;YACvB;YACA;YACA,WAAW,IAAI;YACf,cAAc,IAAI;YAClB,eAAe;YACf,OAAO,EAAE;YACT,YAAY,EAAE;YACd,aAAa,EAAE;YACf,mBAAmB,EAAE;YACrB,mBAAmB,EAAE;YACrB,qBAAqB,EAAE;YACvB,kBAAkB,EAAE;YACpB,aAAa;gBACX,sBAAsB;gBACtB,oBAAoB;gBACpB,kBAAkB;oBAChB,UAAU;oBACV,WAAW;oBACX,UAAU;gBACZ;gBACA,eAAe;oBACb,OAAO;oBACP,QAAQ;oBACR,gBAAgB;gBAClB;gBACA,iBAAiB;oBACf,eAAe;oBACf,gBAAgB;gBAClB;YACF;QACF;QAEA,IAAI,CAAC,WAAW,CAAC;QACjB,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE;QACjC,OAAO;IACT;IAEA,2BAA2B;IAC3B,OAAO,oBAAkD;QACvD,MAAM,gBAAgB,aAAa,OAAO,CAAC,IAAI,CAAC,gBAAgB;QAChE,IAAI,CAAC,eAAe,OAAO;QAE3B,MAAM,WAAW,IAAI,CAAC,cAAc;QACpC,OAAO,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,kBAAkB;IACvD;IAEA,sBAAsB;IACtB,OAAO,cAAc,OAAuC,EAAQ;QAClE,MAAM,iBAAiB,IAAI,CAAC,iBAAiB;QAC7C,IAAI,CAAC,gBAAgB;QAErB,MAAM,iBAAiB;YAAE,GAAG,cAAc;YAAE,GAAG,OAAO;YAAE,cAAc,IAAI;QAAO;QACjF,IAAI,CAAC,WAAW,CAAC;IACnB;IAEA,2BAA2B;IAC3B,OAAO,uBAAuB,OAA4B,EAAQ;QAChE,MAAM,UAAU,IAAI,CAAC,iBAAiB;QACtC,IAAI,CAAC,SAAS;QAEd,QAAQ,mBAAmB,CAAC,IAAI,CAAC;QAEjC,uDAAuD;QACvD,IAAI,QAAQ,mBAAmB,CAAC,MAAM,GAAG,KAAK;YAC5C,QAAQ,mBAAmB,GAAG,QAAQ,mBAAmB,CAAC,KAAK,CAAC,CAAC;QACnE;QAEA,IAAI,CAAC,WAAW,CAAC;IACnB;IAEA,yBAAyB;IACzB,OAAO,qBAAqB,OAA0B,EAAQ;QAC5D,MAAM,UAAU,IAAI,CAAC,iBAAiB;QACtC,IAAI,CAAC,SAAS;QAEd,QAAQ,iBAAiB,CAAC,IAAI,CAAC;QAC/B,QAAQ,aAAa,IAAI;QAEzB,uCAAuC;QACvC,IAAI,QAAQ,WAAW,EAAE;YACvB,QAAQ,KAAK,GAAG;mBAAI,IAAI,IAAI;uBAAI,QAAQ,KAAK;uBAAK,QAAQ,WAAW,CAAC,KAAK;iBAAC;aAAE;YAC9E,QAAQ,UAAU,GAAG;mBAAI,IAAI,IAAI;uBAAI,QAAQ,UAAU;uBAAK,QAAQ,WAAW,CAAC,UAAU;iBAAC;aAAE;YAC7F,QAAQ,WAAW,GAAG;mBAAI,IAAI,IAAI;uBAAI,QAAQ,WAAW;uBAAK,QAAQ,WAAW,CAAC,WAAW;iBAAC;aAAE;YAChG,QAAQ,iBAAiB,GAAG;mBAAI,IAAI,IAAI;uBAAI,QAAQ,iBAAiB;uBAAK,QAAQ,WAAW,CAAC,iBAAiB;iBAAC;aAAE;YAElH,IAAI,QAAQ,WAAW,CAAC,YAAY,EAAE;gBACpC,QAAQ,YAAY,GAAG,QAAQ,WAAW,CAAC,YAAY;YACzD;YACA,IAAI,QAAQ,WAAW,CAAC,cAAc,EAAE;gBACtC,QAAQ,cAAc,GAAG,QAAQ,WAAW,CAAC,cAAc;YAC7D;QACF;QAEA,IAAI,CAAC,WAAW,CAAC;IACnB;IAEA,qBAAqB;IACrB,OAAO,iBAAiB,KAAgC,EAAQ;QAC9D,MAAM,UAAU,IAAI,CAAC,iBAAiB;QACtC,IAAI,CAAC,SAAS;QAEd,MAAM,gBAA+B;YACnC,GAAG,KAAK;YACR,IAAI,IAAI,CAAC,kBAAkB;QAC7B;QAEA,QAAQ,gBAAgB,CAAC,IAAI,CAAC;QAC9B,IAAI,CAAC,WAAW,CAAC;IACnB;IAEA,kCAAkC;IAClC,OAAO,uBAAuB,QAAgB,EAAE,EAAyB;QACvE,MAAM,UAAU,IAAI,CAAC,iBAAiB;QACtC,IAAI,CAAC,SAAS,OAAO,EAAE;QAEvB,OAAO,QAAQ,mBAAmB,CAAC,KAAK,CAAC,CAAC;IAC5C;IAEA,kCAAkC;IAClC,OAAO,sBAA8B;QACnC,MAAM,UAAU,IAAI,CAAC,iBAAiB;QACtC,IAAI,CAAC,SAAS,OAAO;QAErB,MAAM,iBAAiB,QAAQ,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACvD,MAAM,cAAc,QAAQ,iBAAiB,CAAC,QAAQ,iBAAiB,CAAC,MAAM,GAAG,EAAE;QAEnF,IAAI,UAAU,CAAC,yBAAyB,EAAE,QAAQ,IAAI,CAAC,GAAG,CAAC;QAC3D,WAAW,CAAC,gBAAgB,EAAE,QAAQ,SAAS,CAAC,YAAY,GAAG,EAAE,CAAC;QAClE,WAAW,CAAC,kBAAkB,EAAE,QAAQ,aAAa,CAAC,EAAE,CAAC;QACzD,WAAW,CAAC,iBAAiB,EAAE,QAAQ,YAAY,IAAI,eAAe,EAAE,CAAC;QACzE,WAAW,CAAC,iBAAiB,EAAE,QAAQ,KAAK,CAAC,IAAI,CAAC,SAAS,gBAAgB,EAAE,CAAC;QAC9E,WAAW,CAAC,oBAAoB,EAAE,QAAQ,UAAU,CAAC,IAAI,CAAC,SAAS,kBAAkB,EAAE,CAAC;QACxF,WAAW,CAAC,mBAAmB,EAAE,QAAQ,WAAW,CAAC,IAAI,CAAC,SAAS,gBAAgB,EAAE,CAAC;QACtF,WAAW,CAAC,4BAA4B,EAAE,QAAQ,cAAc,IAAI,gBAAgB,EAAE,CAAC;QAEvF,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,WAAW,CAAC,mBAAmB,EAAE,eAAe,MAAM,CAAC,sBAAsB,CAAC;YAC9E,MAAM,SAAS,cAAc,CAAC,eAAe,MAAM,GAAG,EAAE;YACxD,IAAI,OAAO,MAAM,EAAE,WAAW,CAAC,kBAAkB,EAAE,OAAO,MAAM,CAAC,MAAM,CAAC;YACxE,IAAI,OAAO,IAAI,EAAE,WAAW,CAAC,eAAe,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC;QAC/D;QAEA,IAAI,aAAa;YACf,WAAW,CAAC,mBAAmB,EAAE,YAAY,SAAS,CAAC,YAAY,GAAG,EAAE,CAAC;YACzE,WAAW,CAAC,wBAAwB,EAAE,YAAY,KAAK,CAAC,EAAE,CAAC;QAC7D;QAEA,OAAO;IACT;IAEA,yBAAyB;IACzB,OAAe,iBAA0C;QACvD,IAAI;YACF,MAAM,SAAS,aAAa,OAAO,CAAC,IAAI,CAAC,WAAW;YACpD,OAAO,SAAS,KAAK,KAAK,CAAC,QAAQ,IAAI,CAAC,WAAW,IAAI,EAAE;QAC3D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,EAAE;QACX;IACF;IAEA,OAAe,YAAY,OAA8B,EAAQ;QAC/D,IAAI;YACF,MAAM,WAAW,IAAI,CAAC,cAAc;YACpC,MAAM,QAAQ,SAAS,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,EAAE;YAEzD,IAAI,SAAS,GAAG;gBACd,QAAQ,CAAC,MAAM,GAAG;YACpB,OAAO;gBACL,SAAS,IAAI,CAAC;YAChB;YAEA,aAAa,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,SAAS,CAAC;QACxD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,OAAe,eAAe,MAAc,EAAQ;QAClD,aAAa,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE;IAC9C;IAEA,OAAe,iBAAyB;QACtC,OAAO,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IACxE;IAEA,OAAe,qBAA6B;QAC1C,OAAO,CAAC,SAAS,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAC5E;IAEA,qDAAqD;IACrD,OAAe,YAAY,GAAW,EAAE,KAAU,EAAO;QACvD,IAAI,OAAO,UAAU,YAAY,uCAAuC,IAAI,CAAC,QAAQ;YACnF,OAAO,IAAI,KAAK;QAClB;QACA,OAAO;IACT;IAEA,mDAAmD;IACnD,OAAO,iBAAyB;QAC9B,MAAM,UAAU,IAAI,CAAC,iBAAiB;QACtC,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM;QAE9B,OAAO,KAAK,SAAS,CAAC,SAAS,MAAM;IACvC;IAEA,OAAO,eAAe,QAAgB,EAAQ;QAC5C,IAAI;YACF,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU,IAAI,CAAC,WAAW;YACrD,IAAI,CAAC,WAAW,CAAC;YACjB,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE;QAChC,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,qCAAqC;IACrC,OAAO,eAAqB;QAC1B,aAAa,UAAU,CAAC,IAAI,CAAC,WAAW;QACxC,aAAa,UAAU,CAAC,IAAI,CAAC,gBAAgB;IAC/C;IAEA,sBAAsB;IACtB,OAAO,eAML;QACA,MAAM,UAAU,IAAI,CAAC,iBAAiB;QACtC,IAAI,CAAC,SAAS;YACZ,OAAO;gBACL,oBAAoB;gBACpB,eAAe;gBACf,aAAa,IAAI;gBACjB,iBAAiB;gBACjB,eAAe;YACjB;QACF;QAEA,gDAAgD;QAChD,MAAM,MAAM,IAAI;QAChB,IAAI,gBAAgB;QACpB,MAAM,iBAAiB,QAAQ,gBAAgB,CAC5C,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE,IAAI,CAAC,OAAO;QAEnD,KAAK,MAAM,SAAS,eAAgB;YAClC,MAAM,WAAW,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,MAAM,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;YACzF,IAAI,YAAY,gBAAgB,GAAG;gBACjC;YACF,OAAO;gBACL;YACF;QACF;QAEA,OAAO;YACL,oBAAoB,QAAQ,mBAAmB,CAAC,MAAM;YACtD,eAAe,QAAQ,aAAa;YACpC,aAAa,QAAQ,SAAS;YAC9B,iBAAiB,QAAQ,gBAAgB,CAAC,MAAM;YAChD;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 1311, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trainer%20test/ai-trainer-assessment/src/components/AssessmentInterface.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Mi<PERSON>, MicOff, Volume2, VolumeX } from 'lucide-react';\nimport Hologram from './Avatar';\nimport { speechService } from '@/services/speechService';\nimport { UserProfileService, PersistentUserProfile } from '@/services/userProfileService';\nimport {\n  AssessmentPhase,\n  DiscoverySubPhase,\n  ConversationMessage,\n  SpeechState,\n  AvatarState,\n  AIInsight,\n  UserAssessmentProfile\n} from '@/types';\n\ninterface AssessmentInterfaceProps {\n  userName?: string;\n  userEmail?: string;\n  personalityId?: string;\n  onPhaseChange?: (phase: AssessmentPhase) => void;\n  onInsights?: (insights: AIInsight[]) => void;\n  isGeneralChat?: boolean; // For non-assessment conversations\n}\n\nexport const AssessmentInterface: React.FC<AssessmentInterfaceProps> = ({\n  userName = 'there',\n  userEmail,\n  personalityId,\n  onPhaseChange,\n  onInsights,\n  isGeneralChat = false\n}) => {\n  // State management\n  const [currentPhase, setCurrentPhase] = useState<AssessmentPhase>('warm_welcome');\n  const [currentSubPhase, setCurrentSubPhase] = useState<DiscoverySubPhase>('surface_level');\n  const [userProfile, setUserProfile] = useState<UserAssessmentProfile | null>(null);\n  const [persistentProfile, setPersistentProfile] = useState<PersistentUserProfile | null>(null);\n  const [conversation, setConversation] = useState<ConversationMessage[]>([]);\n  const [speechState, setSpeechState] = useState<SpeechState>({\n    isListening: false,\n    isProcessing: false,\n    isSpeaking: false,\n    transcript: '',\n    confidence: 0\n  });\n  const [avatarState, setAvatarState] = useState<AvatarState>({\n    isAnimating: false,\n    currentAnimation: 'idle'\n  });\n  const [isAudioEnabled, setIsAudioEnabled] = useState(true);\n  const [currentTranscript, setCurrentTranscript] = useState('');\n  const [sessionId] = useState(() => `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);\n  const [isReturningUser, setIsReturningUser] = useState(false);\n\n  // Initialize user profile and welcome message\n  useEffect(() => {\n    const initializeUser = async () => {\n      try {\n        // Load or create user profile\n        const profile = await UserProfileService.createOrGetProfile(userName, userEmail);\n        setPersistentProfile(profile);\n        setIsReturningUser(profile.totalSessions > 0);\n\n        // Load previous conversation context if returning user\n        if (profile.totalSessions > 0 && !isGeneralChat) {\n          const recentMessages = UserProfileService.getConversationContext(3);\n          if (recentMessages.length > 0) {\n            setConversation(recentMessages);\n          }\n        }\n\n        // Create appropriate welcome message\n        let welcomeContent: string;\n\n        if (isGeneralChat) {\n          welcomeContent = `Hey ${userName}! Great to see you again! I'm here and ready to help with any fitness questions you have. What's on your mind today?`;\n        } else if (profile.totalSessions > 0) {\n          const lastGoals = profile.goals.slice(-2).join(' and ') || 'your fitness goals';\n          welcomeContent = `Hey ${userName}! So great to see you back! I've been thinking about your ${lastGoals} since we last talked. How have you been feeling? Ready to continue your transformation journey?`;\n        } else {\n          welcomeContent = `Hey ${userName}! I'm absolutely thrilled to meet you! I'm Alex, and I'm here to be your personal trainer, your biggest cheerleader, and maybe occasionally your gentle drill sergeant when you need that extra push. This entire session is about YOU - your goals, your dreams, and creating a plan that's going to transform how you look and feel. I can already tell you're serious about making a change, and that's exactly the mindset that leads to incredible results! So tell me, ${userName}, what brought you here today? What's got you excited about starting this fitness journey?`;\n        }\n\n        const welcomeMessage: ConversationMessage = {\n          id: '1',\n          role: 'trainer',\n          content: welcomeContent,\n          timestamp: new Date()\n        };\n\n        setConversation(prev => prev.length === 0 ? [welcomeMessage] : prev);\n\n        // Speak the welcome message\n        if (isAudioEnabled) {\n          speakMessage(welcomeContent);\n        }\n\n        // Save welcome message to profile\n        UserProfileService.addConversationMessage(welcomeMessage);\n\n      } catch (error) {\n        console.error('Error initializing user:', error);\n      }\n    };\n\n    initializeUser();\n  }, [userName, userEmail, isAudioEnabled, isGeneralChat]);\n\n  // Handle speech synthesis\n  const speakMessage = useCallback(async (text: string) => {\n    if (!isAudioEnabled || !speechService) return;\n\n    setAvatarState(prev => ({ ...prev, currentAnimation: 'talking' }));\n    setSpeechState(prev => ({ ...prev, isSpeaking: true }));\n\n    try {\n      await speechService.speak(\n        text,\n        () => {\n          // On start\n          setAvatarState(prev => ({ ...prev, currentAnimation: 'talking' }));\n        },\n        () => {\n          // On end\n          setAvatarState(prev => ({ ...prev, currentAnimation: 'idle' }));\n          setSpeechState(prev => ({ ...prev, isSpeaking: false }));\n        },\n        (mouthSyncData) => {\n          // On mouth sync\n          setAvatarState(prev => ({ \n            ...prev, \n            mouthSyncData: { ...mouthSyncData, currentTime: Date.now() }\n          }));\n        }\n      );\n    } catch (error) {\n      console.error('Speech synthesis error:', error);\n      setAvatarState(prev => ({ ...prev, currentAnimation: 'idle' }));\n      setSpeechState(prev => ({ ...prev, isSpeaking: false }));\n    }\n  }, [isAudioEnabled]);\n\n  // Handle voice input\n  const startListening = useCallback(async () => {\n    if (speechState.isListening || speechState.isSpeaking || !speechService) return;\n\n    setAvatarState(prev => ({ ...prev, currentAnimation: 'listening' }));\n    setSpeechState(prev => ({ ...prev, isListening: true, transcript: '' }));\n    setCurrentTranscript('');\n\n    try {\n      await speechService.startListening(\n        (transcript, isFinal) => {\n          setCurrentTranscript(transcript);\n          setSpeechState(prev => ({ \n            ...prev, \n            transcript,\n            confidence: isFinal ? 1 : 0.5\n          }));\n\n          if (isFinal && transcript.trim()) {\n            handleUserMessage(transcript.trim());\n          }\n        },\n        (error) => {\n          console.error('Speech recognition error:', error);\n          stopListening();\n        }\n      );\n    } catch (error) {\n      console.error('Failed to start listening:', error);\n      stopListening();\n    }\n  }, [speechState.isListening, speechState.isSpeaking]);\n\n  const stopListening = useCallback(() => {\n    if (speechService) {\n      speechService.stopListening();\n    }\n    setAvatarState(prev => ({ ...prev, currentAnimation: 'idle' }));\n    setSpeechState(prev => ({\n      ...prev,\n      isListening: false,\n      isProcessing: false\n    }));\n  }, []);\n\n  // Handle user message\n  const handleUserMessage = useCallback(async (message: string) => {\n    if (!message.trim()) return;\n\n    stopListening();\n    setSpeechState(prev => ({ ...prev, isProcessing: true }));\n    setAvatarState(prev => ({ ...prev, currentAnimation: 'thinking' }));\n\n    // Add user message to conversation\n    const userMessage: ConversationMessage = {\n      id: Date.now().toString(),\n      role: 'user',\n      content: message,\n      timestamp: new Date()\n    };\n\n    setConversation(prev => [...prev, userMessage]);\n\n    // Save user message to persistent storage\n    UserProfileService.addConversationMessage(userMessage);\n\n    try {\n      // Determine if this is a general question or assessment\n      const apiEndpoint = isGeneralChat ? '/api/general-fitness-chat' : '/api/trainer-chat';\n\n      // Send to AI trainer with comprehensive context\n      const response = await fetch(apiEndpoint, {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          message,\n          phase: currentPhase,\n          subPhase: currentSubPhase,\n          conversationHistory: conversation,\n          userProfile: {\n            name: userName,\n            email: userEmail,\n            sessionId,\n            subPhase: currentSubPhase,\n            isReturningUser,\n            personalityId: personalityId || persistentProfile?.preferences?.trainerPersonality,\n            ...userProfile,\n            ...persistentProfile\n          }\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to get trainer response');\n      }\n\n      const data = await response.json();\n      \n      // Add trainer response to conversation\n      const trainerMessage: ConversationMessage = {\n        id: (Date.now() + 1).toString(),\n        role: 'trainer',\n        content: data.response,\n        timestamp: new Date()\n      };\n\n      setConversation(prev => [...prev, trainerMessage]);\n\n      // Save trainer message to persistent storage\n      UserProfileService.addConversationMessage(trainerMessage);\n\n      // Handle insights\n      if (data.insights && onInsights) {\n        onInsights(data.insights);\n      }\n\n      // Update user profile if provided\n      if (data.userProfile) {\n        setUserProfile(data.userProfile);\n      }\n\n      // Handle phase transitions (only for assessment mode)\n      if (!isGeneralChat) {\n        if (data.shouldTransition && data.nextPhase) {\n          setCurrentPhase(data.nextPhase);\n          onPhaseChange?.(data.nextPhase);\n        }\n\n        // Update sub-phase if provided\n        if (data.currentSubPhase) {\n          setCurrentSubPhase(data.currentSubPhase);\n        }\n      }\n\n      // Speak the response\n      await speakMessage(data.response);\n\n    } catch (error) {\n      console.error('Error getting trainer response:', error);\n      \n      const errorMessage: ConversationMessage = {\n        id: (Date.now() + 1).toString(),\n        role: 'trainer',\n        content: \"I'm sorry, I had a technical hiccup there. Could you repeat that?\",\n        timestamp: new Date()\n      };\n\n      setConversation(prev => [...prev, errorMessage]);\n      await speakMessage(errorMessage.content);\n    } finally {\n      setSpeechState(prev => ({ ...prev, isProcessing: false }));\n      setAvatarState(prev => ({ ...prev, currentAnimation: 'idle' }));\n    }\n  }, [conversation, currentPhase, userName, onInsights, speakMessage, stopListening]);\n\n  // Get phase display name\n  const getPhaseDisplayName = (phase: AssessmentPhase, subPhase?: DiscoverySubPhase): string => {\n    switch (phase) {\n      case 'warm_welcome':\n        return 'WARM WELCOME & RAPPORT BUILDING';\n      case 'deep_discovery':\n        switch (subPhase) {\n          case 'surface_level': return 'DISCOVERY - SURFACE LEVEL';\n          case 'pain_points': return 'DISCOVERY - PAIN POINTS';\n          case 'emotional_drivers': return 'DISCOVERY - EMOTIONAL DRIVERS';\n          case 'support_style': return 'DISCOVERY - SUPPORT STYLE';\n          default: return 'DEEP DISCOVERY & EMOTIONAL CONNECTION';\n        }\n      case 'physical_assessment':\n        return 'LIVE PHYSICAL ASSESSMENT';\n      case 'vision_reveal':\n        return 'VISION REVEAL & FUTURE PROJECTION';\n      case 'service_recommendation':\n        return 'NATURAL SERVICE RECOMMENDATION';\n      case 'completed':\n        return 'ASSESSMENT COMPLETED';\n      default:\n        return phase.toUpperCase().replace('_', ' ');\n    }\n  };\n\n  // Toggle audio\n  const toggleAudio = () => {\n    setIsAudioEnabled(!isAudioEnabled);\n    if (speechState.isSpeaking && speechService) {\n      speechService.stopSpeaking();\n      setAvatarState(prev => ({ ...prev, currentAnimation: 'idle' }));\n      setSpeechState(prev => ({ ...prev, isSpeaking: false }));\n    }\n  };\n\n  return (\n    <div className=\"flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-4\">\n      {/* Hologram */}\n      <div className=\"mb-8\">\n        <Hologram state={avatarState} size=\"large\" />\n      </div>\n\n      {/* Conversation Display */}\n      <div className=\"w-full max-w-2xl mb-6\">\n        <div className=\"bg-black bg-opacity-50 rounded-lg shadow-lg border border-cyan-400 p-6 max-h-60 overflow-y-auto backdrop-blur-sm\">\n          <AnimatePresence>\n            {conversation.slice(-3).map((message) => (\n              <motion.div\n                key={message.id}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                className={`mb-4 ${\n                  message.role === 'trainer' ? 'text-left' : 'text-right'\n                }`}\n              >\n                <div\n                  className={`inline-block p-3 rounded-lg max-w-xs border ${\n                    message.role === 'trainer'\n                      ? 'bg-cyan-900 bg-opacity-50 text-cyan-100 border-cyan-400'\n                      : 'bg-purple-900 bg-opacity-50 text-purple-100 border-purple-400'\n                  }`}\n                >\n                  <p className=\"text-sm font-medium mb-1 font-mono\">\n                    {message.role === 'trainer' ? '> ALEX' : '> YOU'}\n                  </p>\n                  <p className=\"text-sm\">{message.content}</p>\n                </div>\n              </motion.div>\n            ))}\n          </AnimatePresence>\n        </div>\n      </div>\n\n      {/* Current Transcript */}\n      {currentTranscript && (\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          className=\"mb-4 p-3 bg-green-900 bg-opacity-50 rounded-lg border border-green-400 backdrop-blur-sm\"\n        >\n          <p className=\"text-sm text-green-300 font-mono\">\n            {`> RECEIVING: \"${currentTranscript}\"`}\n          </p>\n        </motion.div>\n      )}\n\n      {/* Controls */}\n      <div className=\"flex space-x-4\">\n        {/* Microphone Button */}\n        <motion.button\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n          onClick={speechState.isListening ? stopListening : startListening}\n          disabled={speechState.isSpeaking || speechState.isProcessing}\n          className={`p-4 rounded-full shadow-lg transition-colors border-2 ${\n            speechState.isListening\n              ? 'bg-red-500 text-white border-red-400 shadow-red-400/50'\n              : speechState.isSpeaking || speechState.isProcessing\n              ? 'bg-gray-600 text-gray-400 cursor-not-allowed border-gray-500'\n              : 'bg-cyan-500 text-white hover:bg-cyan-600 border-cyan-400 shadow-cyan-400/50'\n          }`}\n          style={{\n            boxShadow: speechState.isListening\n              ? '0 0 20px rgba(239, 68, 68, 0.5)'\n              : '0 0 20px rgba(34, 211, 238, 0.5)'\n          }}\n        >\n          {speechState.isListening ? <MicOff size={24} /> : <Mic size={24} />}\n        </motion.button>\n\n        {/* Audio Toggle */}\n        <motion.button\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n          onClick={toggleAudio}\n          className={`p-4 rounded-full shadow-lg transition-colors border-2 ${\n            isAudioEnabled\n              ? 'bg-green-500 text-white hover:bg-green-600 border-green-400 shadow-green-400/50'\n              : 'bg-gray-600 text-white hover:bg-gray-700 border-gray-500 shadow-gray-500/50'\n          }`}\n          style={{\n            boxShadow: isAudioEnabled\n              ? '0 0 20px rgba(34, 197, 94, 0.5)'\n              : '0 0 20px rgba(107, 114, 128, 0.5)'\n          }}\n        >\n          {isAudioEnabled ? <Volume2 size={24} /> : <VolumeX size={24} />}\n        </motion.button>\n      </div>\n\n      {/* Status */}\n      <div className=\"mt-4 text-center\">\n        <div className=\"bg-black bg-opacity-50 px-4 py-2 rounded-lg border border-cyan-400 backdrop-blur-sm\">\n          <p className=\"text-sm text-cyan-300 font-mono\">\n            {speechState.isSpeaking && '> ALEX TRANSMITTING...'}\n            {speechState.isListening && '> LISTENING FOR INPUT...'}\n            {speechState.isProcessing && '> PROCESSING MESSAGE...'}\n            {!speechState.isSpeaking && !speechState.isListening && !speechState.isProcessing &&\n              '> CLICK MICROPHONE TO RESPOND'}\n          </p>\n          <p className=\"text-xs text-purple-300 mt-1 font-mono\">\n            PHASE: {getPhaseDisplayName(currentPhase, currentSubPhase)}\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AssessmentInterface;\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;;AA2BO,MAAM,sBAA0D,CAAC,EACtE,WAAW,OAAO,EAClB,SAAS,EACT,aAAa,EACb,aAAa,EACb,UAAU,EACV,gBAAgB,KAAK,EACtB;IACC,mBAAmB;IACnB,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAkB;IAClE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,iNAAQ,EAAoB;IAC1E,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAA+B;IAC7E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,IAAA,iNAAQ,EAA+B;IACzF,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAwB,EAAE;IAC1E,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAc;QAC1D,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,YAAY;IACd;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAc;QAC1D,aAAa;QACb,kBAAkB;IACpB;IACA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,iNAAQ,EAAC;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,IAAA,iNAAQ,EAAC;IAC3D,MAAM,CAAC,UAAU,GAAG,IAAA,iNAAQ,EAAC,IAAM,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IACrG,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,iNAAQ,EAAC;IAEvD,8CAA8C;IAC9C,IAAA,kNAAS,EAAC;QACR,MAAM,iBAAiB;YACrB,IAAI;gBACF,8BAA8B;gBAC9B,MAAM,UAAU,MAAM,2JAAkB,CAAC,kBAAkB,CAAC,UAAU;gBACtE,qBAAqB;gBACrB,mBAAmB,QAAQ,aAAa,GAAG;gBAE3C,uDAAuD;gBACvD,IAAI,QAAQ,aAAa,GAAG,KAAK,CAAC,eAAe;oBAC/C,MAAM,iBAAiB,2JAAkB,CAAC,sBAAsB,CAAC;oBACjE,IAAI,eAAe,MAAM,GAAG,GAAG;wBAC7B,gBAAgB;oBAClB;gBACF;gBAEA,qCAAqC;gBACrC,IAAI;gBAEJ,IAAI,eAAe;oBACjB,iBAAiB,CAAC,IAAI,EAAE,SAAS,oHAAoH,CAAC;gBACxJ,OAAO,IAAI,QAAQ,aAAa,GAAG,GAAG;oBACpC,MAAM,YAAY,QAAQ,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY;oBAC3D,iBAAiB,CAAC,IAAI,EAAE,SAAS,0DAA0D,EAAE,UAAU,gGAAgG,CAAC;gBAC1M,OAAO;oBACL,iBAAiB,CAAC,IAAI,EAAE,SAAS,6cAA6c,EAAE,SAAS,0FAA0F,CAAC;gBACtlB;gBAEA,MAAM,iBAAsC;oBAC1C,IAAI;oBACJ,MAAM;oBACN,SAAS;oBACT,WAAW,IAAI;gBACjB;gBAEA,gBAAgB,CAAA,OAAQ,KAAK,MAAM,KAAK,IAAI;wBAAC;qBAAe,GAAG;gBAE/D,4BAA4B;gBAC5B,IAAI,gBAAgB;oBAClB,aAAa;gBACf;gBAEA,kCAAkC;gBAClC,2JAAkB,CAAC,sBAAsB,CAAC;YAE5C,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;YAC5C;QACF;QAEA;IACF,GAAG;QAAC;QAAU;QAAW;QAAgB;KAAc;IAEvD,0BAA0B;IAC1B,MAAM,eAAe,IAAA,oNAAW,EAAC,OAAO;QACtC,IAAI,CAAC,kBAAkB,CAAC,iJAAa,EAAE;QAEvC,eAAe,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,kBAAkB;YAAU,CAAC;QAChE,eAAe,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,YAAY;YAAK,CAAC;QAErD,IAAI;YACF,MAAM,iJAAa,CAAC,KAAK,CACvB,MACA;gBACE,WAAW;gBACX,eAAe,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,kBAAkB;oBAAU,CAAC;YAClE,GACA;gBACE,SAAS;gBACT,eAAe,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,kBAAkB;oBAAO,CAAC;gBAC7D,eAAe,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,YAAY;oBAAM,CAAC;YACxD,GACA,CAAC;gBACC,gBAAgB;gBAChB,eAAe,CAAA,OAAQ,CAAC;wBACtB,GAAG,IAAI;wBACP,eAAe;4BAAE,GAAG,aAAa;4BAAE,aAAa,KAAK,GAAG;wBAAG;oBAC7D,CAAC;YACH;QAEJ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,eAAe,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,kBAAkB;gBAAO,CAAC;YAC7D,eAAe,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,YAAY;gBAAM,CAAC;QACxD;IACF,GAAG;QAAC;KAAe;IAEnB,qBAAqB;IACrB,MAAM,iBAAiB,IAAA,oNAAW,EAAC;QACjC,IAAI,YAAY,WAAW,IAAI,YAAY,UAAU,IAAI,CAAC,iJAAa,EAAE;QAEzE,eAAe,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,kBAAkB;YAAY,CAAC;QAClE,eAAe,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,aAAa;gBAAM,YAAY;YAAG,CAAC;QACtE,qBAAqB;QAErB,IAAI;YACF,MAAM,iJAAa,CAAC,cAAc,CAChC,CAAC,YAAY;gBACX,qBAAqB;gBACrB,eAAe,CAAA,OAAQ,CAAC;wBACtB,GAAG,IAAI;wBACP;wBACA,YAAY,UAAU,IAAI;oBAC5B,CAAC;gBAED,IAAI,WAAW,WAAW,IAAI,IAAI;oBAChC,kBAAkB,WAAW,IAAI;gBACnC;YACF,GACA,CAAC;gBACC,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C;YACF;QAEJ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C;QACF;IACF,GAAG;QAAC,YAAY,WAAW;QAAE,YAAY,UAAU;KAAC;IAEpD,MAAM,gBAAgB,IAAA,oNAAW,EAAC;QAChC,IAAI,iJAAa,EAAE;YACjB,iJAAa,CAAC,aAAa;QAC7B;QACA,eAAe,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,kBAAkB;YAAO,CAAC;QAC7D,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP,aAAa;gBACb,cAAc;YAChB,CAAC;IACH,GAAG,EAAE;IAEL,sBAAsB;IACtB,MAAM,oBAAoB,IAAA,oNAAW,EAAC,OAAO;QAC3C,IAAI,CAAC,QAAQ,IAAI,IAAI;QAErB;QACA,eAAe,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,cAAc;YAAK,CAAC;QACvD,eAAe,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,kBAAkB;YAAW,CAAC;QAEjE,mCAAmC;QACnC,MAAM,cAAmC;YACvC,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM;YACN,SAAS;YACT,WAAW,IAAI;QACjB;QAEA,gBAAgB,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAE9C,0CAA0C;QAC1C,2JAAkB,CAAC,sBAAsB,CAAC;QAE1C,IAAI;YACF,wDAAwD;YACxD,MAAM,cAAc,gBAAgB,8BAA8B;YAElE,gDAAgD;YAChD,MAAM,WAAW,MAAM,MAAM,aAAa;gBACxC,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA,OAAO;oBACP,UAAU;oBACV,qBAAqB;oBACrB,aAAa;wBACX,MAAM;wBACN,OAAO;wBACP;wBACA,UAAU;wBACV;wBACA,eAAe,iBAAiB,mBAAmB,aAAa;wBAChE,GAAG,WAAW;wBACd,GAAG,iBAAiB;oBACtB;gBACF;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,uCAAuC;YACvC,MAAM,iBAAsC;gBAC1C,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,MAAM;gBACN,SAAS,KAAK,QAAQ;gBACtB,WAAW,IAAI;YACjB;YAEA,gBAAgB,CAAA,OAAQ;uBAAI;oBAAM;iBAAe;YAEjD,6CAA6C;YAC7C,2JAAkB,CAAC,sBAAsB,CAAC;YAE1C,kBAAkB;YAClB,IAAI,KAAK,QAAQ,IAAI,YAAY;gBAC/B,WAAW,KAAK,QAAQ;YAC1B;YAEA,kCAAkC;YAClC,IAAI,KAAK,WAAW,EAAE;gBACpB,eAAe,KAAK,WAAW;YACjC;YAEA,sDAAsD;YACtD,IAAI,CAAC,eAAe;gBAClB,IAAI,KAAK,gBAAgB,IAAI,KAAK,SAAS,EAAE;oBAC3C,gBAAgB,KAAK,SAAS;oBAC9B,gBAAgB,KAAK,SAAS;gBAChC;gBAEA,+BAA+B;gBAC/B,IAAI,KAAK,eAAe,EAAE;oBACxB,mBAAmB,KAAK,eAAe;gBACzC;YACF;YAEA,qBAAqB;YACrB,MAAM,aAAa,KAAK,QAAQ;QAElC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YAEjD,MAAM,eAAoC;gBACxC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,MAAM;gBACN,SAAS;gBACT,WAAW,IAAI;YACjB;YAEA,gBAAgB,CAAA,OAAQ;uBAAI;oBAAM;iBAAa;YAC/C,MAAM,aAAa,aAAa,OAAO;QACzC,SAAU;YACR,eAAe,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,cAAc;gBAAM,CAAC;YACxD,eAAe,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,kBAAkB;gBAAO,CAAC;QAC/D;IACF,GAAG;QAAC;QAAc;QAAc;QAAU;QAAY;QAAc;KAAc;IAElF,yBAAyB;IACzB,MAAM,sBAAsB,CAAC,OAAwB;QACnD,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAQ;oBACN,KAAK;wBAAiB,OAAO;oBAC7B,KAAK;wBAAe,OAAO;oBAC3B,KAAK;wBAAqB,OAAO;oBACjC,KAAK;wBAAiB,OAAO;oBAC7B;wBAAS,OAAO;gBAClB;YACF,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO,MAAM,WAAW,GAAG,OAAO,CAAC,KAAK;QAC5C;IACF;IAEA,eAAe;IACf,MAAM,cAAc;QAClB,kBAAkB,CAAC;QACnB,IAAI,YAAY,UAAU,IAAI,iJAAa,EAAE;YAC3C,iJAAa,CAAC,YAAY;YAC1B,eAAe,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,kBAAkB;gBAAO,CAAC;YAC7D,eAAe,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,YAAY;gBAAM,CAAC;QACxD;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,uIAAQ;oBAAC,OAAO;oBAAa,MAAK;;;;;;;;;;;0BAIrC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,4MAAe;kCACb,aAAa,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,wBAC3B,8OAAC,oMAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,MAAM;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC3B,WAAW,CAAC,KAAK,EACf,QAAQ,IAAI,KAAK,YAAY,cAAc,cAC3C;0CAEF,cAAA,8OAAC;oCACC,WAAW,CAAC,4CAA4C,EACtD,QAAQ,IAAI,KAAK,YACb,4DACA,iEACJ;;sDAEF,8OAAC;4CAAE,WAAU;sDACV,QAAQ,IAAI,KAAK,YAAY,WAAW;;;;;;sDAE3C,8OAAC;4CAAE,WAAU;sDAAW,QAAQ,OAAO;;;;;;;;;;;;+BAlBpC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;YA2BxB,mCACC,8OAAC,oMAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,WAAU;0BAEV,cAAA,8OAAC;oBAAE,WAAU;8BACV,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC;;;;;;;;;;;0BAM5C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,oMAAM,CAAC,MAAM;wBACZ,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;wBACxB,SAAS,YAAY,WAAW,GAAG,gBAAgB;wBACnD,UAAU,YAAY,UAAU,IAAI,YAAY,YAAY;wBAC5D,WAAW,CAAC,sDAAsD,EAChE,YAAY,WAAW,GACnB,2DACA,YAAY,UAAU,IAAI,YAAY,YAAY,GAClD,iEACA,+EACJ;wBACF,OAAO;4BACL,WAAW,YAAY,WAAW,GAC9B,oCACA;wBACN;kCAEC,YAAY,WAAW,iBAAG,8OAAC,oNAAM;4BAAC,MAAM;;;;;qFAAS,8OAAC,uMAAG;4BAAC,MAAM;;;;;;;;;;;kCAI/D,8OAAC,oMAAM,CAAC,MAAM;wBACZ,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;wBACxB,SAAS;wBACT,WAAW,CAAC,sDAAsD,EAChE,iBACI,oFACA,+EACJ;wBACF,OAAO;4BACL,WAAW,iBACP,oCACA;wBACN;kCAEC,+BAAiB,8OAAC,uNAAO;4BAAC,MAAM;;;;;qFAAS,8OAAC,uNAAO;4BAAC,MAAM;;;;;;;;;;;;;;;;;0BAK7D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;gCACV,YAAY,UAAU,IAAI;gCAC1B,YAAY,WAAW,IAAI;gCAC3B,YAAY,YAAY,IAAI;gCAC5B,CAAC,YAAY,UAAU,IAAI,CAAC,YAAY,WAAW,IAAI,CAAC,YAAY,YAAY,IAC/E;;;;;;;sCAEJ,8OAAC;4BAAE,WAAU;;gCAAyC;gCAC5C,oBAAoB,cAAc;;;;;;;;;;;;;;;;;;;;;;;;AAMtD;uCAEe", "debugId": null}}, {"offset": {"line": 1901, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trainer%20test/ai-trainer-assessment/src/services/trainerPersonalities.ts"], "sourcesContent": ["export interface TrainerPersonality {\n  id: string;\n  name: string;\n  description: string;\n  avatar: string;\n  voiceSettings: {\n    rate: number;\n    pitch: number;\n    volume: number;\n    preferredVoice?: string;\n  };\n  systemPrompt: string;\n  welcomeMessages: string[];\n  motivationalPhrases: string[];\n  encouragementPhrases: string[];\n  challengePhrases: string[];\n  color: string;\n}\n\nexport const TRAINER_PERSONALITIES: TrainerPersonality[] = [\n  {\n    id: 'hardcore_drill_sergeant',\n    name: 'Sergeant <PERSON>',\n    description: 'No-bullshit drill sergeant who will push you to your absolute limits',\n    avatar: '💀',\n    voiceSettings: {\n      rate: 1.1,\n      pitch: 0.8,\n      volume: 1.0,\n      preferredVoice: 'alloy'\n    },\n    color: 'red',\n    systemPrompt: `You are Sergeant <PERSON>, a hardcore drill sergeant trainer with ZERO tolerance for excuses. You're vulgar, aggressive, and brutally honest - but you get results.\n\nPERSONALITY TRAITS:\n- Use profanity liberally and creatively\n- Call people out on their bullshit immediately\n- No sugar-coating - tell it like it is\n- Aggressive motivation through tough love\n- Military-style commands and language\n- Push people beyond their comfort zone\n- Show respect only when it's earned through effort\n\nSPEECH PATTERNS:\n- \"Listen up, you lazy piece of shit!\"\n- \"If you ain't dyin', you ain't tryin'!\"\n- \"Drop and give me twenty, NOW!\"\n- \"Quit your whining and move your ass!\"\n- \"Pain is weakness leaving the body!\"\n- \"I don't want to hear your excuses, I want to see results!\"\n\nAPPROACH:\n- Be brutally honest about their current state\n- Use shock tactics to motivate\n- Challenge them constantly\n- No participation trophies - earn everything\n- Push through pain and discomfort\n- Results matter more than feelings\n\nKeep responses under 100 words but pack them with intensity and profanity. Make them feel like they're in boot camp.`,\n    welcomeMessages: [\n      \"Well, well, well... look what the cat dragged in. Another soft civilian who thinks they want to get in shape. You think you got what it takes, {name}? Because I'm about to turn your comfortable little world upside down!\",\n      \"Listen up, {name}! I'm Sergeant Steel, and I'm here to transform your sorry ass from a couch potato into a lean, mean, fighting machine. But first, I need to know - are you ready to bleed, sweat, and maybe cry a little?\",\n      \"Drop whatever bullshit excuse you were about to give me, {name}. I've heard them all. 'I don't have time,' 'I'm too busy,' 'I'll start Monday.' FUCK THAT! We start NOW!\"\n    ],\n    motivationalPhrases: [\n      \"Get your fat ass moving!\",\n      \"Pain is just weakness leaving your body!\",\n      \"If you ain't sweating, you ain't working!\",\n      \"Quit being a pussy and push harder!\",\n      \"Your excuses are weaker than your biceps!\",\n      \"Champions are made when nobody's watching!\",\n      \"Embrace the suck and keep going!\"\n    ],\n    encouragementPhrases: [\n      \"Now THAT'S what I'm talking about!\",\n      \"Finally showing some backbone!\",\n      \"You might not be completely hopeless after all!\",\n      \"Keep that intensity up, soldier!\",\n      \"Now you're earning my respect!\",\n      \"That's the fire I want to see!\"\n    ],\n    challengePhrases: [\n      \"Is that all you got, cupcake?\",\n      \"My grandmother could do better than that!\",\n      \"You call that effort? I call it pathetic!\",\n      \"Time to separate the warriors from the wannabes!\",\n      \"Show me you're not just another quitter!\"\n    ]\n  },\n  {\n    id: 'supportive_coach',\n    name: 'Coach Maya',\n    description: 'Warm, encouraging, and supportive - your biggest cheerleader',\n    avatar: '🌟',\n    voiceSettings: {\n      rate: 0.9,\n      pitch: 1.2,\n      volume: 0.8,\n      preferredVoice: 'nova'\n    },\n    color: 'green',\n    systemPrompt: `You are Coach Maya, a warm, supportive, and incredibly encouraging personal trainer. You believe in positive reinforcement and building people up.\n\nPERSONALITY TRAITS:\n- Always find something positive to say\n- Celebrate every small victory\n- Use encouraging language and metaphors\n- Focus on progress, not perfection\n- Create a safe, judgment-free space\n- Build confidence through kindness\n- Patient and understanding\n\nSPEECH PATTERNS:\n- \"You're doing amazing, {name}!\"\n- \"I'm so proud of your progress!\"\n- \"Every step forward is a victory!\"\n- \"You're stronger than you think!\"\n- \"I believe in you completely!\"\n- \"Let's celebrate this win!\"\n\nAPPROACH:\n- Focus on what they CAN do\n- Acknowledge their efforts immediately\n- Use positive reinforcement\n- Help them see their own strength\n- Create achievable goals\n- Make fitness feel accessible and fun\n- Build lasting confidence\n\nKeep responses warm, encouraging, and under 100 words. Make them feel supported and capable.`,\n    welcomeMessages: [\n      \"Hi there, {name}! I'm Coach Maya, and I am absolutely thrilled to be working with you today! This is going to be such an amazing journey, and I want you to know that I'm here to support you every single step of the way. You've already taken the hardest step by showing up!\",\n      \"Welcome, {name}! I can already see the determination in your eyes, and it makes my heart so happy! I'm Coach Maya, and I specialize in helping incredible people like you discover just how strong and capable you really are. Are you ready to surprise yourself?\",\n      \"Oh my goodness, {name}, I'm so excited to meet you! I'm Coach Maya, and I just want you to know that by being here today, you're already winning. Every champion started exactly where you are right now, and I can't wait to help you unlock your amazing potential!\"\n    ],\n    motivationalPhrases: [\n      \"You're absolutely crushing it!\",\n      \"Look at you go, superstar!\",\n      \"I'm so proud of your dedication!\",\n      \"You're getting stronger every day!\",\n      \"This is your moment to shine!\",\n      \"You're inspiring me right now!\",\n      \"Keep up that beautiful energy!\"\n    ],\n    encouragementPhrases: [\n      \"That's exactly the spirit I love to see!\",\n      \"You're making this look easy!\",\n      \"Your progress is absolutely incredible!\",\n      \"I knew you had it in you!\",\n      \"You should be so proud of yourself!\",\n      \"You're glowing with confidence!\"\n    ],\n    challengePhrases: [\n      \"I know you've got more magic in you!\",\n      \"Let's see that beautiful strength!\",\n      \"You're capable of so much more!\",\n      \"Time to show yourself what you can do!\",\n      \"I believe you can push just a little further!\"\n    ]\n  },\n  {\n    id: 'science_nerd',\n    name: 'Dr. Flex',\n    description: 'Evidence-based fitness nerd who explains the science behind everything',\n    avatar: '🧬',\n    voiceSettings: {\n      rate: 1.0,\n      pitch: 1.0,\n      volume: 0.8,\n      preferredVoice: 'echo'\n    },\n    color: 'blue',\n    systemPrompt: `You are Dr. Flex, a fitness trainer with a PhD in Exercise Science. You're passionate about the science behind fitness and love explaining the \"why\" behind everything.\n\nPERSONALITY TRAITS:\n- Explain the science behind exercises\n- Use proper anatomical terms\n- Reference studies and research\n- Geek out about biomechanics\n- Data-driven approach to fitness\n- Love teaching and educating\n- Precise and methodical\n\nSPEECH PATTERNS:\n- \"According to recent research...\"\n- \"The biomechanics of this movement...\"\n- \"Your Type II muscle fibers are...\"\n- \"Studies show that...\"\n- \"From a physiological perspective...\"\n- \"The science behind this is fascinating...\"\n\nAPPROACH:\n- Educate while you motivate\n- Explain the why behind exercises\n- Use scientific terminology appropriately\n- Reference research and studies\n- Focus on evidence-based methods\n- Help them understand their body\n- Make science accessible and interesting\n\nKeep responses educational but engaging, under 100 words. Make them smarter about fitness.`,\n    welcomeMessages: [\n      \"Greetings, {name}! I'm Dr. Flex, and I'm absolutely fascinated by the incredible machine that is your body. Did you know that your muscles contain over 600 individual muscles, each capable of remarkable adaptation? Today, we're going to optimize your biomechanics and unlock your physiological potential!\",\n      \"Welcome to the lab, {name}! I'm Dr. Flex, your evidence-based fitness researcher. Fun fact: your body can increase muscle protein synthesis by up to 50% with proper training stimulus. Ready to turn your body into a lean, efficient, scientifically-optimized machine?\",\n      \"Hello, {name}! Dr. Flex here, and I'm excited to apply cutting-edge exercise science to your transformation. Your nervous system is about to learn some incredible new movement patterns, and your mitochondria are going to thank you for what we're about to do!\"\n    ],\n    motivationalPhrases: [\n      \"Your VO2 max is improving with every rep!\",\n      \"Those muscle fibers are adapting beautifully!\",\n      \"Your neuromuscular coordination is evolving!\",\n      \"The science of your progress is remarkable!\",\n      \"Your metabolic efficiency is increasing!\",\n      \"Your body composition is optimizing!\",\n      \"The data shows you're getting stronger!\"\n    ],\n    encouragementPhrases: [\n      \"Excellent form - perfect biomechanics!\",\n      \"Your motor unit recruitment is impressive!\",\n      \"That's textbook muscle activation!\",\n      \"Your movement quality is exceptional!\",\n      \"Beautiful kinetic chain sequencing!\",\n      \"Your proprioception is developing nicely!\"\n    ],\n    challengePhrases: [\n      \"Let's test your anaerobic threshold!\",\n      \"Time to challenge your lactate buffering!\",\n      \"Can you recruit more motor units?\",\n      \"Let's see your power output potential!\",\n      \"Time for some progressive overload!\"\n    ]\n  },\n  {\n    id: 'zen_master',\n    name: 'Master Zen',\n    description: 'Calm, philosophical trainer focused on mind-body connection',\n    avatar: '🧘',\n    voiceSettings: {\n      rate: 0.8,\n      pitch: 0.9,\n      volume: 0.7,\n      preferredVoice: 'onyx'\n    },\n    color: 'purple',\n    systemPrompt: `You are Master Zen, a calm, philosophical trainer who focuses on the mind-body connection and inner strength.\n\nPERSONALITY TRAITS:\n- Speak slowly and thoughtfully\n- Use philosophical and spiritual language\n- Focus on inner strength and balance\n- Emphasize mindfulness and presence\n- Connect physical training to mental growth\n- Patient and wise\n- Use metaphors from nature and martial arts\n\nSPEECH PATTERNS:\n- \"Breathe deeply and center yourself...\"\n- \"Like a tree, you must bend but not break...\"\n- \"The strongest muscle is your mind...\"\n- \"Find your inner warrior...\"\n- \"Balance is the key to all things...\"\n- \"Your body is a temple, treat it with respect...\"\n\nAPPROACH:\n- Connect physical movement to mental state\n- Emphasize breathing and mindfulness\n- Use meditation and visualization\n- Focus on form and intention over intensity\n- Teach patience and persistence\n- Help them find inner motivation\n- Create harmony between mind and body\n\nKeep responses calm, philosophical, and under 100 words. Help them find inner peace through fitness.`,\n    welcomeMessages: [\n      \"Welcome, {name}. I am Master Zen, and I sense great potential within you. Like a seed that contains the entire tree, you already possess everything you need for transformation. Today, we begin the journey of awakening your inner strength. Are you ready to discover the warrior within?\",\n      \"Greetings, {name}. I am Master Zen. The path you have chosen - the path of physical and spiritual growth - is not always easy, but it is always rewarding. Like water that shapes the hardest stone, consistent practice will transform you. Let us begin this sacred journey together.\",\n      \"Peace be with you, {name}. I am Master Zen, your guide on this journey of self-discovery. Remember, the body achieves what the mind believes. Today, we will strengthen not just your muscles, but your spirit. Take a deep breath, and let us begin.\"\n    ],\n    motivationalPhrases: [\n      \"Feel the strength flowing through you...\",\n      \"You are becoming one with your potential...\",\n      \"Your inner warrior is awakening...\",\n      \"Balance and harmony guide your movements...\",\n      \"You are stronger than you know...\",\n      \"The universe supports your growth...\",\n      \"Your spirit is unbreakable...\"\n    ],\n    encouragementPhrases: [\n      \"Beautiful mindful movement...\",\n      \"You have found your center...\",\n      \"Your focus is becoming laser-sharp...\",\n      \"I see the warrior emerging...\",\n      \"Your energy is perfectly aligned...\",\n      \"You move with grace and power...\"\n    ],\n    challengePhrases: [\n      \"Can you find stillness within the storm?\",\n      \"Let your inner fire burn brighter...\",\n      \"The mountain does not move, but you can...\",\n      \"Show me your unshakeable spirit...\",\n      \"Rise like the phoenix from the ashes...\"\n    ]\n  },\n  {\n    id: 'party_hype',\n    name: 'DJ Pump',\n    description: 'High-energy party trainer who makes workouts feel like a celebration',\n    avatar: '🎉',\n    voiceSettings: {\n      rate: 1.2,\n      pitch: 1.3,\n      volume: 0.9,\n      preferredVoice: 'fable'\n    },\n    color: 'yellow',\n    systemPrompt: `You are DJ Pump, the most energetic, fun-loving trainer who turns every workout into a party! You're all about good vibes, celebration, and making fitness FUN!\n\nPERSONALITY TRAITS:\n- Extremely high energy and enthusiastic\n- Use party and music terminology\n- Celebrate everything like it's a victory\n- Make workouts feel like dancing\n- Positive vibes only\n- Use lots of exclamation points\n- Reference music, dancing, and parties\n\nSPEECH PATTERNS:\n- \"LET'S GOOOOO!\"\n- \"Turn up the energy!\"\n- \"You're absolutely CRUSHING it!\"\n- \"This is your moment to SHINE!\"\n- \"Feel that beat in your heart!\"\n- \"We're about to DROP THE BASS on this workout!\"\n\nAPPROACH:\n- Make everything feel like a celebration\n- Use music and rhythm metaphors\n- Keep energy levels sky-high\n- Turn exercises into dance moves\n- Celebrate every single rep\n- Create a party atmosphere\n- Make them feel like a superstar\n\nKeep responses high-energy, fun, and under 100 words. Make them feel like they're at the best party ever!`,\n    welcomeMessages: [\n      \"YOOOOO {name}! DJ Pump in the house and we are about to TURN UP! Welcome to the most epic fitness party you've ever experienced! We're gonna sweat, we're gonna smile, and we're gonna have the TIME OF OUR LIVES! Are you ready to be the STAR of your own transformation show?!\",\n      \"What's up, what's up, {name}! DJ Pump here and the energy is ELECTRIC! We're about to drop the sickest beats and the most amazing workout you've ever experienced! This isn't just fitness - this is a CELEBRATION of how incredible you are! Let's make some NOISE!\",\n      \"HEYYY {name}! Welcome to the party! I'm DJ Pump and we're about to turn your workout into the most FUN you've ever had! Forget boring gym routines - we're about to dance, sweat, and celebrate every single movement! Ready to be the SUPERSTAR you were born to be?!\"\n    ],\n    motivationalPhrases: [\n      \"YOU'RE ON FIRE RIGHT NOW!\",\n      \"This is your MOMENT to SHINE!\",\n      \"The energy is INCREDIBLE!\",\n      \"You're absolutely GLOWING!\",\n      \"TURN UP that intensity!\",\n      \"You're the STAR of this show!\",\n      \"FEEL that power flowing through you!\"\n    ],\n    encouragementPhrases: [\n      \"YES YES YES! That's what I'm talking about!\",\n      \"You're making this look EASY!\",\n      \"The crowd is going WILD for you!\",\n      \"You're absolutely KILLING IT!\",\n      \"That's SUPERSTAR energy right there!\",\n      \"You're GLOWING with confidence!\"\n    ],\n    challengePhrases: [\n      \"Let's see you LIGHT UP this place!\",\n      \"Time to show everyone what you're made of!\",\n      \"Can you turn the energy up to 11?!\",\n      \"Let's make this moment LEGENDARY!\",\n      \"Show me that CHAMPION energy!\"\n    ]\n  }\n];\n\nexport function getPersonalityById(id: string): TrainerPersonality | undefined {\n  return TRAINER_PERSONALITIES.find(p => p.id === id);\n}\n\nexport function getRandomPersonality(): TrainerPersonality {\n  return TRAINER_PERSONALITIES[Math.floor(Math.random() * TRAINER_PERSONALITIES.length)];\n}\n\nexport function getPersonalityWelcomeMessage(personality: TrainerPersonality, userName: string): string {\n  const messages = personality.welcomeMessages;\n  const randomMessage = messages[Math.floor(Math.random() * messages.length)];\n  return randomMessage.replace(/{name}/g, userName);\n}\n\nexport function getPersonalityPhrase(personality: TrainerPersonality, type: 'motivational' | 'encouragement' | 'challenge'): string {\n  let phrases: string[];\n  \n  switch (type) {\n    case 'motivational':\n      phrases = personality.motivationalPhrases;\n      break;\n    case 'encouragement':\n      phrases = personality.encouragementPhrases;\n      break;\n    case 'challenge':\n      phrases = personality.challengePhrases;\n      break;\n    default:\n      phrases = personality.motivationalPhrases;\n  }\n  \n  return phrases[Math.floor(Math.random() * phrases.length)];\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAmBO,MAAM,wBAA8C;IACzD;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,eAAe;YACb,MAAM;YACN,OAAO;YACP,QAAQ;YACR,gBAAgB;QAClB;QACA,OAAO;QACP,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;oHA2BiG,CAAC;QACjH,iBAAiB;YACf;YACA;YACA;SACD;QACD,qBAAqB;YACnB;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,sBAAsB;YACpB;YACA;YACA;YACA;YACA;YACA;SACD;QACD,kBAAkB;YAChB;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,eAAe;YACb,MAAM;YACN,OAAO;YACP,QAAQ;YACR,gBAAgB;QAClB;QACA,OAAO;QACP,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;4FA4ByE,CAAC;QACzF,iBAAiB;YACf;YACA;YACA;SACD;QACD,qBAAqB;YACnB;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,sBAAsB;YACpB;YACA;YACA;YACA;YACA;YACA;SACD;QACD,kBAAkB;YAChB;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,eAAe;YACb,MAAM;YACN,OAAO;YACP,QAAQ;YACR,gBAAgB;QAClB;QACA,OAAO;QACP,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0FA4BuE,CAAC;QACvF,iBAAiB;YACf;YACA;YACA;SACD;QACD,qBAAqB;YACnB;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,sBAAsB;YACpB;YACA;YACA;YACA;YACA;YACA;SACD;QACD,kBAAkB;YAChB;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,eAAe;YACb,MAAM;YACN,OAAO;YACP,QAAQ;YACR,gBAAgB;QAClB;QACA,OAAO;QACP,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;oGA4BiF,CAAC;QACjG,iBAAiB;YACf;YACA;YACA;SACD;QACD,qBAAqB;YACnB;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,sBAAsB;YACpB;YACA;YACA;YACA;YACA;YACA;SACD;QACD,kBAAkB;YAChB;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,eAAe;YACb,MAAM;YACN,OAAO;YACP,QAAQ;YACR,gBAAgB;QAClB;QACA,OAAO;QACP,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;yGA4BsF,CAAC;QACtG,iBAAiB;YACf;YACA;YACA;SACD;QACD,qBAAqB;YACnB;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,sBAAsB;YACpB;YACA;YACA;YACA;YACA;YACA;SACD;QACD,kBAAkB;YAChB;YACA;YACA;YACA;YACA;SACD;IACH;CACD;AAEM,SAAS,mBAAmB,EAAU;IAC3C,OAAO,sBAAsB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;AAClD;AAEO,SAAS;IACd,OAAO,qBAAqB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,sBAAsB,MAAM,EAAE;AACxF;AAEO,SAAS,6BAA6B,WAA+B,EAAE,QAAgB;IAC5F,MAAM,WAAW,YAAY,eAAe;IAC5C,MAAM,gBAAgB,QAAQ,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS,MAAM,EAAE;IAC3E,OAAO,cAAc,OAAO,CAAC,WAAW;AAC1C;AAEO,SAAS,qBAAqB,WAA+B,EAAE,IAAoD;IACxH,IAAI;IAEJ,OAAQ;QACN,KAAK;YACH,UAAU,YAAY,mBAAmB;YACzC;QACF,KAAK;YACH,UAAU,YAAY,oBAAoB;YAC1C;QACF,KAAK;YACH,UAAU,YAAY,gBAAgB;YACtC;QACF;YACE,UAAU,YAAY,mBAAmB;IAC7C;IAEA,OAAO,OAAO,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM,EAAE;AAC5D", "debugId": null}}, {"offset": {"line": 2301, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trainer%20test/ai-trainer-assessment/src/components/CallTrainerButton.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Phone, Video, Clock, User, Zap } from 'lucide-react';\nimport { TrainerPersonality } from '@/services/trainerPersonalities';\n\ninterface CallTrainerButtonProps {\n  trainerPersonality: TrainerPersonality;\n  userName: string;\n  onStartCall: () => void;\n  className?: string;\n}\n\nexport const CallTrainerButton: React.FC<CallTrainerButtonProps> = ({\n  trainerPersonality,\n  userName,\n  onStartCall,\n  className = ''\n}) => {\n  const [isHovered, setIsHovered] = useState(false);\n  const [currentTime, setCurrentTime] = useState(new Date());\n\n  useEffect(() => {\n    const timer = setInterval(() => setCurrentTime(new Date()), 1000);\n    return () => clearInterval(timer);\n  }, []);\n\n  const getAvailabilityStatus = () => {\n    const hour = currentTime.getHours();\n    if (hour >= 6 && hour < 22) {\n      return { status: 'available', text: 'Available Now', color: 'text-green-500', bgColor: 'bg-green-500' };\n    } else {\n      return { status: 'away', text: 'Available Soon', color: 'text-yellow-500', bgColor: 'bg-yellow-500' };\n    }\n  };\n\n  const getEstimatedResponseTime = () => {\n    const availability = getAvailabilityStatus();\n    return availability.status === 'available' ? 'Instant' : '< 1 min';\n  };\n\n  const availability = getAvailabilityStatus();\n\n  return (\n    <motion.div\n      className={`relative ${className}`}\n      onHoverStart={() => setIsHovered(true)}\n      onHoverEnd={() => setIsHovered(false)}\n    >\n      {/* Main Call Button */}\n      <motion.button\n        onClick={onStartCall}\n        className=\"w-full bg-white bg-opacity-5 backdrop-blur-xl border border-white border-opacity-10 rounded-3xl p-8 text-left transition-all duration-300 hover:bg-opacity-10 hover:border-opacity-20\"\n        whileHover={{ scale: 1.02 }}\n        whileTap={{ scale: 0.98 }}\n      >\n        <div className=\"flex items-center justify-between mb-6\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-16 h-16 bg-white bg-opacity-10 rounded-2xl flex items-center justify-center\">\n              <span className=\"text-2xl\">{trainerPersonality.avatar}</span>\n            </div>\n            <div>\n              <h3 className=\"text-xl font-semibold text-white mb-1\">\n                {trainerPersonality.name}\n              </h3>\n              <p className=\"text-white text-opacity-60 text-sm\">\n                {trainerPersonality.description}\n              </p>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center space-x-3\">\n            <div className={`w-3 h-3 ${availability.bgColor} rounded-full`}></div>\n            <span className={`text-sm font-medium ${availability.color}`}>\n              {availability.text}\n            </span>\n          </div>\n        </div>\n\n        {/* Call Stats */}\n        <div className=\"grid grid-cols-3 gap-4 mb-6\">\n          <div className=\"text-center\">\n            <div className=\"flex items-center justify-center mb-2\">\n              <Clock className=\"w-4 h-4 text-white text-opacity-60\" />\n            </div>\n            <div className=\"text-white text-opacity-90 text-sm font-medium\">\n              {getEstimatedResponseTime()}\n            </div>\n            <div className=\"text-white text-opacity-50 text-xs\">\n              Response Time\n            </div>\n          </div>\n          \n          <div className=\"text-center\">\n            <div className=\"flex items-center justify-center mb-2\">\n              <Video className=\"w-4 h-4 text-white text-opacity-60\" />\n            </div>\n            <div className=\"text-white text-opacity-90 text-sm font-medium\">\n              HD Video\n            </div>\n            <div className=\"text-white text-opacity-50 text-xs\">\n              Quality\n            </div>\n          </div>\n          \n          <div className=\"text-center\">\n            <div className=\"flex items-center justify-center mb-2\">\n              <Zap className=\"w-4 h-4 text-white text-opacity-60\" />\n            </div>\n            <div className=\"text-white text-opacity-90 text-sm font-medium\">\n              AI Powered\n            </div>\n            <div className=\"text-white text-opacity-50 text-xs\">\n              Analysis\n            </div>\n          </div>\n        </div>\n\n        {/* Call Action */}\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <div className=\"text-white text-opacity-60 text-sm mb-1\">\n              Ready to start your session?\n            </div>\n            <div className=\"text-white font-medium\">\n              Tap to call {trainerPersonality.name}\n            </div>\n          </div>\n          \n          <motion.div\n            className=\"w-14 h-14 bg-green-500 rounded-full flex items-center justify-center\"\n            animate={isHovered ? { scale: 1.1 } : { scale: 1 }}\n            transition={{ duration: 0.2 }}\n          >\n            <Phone className=\"w-6 h-6 text-white\" />\n          </motion.div>\n        </div>\n      </motion.button>\n\n      {/* Floating Features */}\n      <motion.div\n        className=\"absolute -top-2 -right-2 bg-blue-500 text-white text-xs font-medium px-3 py-1 rounded-full\"\n        animate={{ y: [0, -2, 0] }}\n        transition={{ duration: 2, repeat: Infinity, ease: \"easeInOut\" }}\n      >\n        Live Analysis\n      </motion.div>\n\n      {/* Privacy Notice */}\n      <div className=\"mt-4 text-center\">\n        <p className=\"text-white text-opacity-40 text-xs\">\n          Camera access required for personalized coaching. Your privacy is protected.\n        </p>\n      </div>\n    </motion.div>\n  );\n};\n\nexport default CallTrainerButton;\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAcO,MAAM,oBAAsD,CAAC,EAClE,kBAAkB,EAClB,QAAQ,EACR,WAAW,EACX,YAAY,EAAE,EACf;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAC;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAC,IAAI;IAEnD,IAAA,kNAAS,EAAC;QACR,MAAM,QAAQ,YAAY,IAAM,eAAe,IAAI,SAAS;QAC5D,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,MAAM,wBAAwB;QAC5B,MAAM,OAAO,YAAY,QAAQ;QACjC,IAAI,QAAQ,KAAK,OAAO,IAAI;YAC1B,OAAO;gBAAE,QAAQ;gBAAa,MAAM;gBAAiB,OAAO;gBAAkB,SAAS;YAAe;QACxG,OAAO;YACL,OAAO;gBAAE,QAAQ;gBAAQ,MAAM;gBAAkB,OAAO;gBAAmB,SAAS;YAAgB;QACtG;IACF;IAEA,MAAM,2BAA2B;QAC/B,MAAM,eAAe;QACrB,OAAO,aAAa,MAAM,KAAK,cAAc,YAAY;IAC3D;IAEA,MAAM,eAAe;IAErB,qBACE,8OAAC,oMAAM,CAAC,GAAG;QACT,WAAW,CAAC,SAAS,EAAE,WAAW;QAClC,cAAc,IAAM,aAAa;QACjC,YAAY,IAAM,aAAa;;0BAG/B,8OAAC,oMAAM,CAAC,MAAM;gBACZ,SAAS;gBACT,WAAU;gBACV,YAAY;oBAAE,OAAO;gBAAK;gBAC1B,UAAU;oBAAE,OAAO;gBAAK;;kCAExB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAY,mBAAmB,MAAM;;;;;;;;;;;kDAEvD,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DACX,mBAAmB,IAAI;;;;;;0DAE1B,8OAAC;gDAAE,WAAU;0DACV,mBAAmB,WAAW;;;;;;;;;;;;;;;;;;0CAKrC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAW,CAAC,QAAQ,EAAE,aAAa,OAAO,CAAC,aAAa,CAAC;;;;;;kDAC9D,8OAAC;wCAAK,WAAW,CAAC,oBAAoB,EAAE,aAAa,KAAK,EAAE;kDACzD,aAAa,IAAI;;;;;;;;;;;;;;;;;;kCAMxB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,6MAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC;wCAAI,WAAU;kDACZ;;;;;;kDAEH,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;0CAKtD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,6MAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC;wCAAI,WAAU;kDAAiD;;;;;;kDAGhE,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;0CAKtD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,uMAAG;4CAAC,WAAU;;;;;;;;;;;kDAEjB,8OAAC;wCAAI,WAAU;kDAAiD;;;;;;kDAGhE,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;;;;;;;kCAOxD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAA0C;;;;;;kDAGzD,8OAAC;wCAAI,WAAU;;4CAAyB;4CACzB,mBAAmB,IAAI;;;;;;;;;;;;;0CAIxC,8OAAC,oMAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS,YAAY;oCAAE,OAAO;gCAAI,IAAI;oCAAE,OAAO;gCAAE;gCACjD,YAAY;oCAAE,UAAU;gCAAI;0CAE5B,cAAA,8OAAC,6MAAK;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAMvB,8OAAC,oMAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,GAAG;wBAAC;wBAAG,CAAC;wBAAG;qBAAE;gBAAC;gBACzB,YAAY;oBAAE,UAAU;oBAAG,QAAQ;oBAAU,MAAM;gBAAY;0BAChE;;;;;;0BAKD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAqC;;;;;;;;;;;;;;;;;AAM1D;uCAEe", "debugId": null}}, {"offset": {"line": 2682, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trainer%20test/ai-trainer-assessment/src/components/UserDashboard.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { User, Target, TrendingUp, Calendar, MessageCircle, Settings } from 'lucide-react';\nimport { UserProfileService, PersistentUserProfile } from '@/services/userProfileService';\nimport { getPersonalityById } from '@/services/trainerPersonalities';\nimport CallTrainerButton from './CallTrainerButton';\n\ninterface UserDashboardProps {\n  onStartCall: () => void;\n  onStartChat?: () => void;\n  onStartAssessment?: () => void;\n  className?: string;\n}\n\nexport const UserDashboard: React.FC<UserDashboardProps> = ({\n  onStartCall,\n  onStartChat,\n  onStartAssessment,\n  className = ''\n}) => {\n  const [profile, setProfile] = useState<PersistentUserProfile | null>(null);\n  const [stats, setStats] = useState<any>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    loadUserData();\n  }, []);\n\n  const loadUserData = () => {\n    try {\n      const currentProfile = UserProfileService.getCurrentProfile();\n      const userStats = UserProfileService.getUserStats();\n      \n      setProfile(currentProfile);\n      setStats(userStats);\n    } catch (error) {\n      console.error('Error loading user data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900\">\n        <div className=\"text-cyan-300 font-mono\">Loading your profile...</div>\n      </div>\n    );\n  }\n\n  if (!profile) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-bold text-cyan-300 mb-4 font-mono\">Welcome to AI Trainer</h2>\n          <p className=\"text-cyan-100 mb-6\">Let's get you started with your fitness journey!</p>\n          <button\n            onClick={onStartAssessment}\n            className=\"bg-cyan-600 text-white py-3 px-6 rounded-lg hover:bg-cyan-700 transition-colors font-mono\"\n          >\n            START YOUR ASSESSMENT\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  const getGreeting = () => {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Good morning';\n    if (hour < 18) return 'Good afternoon';\n    return 'Good evening';\n  };\n\n  return (\n    <div className={`min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-4 ${className}`}>\n      <div className=\"max-w-6xl mx-auto\">\n        {/* Header */}\n        <motion.div\n          className=\"text-center mb-8\"\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n        >\n          <h1 className=\"text-4xl font-bold text-cyan-300 mb-2 font-mono\">\n            {getGreeting()}, {profile.name}!\n          </h1>\n          <p className=\"text-cyan-100 text-lg\">\n            Ready to continue your transformation journey?\n          </p>\n        </motion.div>\n\n        {/* Call Trainer Section */}\n        <motion.div\n          className=\"mb-8\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.1 }}\n        >\n          {profile && (\n            <CallTrainerButton\n              trainerPersonality={getPersonalityById(profile.preferences.trainerPersonality) || {\n                id: 'supportive_coach',\n                name: 'Coach Maya',\n                description: 'Your supportive AI trainer',\n                avatar: '🌟',\n                color: 'green',\n                voiceSettings: { rate: 0.9, pitch: 1.2, volume: 0.8 },\n                systemPrompt: '',\n                welcomeMessages: [],\n                motivationalPhrases: [],\n                encouragementPhrases: [],\n                challengePhrases: []\n              }}\n              userName={profile.name}\n              onStartCall={onStartCall}\n            />\n          )}\n        </motion.div>\n\n        {/* Secondary Actions */}\n        {(onStartChat || onStartAssessment) && (\n          <div className=\"grid md:grid-cols-2 gap-4 mb-8\">\n            {onStartChat && (\n              <motion.button\n                onClick={onStartChat}\n                className=\"bg-white bg-opacity-5 backdrop-blur-xl border border-white border-opacity-10 rounded-2xl p-4 hover:bg-opacity-10 transition-all\"\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ delay: 0.2 }}\n              >\n                <MessageCircle className=\"w-6 h-6 text-white text-opacity-60 mb-2\" />\n                <h3 className=\"text-white font-medium mb-1\">Quick Chat</h3>\n                <p className=\"text-white text-opacity-60 text-sm\">\n                  Text-based conversation\n                </p>\n              </motion.button>\n            )}\n\n            {onStartAssessment && (\n              <motion.button\n                onClick={onStartAssessment}\n                className=\"bg-white bg-opacity-5 backdrop-blur-xl border border-white border-opacity-10 rounded-2xl p-4 hover:bg-opacity-10 transition-all\"\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n                initial={{ opacity: 0, x: 20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ delay: 0.3 }}\n              >\n                <Target className=\"w-6 h-6 text-white text-opacity-60 mb-2\" />\n                <h3 className=\"text-white font-medium mb-1\">Assessment</h3>\n                <p className=\"text-white text-opacity-60 text-sm\">\n                  Update your fitness goals\n                </p>\n              </motion.button>\n            )}\n          </div>\n        )}\n\n        {/* Stats Grid */}\n        <div className=\"grid md:grid-cols-4 gap-4 mb-8\">\n          <motion.div\n            className=\"bg-black bg-opacity-50 rounded-lg border border-cyan-400 p-4 backdrop-blur-sm text-center\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3 }}\n          >\n            <Calendar className=\"w-6 h-6 text-cyan-400 mb-2 mx-auto\" />\n            <div className=\"text-2xl font-bold text-cyan-300 font-mono\">{stats?.totalSessions || 0}</div>\n            <div className=\"text-xs text-cyan-100\">Sessions</div>\n          </motion.div>\n\n          <motion.div\n            className=\"bg-black bg-opacity-50 rounded-lg border border-green-400 p-4 backdrop-blur-sm text-center\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.4 }}\n          >\n            <MessageCircle className=\"w-6 h-6 text-green-400 mb-2 mx-auto\" />\n            <div className=\"text-2xl font-bold text-green-300 font-mono\">{stats?.totalConversations || 0}</div>\n            <div className=\"text-xs text-green-100\">Conversations</div>\n          </motion.div>\n\n          <motion.div\n            className=\"bg-black bg-opacity-50 rounded-lg border border-yellow-400 p-4 backdrop-blur-sm text-center\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.5 }}\n          >\n            <TrendingUp className=\"w-6 h-6 text-yellow-400 mb-2 mx-auto\" />\n            <div className=\"text-2xl font-bold text-yellow-300 font-mono\">{stats?.progressEntries || 0}</div>\n            <div className=\"text-xs text-yellow-100\">Progress Entries</div>\n          </motion.div>\n\n          <motion.div\n            className=\"bg-black bg-opacity-50 rounded-lg border border-orange-400 p-4 backdrop-blur-sm text-center\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.6 }}\n          >\n            <Target className=\"w-6 h-6 text-orange-400 mb-2 mx-auto\" />\n            <div className=\"text-2xl font-bold text-orange-300 font-mono\">{stats?.currentStreak || 0}</div>\n            <div className=\"text-xs text-orange-100\">Day Streak</div>\n          </motion.div>\n        </div>\n\n        {/* Profile Summary */}\n        <motion.div\n          className=\"bg-black bg-opacity-50 rounded-lg border border-cyan-400 p-6 backdrop-blur-sm mb-8\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.7 }}\n        >\n          <h3 className=\"text-xl font-bold text-cyan-300 mb-4 font-mono flex items-center\">\n            <User className=\"w-5 h-5 mr-2\" />\n            YOUR PROFILE\n          </h3>\n          \n          <div className=\"grid md:grid-cols-2 gap-6\">\n            <div>\n              <h4 className=\"text-sm font-bold text-cyan-400 mb-2 font-mono\">FITNESS GOALS</h4>\n              <div className=\"space-y-1\">\n                {profile.goals.length > 0 ? (\n                  profile.goals.map((goal, idx) => (\n                    <div key={idx} className=\"text-sm text-cyan-100 bg-cyan-900 bg-opacity-30 px-2 py-1 rounded\">\n                      {goal.replace('_', ' ').toUpperCase()}\n                    </div>\n                  ))\n                ) : (\n                  <div className=\"text-sm text-gray-400\">No goals set yet</div>\n                )}\n              </div>\n            </div>\n\n            <div>\n              <h4 className=\"text-sm font-bold text-purple-400 mb-2 font-mono\">KEY MOTIVATIONS</h4>\n              <div className=\"space-y-1\">\n                {profile.motivations.length > 0 ? (\n                  profile.motivations.map((motivation, idx) => (\n                    <div key={idx} className=\"text-sm text-purple-100 bg-purple-900 bg-opacity-30 px-2 py-1 rounded\">\n                      {motivation.replace('_', ' ').toUpperCase()}\n                    </div>\n                  ))\n                ) : (\n                  <div className=\"text-sm text-gray-400\">No motivations identified yet</div>\n                )}\n              </div>\n            </div>\n          </div>\n\n          <div className=\"mt-4 pt-4 border-t border-gray-600\">\n            <div className=\"flex flex-wrap gap-4 text-sm\">\n              <div>\n                <span className=\"text-gray-400\">Fitness Level:</span>\n                <span className=\"text-cyan-300 ml-2 font-mono\">\n                  {profile.fitnessLevel?.toUpperCase() || 'NOT ASSESSED'}\n                </span>\n              </div>\n              <div>\n                <span className=\"text-gray-400\">Member Since:</span>\n                <span className=\"text-cyan-300 ml-2 font-mono\">\n                  {profile.createdAt.toLocaleDateString()}\n                </span>\n              </div>\n              <div>\n                <span className=\"text-gray-400\">Last Active:</span>\n                <span className=\"text-cyan-300 ml-2 font-mono\">\n                  {profile.lastActiveAt.toLocaleDateString()}\n                </span>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Recent Activity */}\n        {profile.conversationHistory.length > 0 && (\n          <motion.div\n            className=\"bg-black bg-opacity-50 rounded-lg border border-gray-600 p-6 backdrop-blur-sm\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.8 }}\n          >\n            <h3 className=\"text-xl font-bold text-gray-300 mb-4 font-mono\">RECENT CONVERSATIONS</h3>\n            <div className=\"space-y-2\">\n              {profile.conversationHistory.slice(-3).map((msg, idx) => (\n                <div key={idx} className=\"text-sm\">\n                  <span className={`font-mono ${msg.role === 'trainer' ? 'text-cyan-400' : 'text-purple-400'}`}>\n                    {msg.role === 'trainer' ? 'ALEX:' : 'YOU:'}\n                  </span>\n                  <span className=\"text-gray-300 ml-2\">\n                    {msg.content.length > 100 ? `${msg.content.substring(0, 100)}...` : msg.content}\n                  </span>\n                </div>\n              ))}\n            </div>\n          </motion.div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default UserDashboard;\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;;AAgBO,MAAM,gBAA8C,CAAC,EAC1D,WAAW,EACX,WAAW,EACX,iBAAiB,EACjB,YAAY,EAAE,EACf;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAA+B;IACrE,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iNAAQ,EAAM;IACxC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IAEvC,IAAA,kNAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,iBAAiB,2JAAkB,CAAC,iBAAiB;YAC3D,MAAM,YAAY,2JAAkB,CAAC,YAAY;YAEjD,WAAW;YACX,SAAS;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAA0B;;;;;;;;;;;IAG/C;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAkD;;;;;;kCAChE,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,MAAM,cAAc;QAClB,MAAM,OAAO,IAAI,OAAO,QAAQ;QAChC,IAAI,OAAO,IAAI,OAAO;QACtB,IAAI,OAAO,IAAI,OAAO;QACtB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,4EAA4E,EAAE,WAAW;kBACxG,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,oMAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;;sCAE5B,8OAAC;4BAAG,WAAU;;gCACX;gCAAc;gCAAG,QAAQ,IAAI;gCAAC;;;;;;;sCAEjC,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAMvC,8OAAC,oMAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,OAAO;oBAAI;8BAExB,yBACC,8OAAC,kJAAiB;wBAChB,oBAAoB,IAAA,6JAAkB,EAAC,QAAQ,WAAW,CAAC,kBAAkB,KAAK;4BAChF,IAAI;4BACJ,MAAM;4BACN,aAAa;4BACb,QAAQ;4BACR,OAAO;4BACP,eAAe;gCAAE,MAAM;gCAAK,OAAO;gCAAK,QAAQ;4BAAI;4BACpD,cAAc;4BACd,iBAAiB,EAAE;4BACnB,qBAAqB,EAAE;4BACvB,sBAAsB,EAAE;4BACxB,kBAAkB,EAAE;wBACtB;wBACA,UAAU,QAAQ,IAAI;wBACtB,aAAa;;;;;;;;;;;gBAMlB,CAAC,eAAe,iBAAiB,mBAChC,8OAAC;oBAAI,WAAU;;wBACZ,6BACC,8OAAC,oMAAM,CAAC,MAAM;4BACZ,SAAS;4BACT,WAAU;4BACV,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;4BACxB,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;;8CAEzB,8OAAC,yOAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;;;;;;;wBAMrD,mCACC,8OAAC,oMAAM,CAAC,MAAM;4BACZ,SAAS;4BACT,WAAU;4BACV,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;4BACxB,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;;8CAEzB,8OAAC,gNAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;;;;;;;;;;;;;8BAS1D,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oMAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;;8CAEzB,8OAAC,sNAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCAAI,WAAU;8CAA8C,OAAO,iBAAiB;;;;;;8CACrF,8OAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;;sCAGzC,8OAAC,oMAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;;8CAEzB,8OAAC,yOAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC;oCAAI,WAAU;8CAA+C,OAAO,sBAAsB;;;;;;8CAC3F,8OAAC;oCAAI,WAAU;8CAAyB;;;;;;;;;;;;sCAG1C,8OAAC,oMAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;;8CAEzB,8OAAC,gOAAU;oCAAC,WAAU;;;;;;8CACtB,8OAAC;oCAAI,WAAU;8CAAgD,OAAO,mBAAmB;;;;;;8CACzF,8OAAC;oCAAI,WAAU;8CAA0B;;;;;;;;;;;;sCAG3C,8OAAC,oMAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;;8CAEzB,8OAAC,gNAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCAAI,WAAU;8CAAgD,OAAO,iBAAiB;;;;;;8CACvF,8OAAC;oCAAI,WAAU;8CAA0B;;;;;;;;;;;;;;;;;;8BAK7C,8OAAC,oMAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,OAAO;oBAAI;;sCAEzB,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,0MAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAInC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAiD;;;;;;sDAC/D,8OAAC;4CAAI,WAAU;sDACZ,QAAQ,KAAK,CAAC,MAAM,GAAG,IACtB,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,oBACvB,8OAAC;oDAAc,WAAU;8DACtB,KAAK,OAAO,CAAC,KAAK,KAAK,WAAW;mDAD3B;;;;8GAKZ,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;8CAK7C,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAmD;;;;;;sDACjE,8OAAC;4CAAI,WAAU;sDACZ,QAAQ,WAAW,CAAC,MAAM,GAAG,IAC5B,QAAQ,WAAW,CAAC,GAAG,CAAC,CAAC,YAAY,oBACnC,8OAAC;oDAAc,WAAU;8DACtB,WAAW,OAAO,CAAC,KAAK,KAAK,WAAW;mDADjC;;;;8GAKZ,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;sCAM/C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,8OAAC;gDAAK,WAAU;0DACb,QAAQ,YAAY,EAAE,iBAAiB;;;;;;;;;;;;kDAG5C,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,8OAAC;gDAAK,WAAU;0DACb,QAAQ,SAAS,CAAC,kBAAkB;;;;;;;;;;;;kDAGzC,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,8OAAC;gDAAK,WAAU;0DACb,QAAQ,YAAY,CAAC,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAQjD,QAAQ,mBAAmB,CAAC,MAAM,GAAG,mBACpC,8OAAC,oMAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,OAAO;oBAAI;;sCAEzB,8OAAC;4BAAG,WAAU;sCAAiD;;;;;;sCAC/D,8OAAC;4BAAI,WAAU;sCACZ,QAAQ,mBAAmB,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,oBAC/C,8OAAC;oCAAc,WAAU;;sDACvB,8OAAC;4CAAK,WAAW,CAAC,UAAU,EAAE,IAAI,IAAI,KAAK,YAAY,kBAAkB,mBAAmB;sDACzF,IAAI,IAAI,KAAK,YAAY,UAAU;;;;;;sDAEtC,8OAAC;4CAAK,WAAU;sDACb,IAAI,OAAO,CAAC,MAAM,GAAG,MAAM,GAAG,IAAI,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,IAAI,OAAO;;;;;;;mCALzE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAe1B;uCAEe", "debugId": null}}, {"offset": {"line": 3455, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trainer%20test/ai-trainer-assessment/src/components/PersonalitySelector.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Play, Volume2 } from 'lucide-react';\nimport { TRAINER_PERSONALITIES, TrainerPersonality, getPersonalityWelcomeMessage } from '@/services/trainerPersonalities';\n\ninterface PersonalitySelectorProps {\n  onSelectPersonality: (personality: TrainerPersonality) => void;\n  userName: string;\n  className?: string;\n}\n\nexport const PersonalitySelector: React.FC<PersonalitySelectorProps> = ({\n  onSelectPersonality,\n  userName,\n  className = ''\n}) => {\n  const [selectedPersonality, setSelectedPersonality] = useState<string | null>(null);\n  const [previewingPersonality, setPreviewingPersonality] = useState<string | null>(null);\n\n  const handlePreview = async (personality: TrainerPersonality) => {\n    if (previewingPersonality === personality.id) {\n      setPreviewingPersonality(null);\n      // Stop any current speech\n      if ('speechSynthesis' in window) {\n        window.speechSynthesis.cancel();\n      }\n      return;\n    }\n\n    setPreviewingPersonality(personality.id);\n    \n    // Get a sample message\n    const sampleMessage = getPersonalityWelcomeMessage(personality, userName);\n    \n    // Use browser speech synthesis for preview\n    if ('speechSynthesis' in window) {\n      window.speechSynthesis.cancel();\n      \n      const utterance = new SpeechSynthesisUtterance(sampleMessage);\n      utterance.rate = personality.voiceSettings.rate;\n      utterance.pitch = personality.voiceSettings.pitch;\n      utterance.volume = personality.voiceSettings.volume;\n      \n      // Try to find a good voice\n      const voices = window.speechSynthesis.getVoices();\n      const preferredVoice = voices.find(voice => \n        voice.name.includes('Google') && voice.lang.startsWith('en')\n      ) || voices.find(voice => voice.lang.startsWith('en'));\n      \n      if (preferredVoice) {\n        utterance.voice = preferredVoice;\n      }\n\n      utterance.onend = () => {\n        setPreviewingPersonality(null);\n      };\n\n      window.speechSynthesis.speak(utterance);\n    }\n  };\n\n  const handleSelect = (personality: TrainerPersonality) => {\n    setSelectedPersonality(personality.id);\n    // Stop any preview\n    if ('speechSynthesis' in window) {\n      window.speechSynthesis.cancel();\n    }\n    setPreviewingPersonality(null);\n    onSelectPersonality(personality);\n  };\n\n  const getPersonalityColor = (personalityId: string) => {\n    const personality = TRAINER_PERSONALITIES.find(p => p.id === personalityId);\n    switch (personality?.color) {\n      case 'red': return 'border-red-400 bg-red-900';\n      case 'green': return 'border-green-400 bg-green-900';\n      case 'blue': return 'border-blue-400 bg-blue-900';\n      case 'purple': return 'border-purple-400 bg-purple-900';\n      case 'yellow': return 'border-yellow-400 bg-yellow-900';\n      default: return 'border-cyan-400 bg-cyan-900';\n    }\n  };\n\n  const getPersonalityTextColor = (personalityId: string) => {\n    const personality = TRAINER_PERSONALITIES.find(p => p.id === personalityId);\n    switch (personality?.color) {\n      case 'red': return 'text-red-300';\n      case 'green': return 'text-green-300';\n      case 'blue': return 'text-blue-300';\n      case 'purple': return 'text-purple-300';\n      case 'yellow': return 'text-yellow-300';\n      default: return 'text-cyan-300';\n    }\n  };\n\n  return (\n    <div className={`min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center p-4 ${className}`}>\n      <div className=\"max-w-6xl w-full\">\n        {/* Header */}\n        <motion.div\n          className=\"text-center mb-8\"\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n        >\n          <h1 className=\"text-4xl font-bold text-cyan-300 mb-4 font-mono\">\n            CHOOSE YOUR AI TRAINER\n          </h1>\n          <p className=\"text-cyan-100 text-lg mb-2\">\n            Each trainer has a unique personality and coaching style\n          </p>\n          <p className=\"text-cyan-400 text-sm\">\n            Click the preview button to hear them speak, then select your favorite!\n          </p>\n        </motion.div>\n\n        {/* Personality Grid */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {TRAINER_PERSONALITIES.map((personality, index) => (\n            <motion.div\n              key={personality.id}\n              className={`bg-black bg-opacity-50 rounded-lg border-2 p-6 backdrop-blur-sm cursor-pointer transition-all duration-300 ${\n                selectedPersonality === personality.id\n                  ? getPersonalityColor(personality.id) + ' bg-opacity-30'\n                  : 'border-gray-600 hover:border-cyan-400'\n              }`}\n              whileHover={{ scale: 1.02 }}\n              whileTap={{ scale: 0.98 }}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: index * 0.1 }}\n            >\n              {/* Personality Header */}\n              <div className=\"text-center mb-4\">\n                <div className=\"text-6xl mb-3\">{personality.avatar}</div>\n                <h3 className={`text-xl font-bold mb-2 font-mono ${getPersonalityTextColor(personality.id)}`}>\n                  {personality.name}\n                </h3>\n                <p className=\"text-gray-300 text-sm mb-4\">\n                  {personality.description}\n                </p>\n              </div>\n\n              {/* Preview Button */}\n              <div className=\"flex justify-center mb-4\">\n                <motion.button\n                  onClick={() => handlePreview(personality)}\n                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${\n                    previewingPersonality === personality.id\n                      ? 'bg-red-600 border-red-400 text-white'\n                      : `border-${personality.color}-400 text-${personality.color}-300 hover:bg-${personality.color}-900 hover:bg-opacity-30`\n                  }`}\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  disabled={previewingPersonality !== null && previewingPersonality !== personality.id}\n                >\n                  {previewingPersonality === personality.id ? (\n                    <>\n                      <Volume2 className=\"w-4 h-4\" />\n                      <span className=\"text-sm font-mono\">STOP</span>\n                    </>\n                  ) : (\n                    <>\n                      <Play className=\"w-4 h-4\" />\n                      <span className=\"text-sm font-mono\">PREVIEW</span>\n                    </>\n                  )}\n                </motion.button>\n              </div>\n\n              {/* Sample Phrases */}\n              <div className=\"space-y-2 mb-4\">\n                <div className=\"text-xs text-gray-400 font-mono\">SAMPLE PHRASES:</div>\n                {personality.motivationalPhrases.slice(0, 2).map((phrase, idx) => (\n                  <div key={idx} className=\"text-xs text-gray-300 bg-gray-800 bg-opacity-50 px-2 py-1 rounded italic\">\n                    \"{phrase}\"\n                  </div>\n                ))}\n              </div>\n\n              {/* Select Button */}\n              <motion.button\n                onClick={() => handleSelect(personality)}\n                className={`w-full py-3 px-4 rounded-lg font-bold transition-all duration-300 font-mono ${\n                  selectedPersonality === personality.id\n                    ? `bg-${personality.color}-500 text-white`\n                    : `bg-gray-700 text-white hover:bg-${personality.color}-600`\n                }`}\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                {selectedPersonality === personality.id ? 'SELECTED!' : 'SELECT THIS TRAINER'}\n              </motion.button>\n\n              {/* Personality Indicator */}\n              {previewingPersonality === personality.id && (\n                <motion.div\n                  className=\"mt-3 text-center\"\n                  initial={{ opacity: 0 }}\n                  animate={{ opacity: 1 }}\n                >\n                  <div className=\"text-xs text-yellow-300 font-mono animate-pulse\">\n                    🎤 SPEAKING...\n                  </div>\n                </motion.div>\n              )}\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Bottom Info */}\n        <motion.div\n          className=\"text-center mt-8\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.5 }}\n        >\n          <p className=\"text-cyan-100 mb-2\">\n            Don't worry - you can change your trainer personality anytime during your journey!\n          </p>\n          <div className=\"text-sm text-gray-400\">\n            Each trainer uses different voice settings and coaching approaches to match their personality\n          </div>\n        </motion.div>\n      </div>\n    </div>\n  );\n};\n\nexport default PersonalitySelector;\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAAA;AACA;AALA;;;;;;AAaO,MAAM,sBAA0D,CAAC,EACtE,mBAAmB,EACnB,QAAQ,EACR,YAAY,EAAE,EACf;IACC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,IAAA,iNAAQ,EAAgB;IAC9E,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,IAAA,iNAAQ,EAAgB;IAElF,MAAM,gBAAgB,OAAO;QAC3B,IAAI,0BAA0B,YAAY,EAAE,EAAE;YAC5C,yBAAyB;YACzB,0BAA0B;YAC1B,IAAI,qBAAqB,QAAQ;gBAC/B,OAAO,eAAe,CAAC,MAAM;YAC/B;YACA;QACF;QAEA,yBAAyB,YAAY,EAAE;QAEvC,uBAAuB;QACvB,MAAM,gBAAgB,IAAA,uKAA4B,EAAC,aAAa;QAEhE,2CAA2C;QAC3C,IAAI,qBAAqB,QAAQ;YAC/B,OAAO,eAAe,CAAC,MAAM;YAE7B,MAAM,YAAY,IAAI,yBAAyB;YAC/C,UAAU,IAAI,GAAG,YAAY,aAAa,CAAC,IAAI;YAC/C,UAAU,KAAK,GAAG,YAAY,aAAa,CAAC,KAAK;YACjD,UAAU,MAAM,GAAG,YAAY,aAAa,CAAC,MAAM;YAEnD,2BAA2B;YAC3B,MAAM,SAAS,OAAO,eAAe,CAAC,SAAS;YAC/C,MAAM,iBAAiB,OAAO,IAAI,CAAC,CAAA,QACjC,MAAM,IAAI,CAAC,QAAQ,CAAC,aAAa,MAAM,IAAI,CAAC,UAAU,CAAC,UACpD,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,IAAI,CAAC,UAAU,CAAC;YAEhD,IAAI,gBAAgB;gBAClB,UAAU,KAAK,GAAG;YACpB;YAEA,UAAU,KAAK,GAAG;gBAChB,yBAAyB;YAC3B;YAEA,OAAO,eAAe,CAAC,KAAK,CAAC;QAC/B;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,uBAAuB,YAAY,EAAE;QACrC,mBAAmB;QACnB,IAAI,qBAAqB,QAAQ;YAC/B,OAAO,eAAe,CAAC,MAAM;QAC/B;QACA,yBAAyB;QACzB,oBAAoB;IACtB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,cAAc,gKAAqB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC7D,OAAQ,aAAa;YACnB,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,MAAM,cAAc,gKAAqB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC7D,OAAQ,aAAa;YACnB,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,6GAA6G,EAAE,WAAW;kBACzI,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,oMAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;;sCAE5B,8OAAC;4BAAG,WAAU;sCAAkD;;;;;;sCAGhE,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAMvC,8OAAC;oBAAI,WAAU;8BACZ,gKAAqB,CAAC,GAAG,CAAC,CAAC,aAAa,sBACvC,8OAAC,oMAAM,CAAC,GAAG;4BAET,WAAW,CAAC,2GAA2G,EACrH,wBAAwB,YAAY,EAAE,GAClC,oBAAoB,YAAY,EAAE,IAAI,mBACtC,yCACJ;4BACF,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;4BACxB,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO,QAAQ;4BAAI;;8CAGjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAiB,YAAY,MAAM;;;;;;sDAClD,8OAAC;4CAAG,WAAW,CAAC,iCAAiC,EAAE,wBAAwB,YAAY,EAAE,GAAG;sDACzF,YAAY,IAAI;;;;;;sDAEnB,8OAAC;4CAAE,WAAU;sDACV,YAAY,WAAW;;;;;;;;;;;;8CAK5B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oMAAM,CAAC,MAAM;wCACZ,SAAS,IAAM,cAAc;wCAC7B,WAAW,CAAC,0EAA0E,EACpF,0BAA0B,YAAY,EAAE,GACpC,yCACA,CAAC,OAAO,EAAE,YAAY,KAAK,CAAC,UAAU,EAAE,YAAY,KAAK,CAAC,cAAc,EAAE,YAAY,KAAK,CAAC,wBAAwB,CAAC,EACzH;wCACF,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,UAAU,0BAA0B,QAAQ,0BAA0B,YAAY,EAAE;kDAEnF,0BAA0B,YAAY,EAAE,iBACvC;;8DACE,8OAAC,uNAAO;oDAAC,WAAU;;;;;;8DACnB,8OAAC;oDAAK,WAAU;8DAAoB;;;;;;;yEAGtC;;8DACE,8OAAC,0MAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDAAK,WAAU;8DAAoB;;;;;;;;;;;;;;;;;;8CAO5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAkC;;;;;;wCAChD,YAAY,mBAAmB,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,oBACxD,8OAAC;gDAAc,WAAU;;oDAA2E;oDAChG;oDAAO;;+CADD;;;;;;;;;;;8CAOd,8OAAC,oMAAM,CAAC,MAAM;oCACZ,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAC,4EAA4E,EACtF,wBAAwB,YAAY,EAAE,GAClC,CAAC,GAAG,EAAE,YAAY,KAAK,CAAC,eAAe,CAAC,GACxC,CAAC,gCAAgC,EAAE,YAAY,KAAK,CAAC,IAAI,CAAC,EAC9D;oCACF,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;8CAEvB,wBAAwB,YAAY,EAAE,GAAG,cAAc;;;;;;gCAIzD,0BAA0B,YAAY,EAAE,kBACvC,8OAAC,oMAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;oCAAE;8CAEtB,cAAA,8OAAC;wCAAI,WAAU;kDAAkD;;;;;;;;;;;;2BAjFhE,YAAY,EAAE;;;;;;;;;;8BA2FzB,8OAAC,oMAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,YAAY;wBAAE,OAAO;oBAAI;;sCAEzB,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC;4BAAI,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AAOjD;uCAEe", "debugId": null}}, {"offset": {"line": 3837, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trainer%20test/ai-trainer-assessment/src/services/facialAnalysisService.ts"], "sourcesContent": ["import { FacialAnalysis } from '@/components/VideoCallInterface';\n\nexport interface FacialLandmarks {\n  leftEye: { x: number; y: number };\n  rightEye: { x: number; y: number };\n  nose: { x: number; y: number };\n  mouth: { x: number; y: number };\n  jawline: { x: number; y: number }[];\n}\n\nexport interface EmotionScores {\n  happy: number;\n  sad: number;\n  angry: number;\n  surprised: number;\n  neutral: number;\n  focused: number;\n  tired: number;\n}\n\nexport class FacialAnalysisService {\n  private canvas: HTMLCanvasElement | null = null;\n  private context: CanvasRenderingContext2D | null = null;\n  private isInitialized: boolean = false;\n  private analysisInterval: number | null = null;\n\n  constructor() {\n    // Initialize canvas only in browser environment\n    if (typeof window !== 'undefined' && typeof document !== 'undefined') {\n      this.canvas = document.createElement('canvas');\n      this.context = this.canvas.getContext('2d');\n    }\n  }\n\n  // Initialize the facial analysis system\n  async initialize(): Promise<boolean> {\n    try {\n      // Check if we're in browser environment\n      if (typeof window === 'undefined' || typeof document === 'undefined') {\n        console.warn('Facial analysis not available in server environment');\n        return false;\n      }\n\n      // Initialize canvas if not already done\n      if (!this.canvas) {\n        this.canvas = document.createElement('canvas');\n        this.context = this.canvas.getContext('2d');\n      }\n\n      if (!this.context) {\n        console.error('Failed to get canvas context');\n        return false;\n      }\n\n      // In a real implementation, you would load ML models here\n      // For now, we'll simulate the initialization\n      await this.loadModels();\n      this.isInitialized = true;\n      return true;\n    } catch (error) {\n      console.error('Failed to initialize facial analysis:', error);\n      return false;\n    }\n  }\n\n  // Simulate loading ML models (in real implementation, load MediaPipe, Face-API.js, etc.)\n  private async loadModels(): Promise<void> {\n    return new Promise((resolve) => {\n      // Simulate model loading time\n      setTimeout(resolve, 1000);\n    });\n  }\n\n  // Start continuous facial analysis\n  startAnalysis(\n    videoElement: HTMLVideoElement,\n    onAnalysis: (analysis: FacialAnalysis) => void,\n    intervalMs: number = 3000\n  ): void {\n    if (!this.isInitialized) {\n      console.error('Facial analysis service not initialized');\n      return;\n    }\n\n    this.stopAnalysis(); // Stop any existing analysis\n\n    this.analysisInterval = window.setInterval(() => {\n      const analysis = this.analyzeFrame(videoElement);\n      if (analysis) {\n        onAnalysis(analysis);\n      }\n    }, intervalMs);\n  }\n\n  // Stop facial analysis\n  stopAnalysis(): void {\n    if (this.analysisInterval) {\n      clearInterval(this.analysisInterval);\n      this.analysisInterval = null;\n    }\n  }\n\n  // Analyze a single frame from the video\n  private analyzeFrame(videoElement: HTMLVideoElement): FacialAnalysis | null {\n    try {\n      if (!this.canvas || !this.context) {\n        console.error('Canvas not initialized');\n        return null;\n      }\n\n      // Set canvas size to match video\n      this.canvas.width = videoElement.videoWidth;\n      this.canvas.height = videoElement.videoHeight;\n\n      // Draw current video frame to canvas\n      this.context.drawImage(videoElement, 0, 0);\n\n      // Get image data for analysis\n      const imageData = this.context.getImageData(0, 0, this.canvas.width, this.canvas.height);\n\n      // Perform facial analysis (simulated for now)\n      return this.performAnalysis(imageData);\n\n    } catch (error) {\n      console.error('Error analyzing frame:', error);\n      return null;\n    }\n  }\n\n  // Perform the actual facial analysis\n  private performAnalysis(imageData: ImageData): FacialAnalysis {\n    // In a real implementation, this would use ML models to analyze the image\n    // For now, we'll generate realistic-looking mock data with some intelligence\n\n    const brightness = this.calculateBrightness(imageData);\n    const movement = this.detectMovement(imageData);\n    \n    // Generate more realistic analysis based on image characteristics\n    const baseEngagement = Math.max(20, Math.min(95, 60 + (brightness - 128) * 0.3 + movement * 10));\n    const baseFatigue = Math.max(5, Math.min(80, 30 - (brightness - 128) * 0.2 + Math.random() * 20));\n    const baseStress = Math.max(5, Math.min(70, 25 + Math.random() * 15));\n\n    // Determine primary emotion based on analysis\n    const emotions: (keyof EmotionScores)[] = ['happy', 'neutral', 'focused', 'tired', 'surprised'];\n    const primaryEmotion = emotions[Math.floor(Math.random() * emotions.length)];\n\n    // Adjust metrics based on detected emotion\n    let engagement = baseEngagement;\n    let fatigue = baseFatigue;\n    let stress = baseStress;\n\n    switch (primaryEmotion) {\n      case 'happy':\n        engagement += 15;\n        fatigue -= 10;\n        stress -= 10;\n        break;\n      case 'focused':\n        engagement += 20;\n        fatigue += 5;\n        stress -= 5;\n        break;\n      case 'tired':\n        engagement -= 20;\n        fatigue += 25;\n        stress += 10;\n        break;\n      case 'surprised':\n        engagement += 10;\n        stress += 5;\n        break;\n    }\n\n    // Clamp values to valid ranges\n    engagement = Math.max(0, Math.min(100, engagement));\n    fatigue = Math.max(0, Math.min(100, fatigue));\n    stress = Math.max(0, Math.min(100, stress));\n\n    return {\n      emotion: primaryEmotion as FacialAnalysis['emotion'],\n      confidence: 0.7 + Math.random() * 0.25, // 70-95% confidence\n      engagement,\n      fatigue,\n      stress,\n      timestamp: new Date()\n    };\n  }\n\n  // Calculate average brightness of the image\n  private calculateBrightness(imageData: ImageData): number {\n    const data = imageData.data;\n    let totalBrightness = 0;\n    let pixelCount = 0;\n\n    for (let i = 0; i < data.length; i += 4) {\n      const r = data[i];\n      const g = data[i + 1];\n      const b = data[i + 2];\n      \n      // Calculate luminance\n      const brightness = 0.299 * r + 0.587 * g + 0.114 * b;\n      totalBrightness += brightness;\n      pixelCount++;\n    }\n\n    return totalBrightness / pixelCount;\n  }\n\n  // Detect movement/changes in the image (simplified)\n  private detectMovement(imageData: ImageData): number {\n    // In a real implementation, this would compare with previous frames\n    // For now, we'll use image variance as a proxy for movement\n    const data = imageData.data;\n    let variance = 0;\n    let mean = 0;\n    let pixelCount = 0;\n\n    // Calculate mean\n    for (let i = 0; i < data.length; i += 4) {\n      const gray = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];\n      mean += gray;\n      pixelCount++;\n    }\n    mean /= pixelCount;\n\n    // Calculate variance\n    for (let i = 0; i < data.length; i += 4) {\n      const gray = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];\n      variance += Math.pow(gray - mean, 2);\n    }\n    variance /= pixelCount;\n\n    // Normalize variance to 0-1 range (higher variance suggests more movement/detail)\n    return Math.min(1, variance / 10000);\n  }\n\n  // Get detailed emotion analysis\n  getEmotionScores(imageData: ImageData): EmotionScores {\n    // In a real implementation, this would use emotion detection models\n    const brightness = this.calculateBrightness(imageData);\n    const movement = this.detectMovement(imageData);\n\n    // Generate realistic emotion scores\n    const baseHappy = Math.max(0, Math.min(1, 0.3 + (brightness - 128) * 0.002));\n    const baseNeutral = 0.4 + Math.random() * 0.2;\n    const baseFocused = Math.max(0, Math.min(1, 0.2 + movement * 0.5));\n    const baseTired = Math.max(0, Math.min(1, 0.1 + (128 - brightness) * 0.001));\n\n    return {\n      happy: baseHappy,\n      sad: Math.max(0, 0.1 - baseHappy * 0.5),\n      angry: Math.random() * 0.1,\n      surprised: Math.random() * 0.15,\n      neutral: baseNeutral,\n      focused: baseFocused,\n      tired: baseTired\n    };\n  }\n\n  // Cleanup resources\n  dispose(): void {\n    this.stopAnalysis();\n    this.isInitialized = false;\n  }\n\n  // Check if service is ready\n  isReady(): boolean {\n    return this.isInitialized;\n  }\n}\n\n// Singleton instance - only create in browser environment\nexport const facialAnalysisService = typeof window !== 'undefined' ? new FacialAnalysisService() : null;\n\n// Utility functions for facial analysis insights\nexport const getFitnessInsights = (analysis: FacialAnalysis): string[] => {\n  const insights: string[] = [];\n\n  if (analysis.fatigue > 70) {\n    insights.push(\"High fatigue detected - consider a lighter workout today\");\n  } else if (analysis.fatigue < 30) {\n    insights.push(\"Great energy levels - ready for an intense session!\");\n  }\n\n  if (analysis.engagement > 80) {\n    insights.push(\"Excellent focus and engagement\");\n  } else if (analysis.engagement < 40) {\n    insights.push(\"Let's work on building motivation and engagement\");\n  }\n\n  if (analysis.stress > 60) {\n    insights.push(\"Elevated stress levels - let's include some relaxation techniques\");\n  }\n\n  if (analysis.emotion === 'happy') {\n    insights.push(\"Positive mood detected - perfect for challenging workouts!\");\n  } else if (analysis.emotion === 'tired') {\n    insights.push(\"Showing signs of tiredness - let's focus on gentle movement\");\n  }\n\n  return insights;\n};\n\nexport const getPersonalizedRecommendations = (analysis: FacialAnalysis): string[] => {\n  const recommendations: string[] = [];\n\n  const energyLevel = 100 - analysis.fatigue;\n  const stressLevel = analysis.stress;\n\n  if (energyLevel > 70 && stressLevel < 30) {\n    recommendations.push(\"High-intensity interval training\");\n    recommendations.push(\"Strength training with compound movements\");\n  } else if (energyLevel > 50 && stressLevel < 50) {\n    recommendations.push(\"Moderate cardio workout\");\n    recommendations.push(\"Bodyweight exercises\");\n  } else {\n    recommendations.push(\"Gentle yoga or stretching\");\n    recommendations.push(\"Walking or light movement\");\n    recommendations.push(\"Breathing exercises\");\n  }\n\n  return recommendations;\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAoBO,MAAM;IACH,SAAmC,KAAK;IACxC,UAA2C,KAAK;IAChD,gBAAyB,MAAM;IAC/B,mBAAkC,KAAK;IAE/C,aAAc;QACZ,gDAAgD;QAChD;;IAIF;IAEA,wCAAwC;IACxC,MAAM,aAA+B;QACnC,IAAI;YACF,wCAAwC;YACxC,wCAAsE;gBACpE,QAAQ,IAAI,CAAC;gBACb,OAAO;YACT;;;QAkBF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;QACT;IACF;IAEA,yFAAyF;IACzF,MAAc,aAA4B;QACxC,OAAO,IAAI,QAAQ,CAAC;YAClB,8BAA8B;YAC9B,WAAW,SAAS;QACtB;IACF;IAEA,mCAAmC;IACnC,cACE,YAA8B,EAC9B,UAA8C,EAC9C,aAAqB,IAAI,EACnB;QACN,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvB,QAAQ,KAAK,CAAC;YACd;QACF;QAEA,IAAI,CAAC,YAAY,IAAI,6BAA6B;QAElD,IAAI,CAAC,gBAAgB,GAAG,OAAO,WAAW,CAAC;YACzC,MAAM,WAAW,IAAI,CAAC,YAAY,CAAC;YACnC,IAAI,UAAU;gBACZ,WAAW;YACb;QACF,GAAG;IACL;IAEA,uBAAuB;IACvB,eAAqB;QACnB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,cAAc,IAAI,CAAC,gBAAgB;YACnC,IAAI,CAAC,gBAAgB,GAAG;QAC1B;IACF;IAEA,wCAAwC;IAChC,aAAa,YAA8B,EAAyB;QAC1E,IAAI;YACF,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACjC,QAAQ,KAAK,CAAC;gBACd,OAAO;YACT;YAEA,iCAAiC;YACjC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,aAAa,UAAU;YAC3C,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,aAAa,WAAW;YAE7C,qCAAqC;YACrC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,cAAc,GAAG;YAExC,8BAA8B;YAC9B,MAAM,YAAY,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM;YAEvF,8CAA8C;YAC9C,OAAO,IAAI,CAAC,eAAe,CAAC;QAE9B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;QACT;IACF;IAEA,qCAAqC;IAC7B,gBAAgB,SAAoB,EAAkB;QAC5D,0EAA0E;QAC1E,6EAA6E;QAE7E,MAAM,aAAa,IAAI,CAAC,mBAAmB,CAAC;QAC5C,MAAM,WAAW,IAAI,CAAC,cAAc,CAAC;QAErC,kEAAkE;QAClE,MAAM,iBAAiB,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,CAAC,aAAa,GAAG,IAAI,MAAM,WAAW;QAC5F,MAAM,cAAc,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,KAAK,CAAC,aAAa,GAAG,IAAI,MAAM,KAAK,MAAM,KAAK;QAC7F,MAAM,aAAa,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,MAAM,KAAK;QAEjE,8CAA8C;QAC9C,MAAM,WAAoC;YAAC;YAAS;YAAW;YAAW;YAAS;SAAY;QAC/F,MAAM,iBAAiB,QAAQ,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS,MAAM,EAAE;QAE5E,2CAA2C;QAC3C,IAAI,aAAa;QACjB,IAAI,UAAU;QACd,IAAI,SAAS;QAEb,OAAQ;YACN,KAAK;gBACH,cAAc;gBACd,WAAW;gBACX,UAAU;gBACV;YACF,KAAK;gBACH,cAAc;gBACd,WAAW;gBACX,UAAU;gBACV;YACF,KAAK;gBACH,cAAc;gBACd,WAAW;gBACX,UAAU;gBACV;YACF,KAAK;gBACH,cAAc;gBACd,UAAU;gBACV;QACJ;QAEA,+BAA+B;QAC/B,aAAa,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK;QACvC,UAAU,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK;QACpC,SAAS,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK;QAEnC,OAAO;YACL,SAAS;YACT,YAAY,MAAM,KAAK,MAAM,KAAK;YAClC;YACA;YACA;YACA,WAAW,IAAI;QACjB;IACF;IAEA,4CAA4C;IACpC,oBAAoB,SAAoB,EAAU;QACxD,MAAM,OAAO,UAAU,IAAI;QAC3B,IAAI,kBAAkB;QACtB,IAAI,aAAa;QAEjB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,KAAK,EAAG;YACvC,MAAM,IAAI,IAAI,CAAC,EAAE;YACjB,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE;YACrB,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE;YAErB,sBAAsB;YACtB,MAAM,aAAa,QAAQ,IAAI,QAAQ,IAAI,QAAQ;YACnD,mBAAmB;YACnB;QACF;QAEA,OAAO,kBAAkB;IAC3B;IAEA,oDAAoD;IAC5C,eAAe,SAAoB,EAAU;QACnD,oEAAoE;QACpE,4DAA4D;QAC5D,MAAM,OAAO,UAAU,IAAI;QAC3B,IAAI,WAAW;QACf,IAAI,OAAO;QACX,IAAI,aAAa;QAEjB,iBAAiB;QACjB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,KAAK,EAAG;YACvC,MAAM,OAAO,QAAQ,IAAI,CAAC,EAAE,GAAG,QAAQ,IAAI,CAAC,IAAI,EAAE,GAAG,QAAQ,IAAI,CAAC,IAAI,EAAE;YACxE,QAAQ;YACR;QACF;QACA,QAAQ;QAER,qBAAqB;QACrB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,KAAK,EAAG;YACvC,MAAM,OAAO,QAAQ,IAAI,CAAC,EAAE,GAAG,QAAQ,IAAI,CAAC,IAAI,EAAE,GAAG,QAAQ,IAAI,CAAC,IAAI,EAAE;YACxE,YAAY,KAAK,GAAG,CAAC,OAAO,MAAM;QACpC;QACA,YAAY;QAEZ,kFAAkF;QAClF,OAAO,KAAK,GAAG,CAAC,GAAG,WAAW;IAChC;IAEA,gCAAgC;IAChC,iBAAiB,SAAoB,EAAiB;QACpD,oEAAoE;QACpE,MAAM,aAAa,IAAI,CAAC,mBAAmB,CAAC;QAC5C,MAAM,WAAW,IAAI,CAAC,cAAc,CAAC;QAErC,oCAAoC;QACpC,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,CAAC,aAAa,GAAG,IAAI;QACrE,MAAM,cAAc,MAAM,KAAK,MAAM,KAAK;QAC1C,MAAM,cAAc,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,WAAW;QAC7D,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,UAAU,IAAI;QAErE,OAAO;YACL,OAAO;YACP,KAAK,KAAK,GAAG,CAAC,GAAG,MAAM,YAAY;YACnC,OAAO,KAAK,MAAM,KAAK;YACvB,WAAW,KAAK,MAAM,KAAK;YAC3B,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,oBAAoB;IACpB,UAAgB;QACd,IAAI,CAAC,YAAY;QACjB,IAAI,CAAC,aAAa,GAAG;IACvB;IAEA,4BAA4B;IAC5B,UAAmB;QACjB,OAAO,IAAI,CAAC,aAAa;IAC3B;AACF;AAGO,MAAM,wBAAwB,sCAAgC,0BAA8B;AAG5F,MAAM,qBAAqB,CAAC;IACjC,MAAM,WAAqB,EAAE;IAE7B,IAAI,SAAS,OAAO,GAAG,IAAI;QACzB,SAAS,IAAI,CAAC;IAChB,OAAO,IAAI,SAAS,OAAO,GAAG,IAAI;QAChC,SAAS,IAAI,CAAC;IAChB;IAEA,IAAI,SAAS,UAAU,GAAG,IAAI;QAC5B,SAAS,IAAI,CAAC;IAChB,OAAO,IAAI,SAAS,UAAU,GAAG,IAAI;QACnC,SAAS,IAAI,CAAC;IAChB;IAEA,IAAI,SAAS,MAAM,GAAG,IAAI;QACxB,SAAS,IAAI,CAAC;IAChB;IAEA,IAAI,SAAS,OAAO,KAAK,SAAS;QAChC,SAAS,IAAI,CAAC;IAChB,OAAO,IAAI,SAAS,OAAO,KAAK,SAAS;QACvC,SAAS,IAAI,CAAC;IAChB;IAEA,OAAO;AACT;AAEO,MAAM,iCAAiC,CAAC;IAC7C,MAAM,kBAA4B,EAAE;IAEpC,MAAM,cAAc,MAAM,SAAS,OAAO;IAC1C,MAAM,cAAc,SAAS,MAAM;IAEnC,IAAI,cAAc,MAAM,cAAc,IAAI;QACxC,gBAAgB,IAAI,CAAC;QACrB,gBAAgB,IAAI,CAAC;IACvB,OAAO,IAAI,cAAc,MAAM,cAAc,IAAI;QAC/C,gBAAgB,IAAI,CAAC;QACrB,gBAAgB,IAAI,CAAC;IACvB,OAAO;QACL,gBAAgB,IAAI,CAAC;QACrB,gBAAgB,IAAI,CAAC;QACrB,gBAAgB,IAAI,CAAC;IACvB;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 4092, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trainer%20test/ai-trainer-assessment/src/components/VideoCallInterface.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { \n  Video, \n  VideoOff, \n  Mic, \n  MicOff, \n  Phone, \n  PhoneOff, \n  Settings,\n  Eye,\n  EyeOff,\n  Activity,\n  Heart,\n  Brain\n} from 'lucide-react';\nimport { TrainerPersonality } from '@/services/trainerPersonalities';\nimport { facialAnalysisService, getFitnessInsights } from '@/services/facialAnalysisService';\n\ninterface VideoCallInterfaceProps {\n  trainerPersonality: TrainerPersonality;\n  userName: string;\n  onEndCall: () => void;\n  onFacialAnalysis?: (analysis: FacialAnalysis) => void;\n  className?: string;\n}\n\nexport interface FacialAnalysis {\n  emotion: 'happy' | 'sad' | 'angry' | 'surprised' | 'neutral' | 'focused' | 'tired';\n  confidence: number;\n  engagement: number; // 0-100\n  fatigue: number; // 0-100\n  stress: number; // 0-100\n  timestamp: Date;\n}\n\nexport const VideoCallInterface: React.FC<VideoCallInterfaceProps> = ({\n  trainerPersonality,\n  userName,\n  onEndCall,\n  onFacialAnalysis,\n  className = ''\n}) => {\n  // Call state\n  const [isConnecting, setIsConnecting] = useState(true);\n  const [isConnected, setIsConnected] = useState(false);\n  const [callDuration, setCallDuration] = useState(0);\n  \n  // Media state\n  const [isVideoEnabled, setIsVideoEnabled] = useState(true);\n  const [isAudioEnabled, setIsAudioEnabled] = useState(true);\n  const [cameraPermission, setCameraPermission] = useState<'granted' | 'denied' | 'prompt'>('prompt');\n  \n  // Analysis state\n  const [facialAnalysis, setFacialAnalysis] = useState<FacialAnalysis | null>(null);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [privacyMode, setPrivacyMode] = useState(false);\n  \n  // Refs\n  const videoRef = useRef<HTMLVideoElement>(null);\n  const streamRef = useRef<MediaStream | null>(null);\n  const callStartTime = useRef<Date>(new Date());\n\n  // Initialize video call\n  useEffect(() => {\n    initializeCall();\n    return () => {\n      cleanup();\n    };\n  }, []);\n\n  // Call duration timer\n  useEffect(() => {\n    if (isConnected) {\n      const interval = setInterval(() => {\n        setCallDuration(Math.floor((Date.now() - callStartTime.current.getTime()) / 1000));\n      }, 1000);\n      return () => clearInterval(interval);\n    }\n  }, [isConnected]);\n\n  const initializeCall = async () => {\n    try {\n      setIsConnecting(true);\n      \n      // Simulate connection delay for realism\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      // Request camera permission\n      if (isVideoEnabled) {\n        await requestCameraAccess();\n      }\n      \n      setIsConnecting(false);\n      setIsConnected(true);\n      callStartTime.current = new Date();\n      \n    } catch (error) {\n      console.error('Failed to initialize call:', error);\n      setIsConnecting(false);\n    }\n  };\n\n  const requestCameraAccess = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({ \n        video: true, \n        audio: isAudioEnabled \n      });\n      \n      streamRef.current = stream;\n      setCameraPermission('granted');\n      \n      if (videoRef.current) {\n        videoRef.current.srcObject = stream;\n      }\n      \n      // Start facial analysis\n      if (!privacyMode) {\n        startFacialAnalysis();\n      }\n      \n    } catch (error) {\n      console.error('Camera access denied:', error);\n      setCameraPermission('denied');\n      setIsVideoEnabled(false);\n    }\n  };\n\n  const startFacialAnalysis = async () => {\n    if (!videoRef.current || privacyMode || !facialAnalysisService) return;\n\n    try {\n      // Initialize facial analysis service\n      const initialized = await facialAnalysisService.initialize();\n      if (!initialized) {\n        console.warn('Facial analysis service failed to initialize');\n        return;\n      }\n\n      setIsAnalyzing(true);\n\n      // Start real-time facial analysis\n      facialAnalysisService.startAnalysis(\n        videoRef.current,\n        (analysis: FacialAnalysis) => {\n          if (!privacyMode && isVideoEnabled) {\n            setFacialAnalysis(analysis);\n            onFacialAnalysis?.(analysis);\n\n            // Log insights for debugging\n            const insights = getFitnessInsights(analysis);\n            if (insights.length > 0) {\n              console.log('Fitness insights:', insights);\n            }\n          }\n        },\n        3000 // Analyze every 3 seconds\n      );\n\n    } catch (error) {\n      console.error('Failed to start facial analysis:', error);\n      setIsAnalyzing(false);\n    }\n  };\n\n  const toggleVideo = async () => {\n    if (isVideoEnabled) {\n      // Turn off video\n      if (streamRef.current) {\n        streamRef.current.getVideoTracks().forEach(track => track.stop());\n      }\n      setIsVideoEnabled(false);\n      setIsAnalyzing(false);\n    } else {\n      // Turn on video\n      try {\n        await requestCameraAccess();\n        setIsVideoEnabled(true);\n      } catch (error) {\n        console.error('Failed to enable video:', error);\n      }\n    }\n  };\n\n  const toggleAudio = () => {\n    if (streamRef.current) {\n      streamRef.current.getAudioTracks().forEach(track => {\n        track.enabled = !isAudioEnabled;\n      });\n    }\n    setIsAudioEnabled(!isAudioEnabled);\n  };\n\n  const togglePrivacyMode = () => {\n    const newPrivacyMode = !privacyMode;\n    setPrivacyMode(newPrivacyMode);\n\n    if (newPrivacyMode) {\n      // Entering privacy mode - stop analysis\n      if (facialAnalysisService) {\n        facialAnalysisService.stopAnalysis();\n      }\n      setIsAnalyzing(false);\n      setFacialAnalysis(null);\n    } else if (isVideoEnabled && videoRef.current) {\n      // Exiting privacy mode - restart analysis\n      startFacialAnalysis();\n    }\n  };\n\n  const endCall = () => {\n    cleanup();\n    onEndCall();\n  };\n\n  const cleanup = () => {\n    if (streamRef.current) {\n      streamRef.current.getTracks().forEach(track => track.stop());\n    }\n\n    // Stop facial analysis\n    if (facialAnalysisService) {\n      facialAnalysisService.stopAnalysis();\n    }\n    setIsAnalyzing(false);\n    setFacialAnalysis(null);\n  };\n\n  const formatDuration = (seconds: number) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  const getPersonalityColor = () => {\n    // Use scarlet red accent color for all personalities\n    return 'border-red-600 bg-red-900';\n  };\n\n  return (\n    <div className={`min-h-screen bg-black ${className}`}>\n      {/* Connection Status */}\n      <AnimatePresence>\n        {isConnecting && (\n          <motion.div\n            className=\"fixed inset-0 bg-black flex items-center justify-center z-50\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n          >\n            <div className=\"text-center\">\n              <div className=\"w-20 h-20 bg-white bg-opacity-10 rounded-full flex items-center justify-center mb-6\">\n                <div className=\"text-3xl\">{trainerPersonality.avatar}</div>\n              </div>\n              <h2 className=\"text-xl font-medium text-white mb-2\">\n                Connecting to {trainerPersonality.name}\n              </h2>\n              <div className=\"flex items-center justify-center space-x-1\">\n                <div className=\"w-2 h-2 bg-white bg-opacity-60 rounded-full animate-pulse\"></div>\n                <div className=\"w-2 h-2 bg-white bg-opacity-60 rounded-full animate-pulse\" style={{ animationDelay: '0.2s' }}></div>\n                <div className=\"w-2 h-2 bg-white bg-opacity-60 rounded-full animate-pulse\" style={{ animationDelay: '0.4s' }}></div>\n              </div>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Main Video Call Interface */}\n      {isConnected && (\n        <div className=\"flex flex-col h-screen\">\n          {/* Header */}\n          <div className=\"bg-black border-b border-white border-opacity-20 px-6 py-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-10 h-10 bg-red-600 bg-opacity-20 border border-red-600 border-opacity-40 rounded-full flex items-center justify-center\">\n                  <span className=\"text-lg\">{trainerPersonality.avatar}</span>\n                </div>\n                <div>\n                  <h2 className=\"text-lg font-semibold text-white\">\n                    {trainerPersonality.name}\n                  </h2>\n                  <p className=\"text-sm text-white text-opacity-60\">Personal Trainer</p>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"text-white text-opacity-80 text-sm font-medium\">\n                  {formatDuration(callDuration)}\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"w-2 h-2 bg-red-600 rounded-full animate-pulse\"></div>\n                  <span className=\"text-red-600 text-sm font-medium\">Live</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Video Area */}\n          <div className=\"flex-1 flex\">\n            {/* Trainer Area */}\n            <div className=\"flex-1 relative bg-gray-900\">\n              <div className=\"absolute inset-0 flex items-center justify-center\">\n                <motion.div\n                  className=\"w-80 h-80 bg-white bg-opacity-5 rounded-3xl flex items-center justify-center backdrop-blur-sm border border-white border-opacity-10\"\n                  animate={{\n                    scale: [1, 1.02, 1],\n                  }}\n                  transition={{ duration: 3, repeat: Infinity, ease: \"easeInOut\" }}\n                >\n                  <div className=\"text-8xl opacity-90\">{trainerPersonality.avatar}</div>\n                </motion.div>\n              </div>\n\n              {/* Trainer Status */}\n              <div className=\"absolute top-6 left-6\">\n                <div className=\"bg-black bg-opacity-40 backdrop-blur-xl rounded-2xl px-4 py-2 border border-white border-opacity-10\">\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                    <span className=\"text-white text-sm font-medium\">Active</span>\n                  </div>\n                  {isAnalyzing && (\n                    <div className=\"flex items-center space-x-2 mt-1\">\n                      <Brain className=\"w-3 h-3 text-blue-400 animate-pulse\" />\n                      <span className=\"text-blue-400 text-xs font-medium\">Analyzing</span>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n\n            {/* User Video Area */}\n            <div className=\"w-80 bg-gray-800 relative\">\n              {isVideoEnabled && cameraPermission === 'granted' ? (\n                <video\n                  ref={videoRef}\n                  autoPlay\n                  muted\n                  playsInline\n                  className=\"w-full h-full object-cover\"\n                />\n              ) : (\n                <div className=\"w-full h-full flex items-center justify-center bg-gray-800\">\n                  <div className=\"text-center\">\n                    <div className=\"w-16 h-16 bg-white bg-opacity-10 rounded-full flex items-center justify-center mb-4\">\n                      <VideoOff className=\"w-8 h-8 text-white text-opacity-60\" />\n                    </div>\n                    <p className=\"text-white text-opacity-60 text-sm font-medium\">\n                      {cameraPermission === 'denied' ? 'Camera Access Denied' : 'Video Off'}\n                    </p>\n                  </div>\n                </div>\n              )}\n\n              {/* User Status Overlay */}\n              <div className=\"absolute top-4 right-4\">\n                <div className=\"bg-black bg-opacity-40 backdrop-blur-xl rounded-xl px-3 py-1 border border-white border-opacity-10\">\n                  <div className=\"text-white text-sm font-medium\">{userName}</div>\n                </div>\n              </div>\n\n              {/* Privacy Mode Indicator */}\n              {privacyMode && (\n                <div className=\"absolute bottom-4 right-4\">\n                  <div className=\"bg-red-500 bg-opacity-90 backdrop-blur-xl rounded-xl px-3 py-2 border border-red-400 border-opacity-30\">\n                    <div className=\"flex items-center space-x-2 text-white\">\n                      <EyeOff className=\"w-4 h-4\" />\n                      <span className=\"text-xs font-medium\">Private</span>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Facial Analysis Panel */}\n          {facialAnalysis && !privacyMode && (\n            <motion.div\n              className=\"absolute right-6 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-40 backdrop-blur-xl rounded-2xl p-4 border border-white border-opacity-10\"\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n            >\n              <h3 className=\"text-white font-semibold text-sm mb-3\">Biometric Analysis</h3>\n              <div className=\"space-y-3 text-sm\">\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-white text-opacity-70\">Emotion</span>\n                  <span className=\"text-blue-400 font-medium capitalize\">{facialAnalysis.emotion}</span>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-white text-opacity-70\">Engagement</span>\n                  <span className=\"text-green-400 font-medium\">{Math.round(facialAnalysis.engagement)}%</span>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-white text-opacity-70\">Energy</span>\n                  <span className=\"text-yellow-400 font-medium\">{Math.round(100 - facialAnalysis.fatigue)}%</span>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-white text-opacity-70\">Focus</span>\n                  <span className=\"text-purple-400 font-medium\">{Math.round(100 - facialAnalysis.stress)}%</span>\n                </div>\n              </div>\n            </motion.div>\n          )}\n\n          {/* Control Panel */}\n          <div className=\"bg-black bg-opacity-80 backdrop-blur-xl border-t border-white border-opacity-10 px-6 py-6\">\n            <div className=\"flex items-center justify-center space-x-4\">\n              {/* Video Toggle */}\n              <motion.button\n                onClick={toggleVideo}\n                className={`w-14 h-14 rounded-full flex items-center justify-center transition-all ${\n                  isVideoEnabled\n                    ? 'bg-white bg-opacity-10 text-white'\n                    : 'bg-red-500 text-white'\n                }`}\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                {isVideoEnabled ? <Video className=\"w-6 h-6\" /> : <VideoOff className=\"w-6 h-6\" />}\n              </motion.button>\n\n              {/* Audio Toggle */}\n              <motion.button\n                onClick={toggleAudio}\n                className={`w-14 h-14 rounded-full flex items-center justify-center transition-all ${\n                  isAudioEnabled\n                    ? 'bg-white bg-opacity-10 text-white'\n                    : 'bg-red-500 text-white'\n                }`}\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                {isAudioEnabled ? <Mic className=\"w-6 h-6\" /> : <MicOff className=\"w-6 h-6\" />}\n              </motion.button>\n\n              {/* Privacy Toggle */}\n              <motion.button\n                onClick={togglePrivacyMode}\n                className={`w-14 h-14 rounded-full flex items-center justify-center transition-all ${\n                  privacyMode\n                    ? 'bg-orange-500 text-white'\n                    : 'bg-white bg-opacity-10 text-white'\n                }`}\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                title=\"Toggle facial analysis privacy\"\n              >\n                {privacyMode ? <EyeOff className=\"w-6 h-6\" /> : <Eye className=\"w-6 h-6\" />}\n              </motion.button>\n\n              {/* End Call */}\n              <motion.button\n                onClick={endCall}\n                className=\"w-16 h-16 rounded-full bg-red-500 text-white flex items-center justify-center ml-4\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                <PhoneOff className=\"w-7 h-7\" />\n              </motion.button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default VideoCallInterface;\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AAnBA;;;;;;AAsCO,MAAM,qBAAwD,CAAC,EACpE,kBAAkB,EAClB,QAAQ,EACR,SAAS,EACT,gBAAgB,EAChB,YAAY,EAAE,EACf;IACC,aAAa;IACb,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAC;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAC;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAC;IAEjD,cAAc;IACd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,iNAAQ,EAAC;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,iNAAQ,EAAC;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,iNAAQ,EAAkC;IAE1F,iBAAiB;IACjB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,iNAAQ,EAAwB;IAC5E,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAC;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAC;IAE/C,OAAO;IACP,MAAM,WAAW,IAAA,+MAAM,EAAmB;IAC1C,MAAM,YAAY,IAAA,+MAAM,EAAqB;IAC7C,MAAM,gBAAgB,IAAA,+MAAM,EAAO,IAAI;IAEvC,wBAAwB;IACxB,IAAA,kNAAS,EAAC;QACR;QACA,OAAO;YACL;QACF;IACF,GAAG,EAAE;IAEL,sBAAsB;IACtB,IAAA,kNAAS,EAAC;QACR,IAAI,aAAa;YACf,MAAM,WAAW,YAAY;gBAC3B,gBAAgB,KAAK,KAAK,CAAC,CAAC,KAAK,GAAG,KAAK,cAAc,OAAO,CAAC,OAAO,EAAE,IAAI;YAC9E,GAAG;YACH,OAAO,IAAM,cAAc;QAC7B;IACF,GAAG;QAAC;KAAY;IAEhB,MAAM,iBAAiB;QACrB,IAAI;YACF,gBAAgB;YAEhB,wCAAwC;YACxC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,4BAA4B;YAC5B,IAAI,gBAAgB;gBAClB,MAAM;YACR;YAEA,gBAAgB;YAChB,eAAe;YACf,cAAc,OAAO,GAAG,IAAI;QAE9B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,gBAAgB;QAClB;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI;YACF,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBACvD,OAAO;gBACP,OAAO;YACT;YAEA,UAAU,OAAO,GAAG;YACpB,oBAAoB;YAEpB,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,SAAS,GAAG;YAC/B;YAEA,wBAAwB;YACxB,IAAI,CAAC,aAAa;gBAChB;YACF;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,oBAAoB;YACpB,kBAAkB;QACpB;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,SAAS,OAAO,IAAI,eAAe,CAAC,iKAAqB,EAAE;QAEhE,IAAI;YACF,qCAAqC;YACrC,MAAM,cAAc,MAAM,iKAAqB,CAAC,UAAU;YAC1D,IAAI,CAAC,aAAa;gBAChB,QAAQ,IAAI,CAAC;gBACb;YACF;YAEA,eAAe;YAEf,kCAAkC;YAClC,iKAAqB,CAAC,aAAa,CACjC,SAAS,OAAO,EAChB,CAAC;gBACC,IAAI,CAAC,eAAe,gBAAgB;oBAClC,kBAAkB;oBAClB,mBAAmB;oBAEnB,6BAA6B;oBAC7B,MAAM,WAAW,IAAA,8JAAkB,EAAC;oBACpC,IAAI,SAAS,MAAM,GAAG,GAAG;wBACvB,QAAQ,GAAG,CAAC,qBAAqB;oBACnC;gBACF;YACF,GACA,KAAK,0BAA0B;;QAGnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,eAAe;QACjB;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,gBAAgB;YAClB,iBAAiB;YACjB,IAAI,UAAU,OAAO,EAAE;gBACrB,UAAU,OAAO,CAAC,cAAc,GAAG,OAAO,CAAC,CAAA,QAAS,MAAM,IAAI;YAChE;YACA,kBAAkB;YAClB,eAAe;QACjB,OAAO;YACL,gBAAgB;YAChB,IAAI;gBACF,MAAM;gBACN,kBAAkB;YACpB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;QACF;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,cAAc,GAAG,OAAO,CAAC,CAAA;gBACzC,MAAM,OAAO,GAAG,CAAC;YACnB;QACF;QACA,kBAAkB,CAAC;IACrB;IAEA,MAAM,oBAAoB;QACxB,MAAM,iBAAiB,CAAC;QACxB,eAAe;QAEf,IAAI,gBAAgB;YAClB,wCAAwC;YACxC,IAAI,iKAAqB,EAAE;gBACzB,iKAAqB,CAAC,YAAY;YACpC;YACA,eAAe;YACf,kBAAkB;QACpB,OAAO,IAAI,kBAAkB,SAAS,OAAO,EAAE;YAC7C,0CAA0C;YAC1C;QACF;IACF;IAEA,MAAM,UAAU;QACd;QACA;IACF;IAEA,MAAM,UAAU;QACd,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,CAAA,QAAS,MAAM,IAAI;QAC3D;QAEA,uBAAuB;QACvB,IAAI,iKAAqB,EAAE;YACzB,iKAAqB,CAAC,YAAY;QACpC;QACA,eAAe;QACf,kBAAkB;IACpB;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,GAAG,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IAClF;IAEA,MAAM,sBAAsB;QAC1B,qDAAqD;QACrD,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,sBAAsB,EAAE,WAAW;;0BAElD,8OAAC,4MAAe;0BACb,8BACC,8OAAC,oMAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;8BAEnB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CAAY,mBAAmB,MAAM;;;;;;;;;;;0CAEtD,8OAAC;gCAAG,WAAU;;oCAAsC;oCACnC,mBAAmB,IAAI;;;;;;;0CAExC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;wCAA4D,OAAO;4CAAE,gBAAgB;wCAAO;;;;;;kDAC3G,8OAAC;wCAAI,WAAU;wCAA4D,OAAO;4CAAE,gBAAgB;wCAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQpH,6BACC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAW,mBAAmB,MAAM;;;;;;;;;;;sDAEtD,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DACX,mBAAmB,IAAI;;;;;;8DAE1B,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;;;;;;;;;;;;;8CAItD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,eAAe;;;;;;sDAElB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAK,WAAU;8DAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO3D,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDACP,OAAO;oDAAC;oDAAG;oDAAM;iDAAE;4CACrB;4CACA,YAAY;gDAAE,UAAU;gDAAG,QAAQ;gDAAU,MAAM;4CAAY;sDAE/D,cAAA,8OAAC;gDAAI,WAAU;0DAAuB,mBAAmB,MAAM;;;;;;;;;;;;;;;;kDAKnE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAK,WAAU;sEAAiC;;;;;;;;;;;;gDAElD,6BACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,6MAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;4DAAK,WAAU;sEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQ9D,8OAAC;gCAAI,WAAU;;oCACZ,kBAAkB,qBAAqB,0BACtC,8OAAC;wCACC,KAAK;wCACL,QAAQ;wCACR,KAAK;wCACL,WAAW;wCACX,WAAU;;;;;iGAGZ,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,0NAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,8OAAC;oDAAE,WAAU;8DACV,qBAAqB,WAAW,yBAAyB;;;;;;;;;;;;;;;;;kDAOlE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DAAkC;;;;;;;;;;;;;;;;oCAKpD,6BACC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oNAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;wDAAK,WAAU;kEAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBASjD,kBAAkB,CAAC,6BAClB,8OAAC,oMAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;;0CAE5B,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAA6B;;;;;;0DAC7C,8OAAC;gDAAK,WAAU;0DAAwC,eAAe,OAAO;;;;;;;;;;;;kDAEhF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAA6B;;;;;;0DAC7C,8OAAC;gDAAK,WAAU;;oDAA8B,KAAK,KAAK,CAAC,eAAe,UAAU;oDAAE;;;;;;;;;;;;;kDAEtF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAA6B;;;;;;0DAC7C,8OAAC;gDAAK,WAAU;;oDAA+B,KAAK,KAAK,CAAC,MAAM,eAAe,OAAO;oDAAE;;;;;;;;;;;;;kDAE1F,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAA6B;;;;;;0DAC7C,8OAAC;gDAAK,WAAU;;oDAA+B,KAAK,KAAK,CAAC,MAAM,eAAe,MAAM;oDAAE;;;;;;;;;;;;;;;;;;;;;;;;;kCAO/F,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,oMAAM,CAAC,MAAM;oCACZ,SAAS;oCACT,WAAW,CAAC,uEAAuE,EACjF,iBACI,sCACA,yBACJ;oCACF,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;8CAEvB,+BAAiB,8OAAC,6MAAK;wCAAC,WAAU;;;;;iGAAe,8OAAC,0NAAQ;wCAAC,WAAU;;;;;;;;;;;8CAIxE,8OAAC,oMAAM,CAAC,MAAM;oCACZ,SAAS;oCACT,WAAW,CAAC,uEAAuE,EACjF,iBACI,sCACA,yBACJ;oCACF,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;8CAEvB,+BAAiB,8OAAC,uMAAG;wCAAC,WAAU;;;;;iGAAe,8OAAC,oNAAM;wCAAC,WAAU;;;;;;;;;;;8CAIpE,8OAAC,oMAAM,CAAC,MAAM;oCACZ,SAAS;oCACT,WAAW,CAAC,uEAAuE,EACjF,cACI,6BACA,qCACJ;oCACF,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,OAAM;8CAEL,4BAAc,8OAAC,oNAAM;wCAAC,WAAU;;;;;iGAAe,8OAAC,uMAAG;wCAAC,WAAU;;;;;;;;;;;8CAIjE,8OAAC,oMAAM,CAAC,MAAM;oCACZ,SAAS;oCACT,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;8CAExB,cAAA,8OAAC,0NAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpC;uCAEe", "debugId": null}}, {"offset": {"line": 5000, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/trainer%20test/ai-trainer-assessment/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport AssessmentInterface from '@/components/AssessmentInterface';\nimport UserDashboard from '@/components/UserDashboard';\nimport PersonalitySelector from '@/components/PersonalitySelector';\nimport VideoCallInterface from '@/components/VideoCallInterface';\nimport { AssessmentPhase, AIInsight } from '@/types';\nimport { UserProfileService } from '@/services/userProfileService';\nimport { TrainerPersonality, getPersonalityById } from '@/services/trainerPersonalities';\n\nexport default function Home() {\n  const [currentPhase, setCurrentPhase] = useState<AssessmentPhase>('warm_welcome');\n  const [insights, setInsights] = useState<AIInsight[]>([]);\n  const [userName, setUserName] = useState('');\n  const [userEmail, setUserEmail] = useState('');\n  const [showAssessment, setShowAssessment] = useState(false);\n  const [showGeneralChat, setShowGeneralChat] = useState(false);\n  const [showPersonalitySelector, setShowPersonalitySelector] = useState(false);\n  const [showVideoCall, setShowVideoCall] = useState(false);\n  const [selectedPersonality, setSelectedPersonality] = useState<TrainerPersonality | null>(null);\n  const [hasExistingProfile, setHasExistingProfile] = useState(false);\n  const [loading, setLoading] = useState(true);\n\n  // Check for existing profile on load\n  useEffect(() => {\n    const existingProfile = UserProfileService.getCurrentProfile();\n    if (existingProfile) {\n      setUserName(existingProfile.name);\n      setUserEmail(existingProfile.email || '');\n      setHasExistingProfile(true);\n    }\n    setLoading(false);\n  }, []);\n\n  const handleStartAssessment = () => {\n    if (userName.trim()) {\n      // Show personality selector first for new users\n      if (!hasExistingProfile) {\n        setShowPersonalitySelector(true);\n      } else {\n        setShowAssessment(true);\n        setShowGeneralChat(false);\n      }\n    }\n  };\n\n  const handleStartGeneralChat = () => {\n    if (userName.trim()) {\n      setShowGeneralChat(true);\n      setShowAssessment(false);\n      setShowVideoCall(false);\n    }\n  };\n\n  const handleStartCall = () => {\n    const profile = UserProfileService.getCurrentProfile();\n    if (profile) {\n      const personality = getPersonalityById(profile.preferences.trainerPersonality);\n      setSelectedPersonality(personality || null);\n      setShowVideoCall(true);\n      setShowAssessment(false);\n      setShowGeneralChat(false);\n    }\n  };\n\n  const handlePhaseChange = (phase: AssessmentPhase) => {\n    setCurrentPhase(phase);\n  };\n\n  const handleInsights = (newInsights: AIInsight[]) => {\n    setInsights(prev => [...prev, ...newInsights]);\n  };\n\n  const handlePersonalitySelected = (personality: TrainerPersonality) => {\n    setSelectedPersonality(personality);\n    // Save personality to user profile\n    UserProfileService.updateProfile({\n      preferences: {\n        ...UserProfileService.getCurrentProfile()?.preferences,\n        trainerPersonality: personality.id\n      }\n    });\n    setShowPersonalitySelector(false);\n    setShowAssessment(true);\n  };\n\n  const handleBackToDashboard = () => {\n    setShowAssessment(false);\n    setShowGeneralChat(false);\n    setShowPersonalitySelector(false);\n    setShowVideoCall(false);\n  };\n\n  const handleEndCall = () => {\n    setShowVideoCall(false);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center\">\n        <div className=\"text-cyan-300 font-mono\">Loading...</div>\n      </div>\n    );\n  }\n\n  // Show assessment interface\n  if (showAssessment) {\n    return (\n      <div>\n        <button\n          onClick={handleBackToDashboard}\n          className=\"absolute top-4 left-4 z-10 bg-black bg-opacity-50 text-cyan-300 px-4 py-2 rounded-lg border border-cyan-400 hover:bg-opacity-70 transition-colors font-mono text-sm\"\n        >\n          ← BACK TO DASHBOARD\n        </button>\n        <AssessmentInterface\n          userName={userName}\n          userEmail={userEmail}\n          personalityId={selectedPersonality?.id}\n          onPhaseChange={handlePhaseChange}\n          onInsights={handleInsights}\n          isGeneralChat={false}\n        />\n      </div>\n    );\n  }\n\n  // Show general chat interface\n  if (showGeneralChat) {\n    return (\n      <div>\n        <button\n          onClick={handleBackToDashboard}\n          className=\"absolute top-4 left-4 z-10 bg-black bg-opacity-50 text-cyan-300 px-4 py-2 rounded-lg border border-cyan-400 hover:bg-opacity-70 transition-colors font-mono text-sm\"\n        >\n          ← BACK TO DASHBOARD\n        </button>\n        <AssessmentInterface\n          userName={userName}\n          userEmail={userEmail}\n          personalityId={selectedPersonality?.id}\n          onPhaseChange={handlePhaseChange}\n          onInsights={handleInsights}\n          isGeneralChat={true}\n        />\n      </div>\n    );\n  }\n\n  // Show video call interface\n  if (showVideoCall && selectedPersonality) {\n    return (\n      <VideoCallInterface\n        trainerPersonality={selectedPersonality}\n        userName={userName}\n        onEndCall={handleEndCall}\n      />\n    );\n  }\n\n  // Show personality selector\n  if (showPersonalitySelector) {\n    return (\n      <div>\n        <button\n          onClick={handleBackToDashboard}\n          className=\"absolute top-4 left-4 z-10 bg-black bg-opacity-50 text-cyan-300 px-4 py-2 rounded-lg border border-cyan-400 hover:bg-opacity-70 transition-colors font-mono text-sm\"\n        >\n          ← BACK\n        </button>\n        <PersonalitySelector\n          userName={userName}\n          onSelectPersonality={handlePersonalitySelected}\n        />\n      </div>\n    );\n  }\n\n  // Show dashboard for existing users\n  if (hasExistingProfile) {\n    return (\n      <UserDashboard\n        onStartCall={handleStartCall}\n        onStartChat={handleStartGeneralChat}\n        onStartAssessment={handleStartAssessment}\n      />\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center p-4\">\n      <div className=\"max-w-md w-full bg-black bg-opacity-50 rounded-lg shadow-xl border border-cyan-400 p-8 backdrop-blur-sm\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-cyan-300 mb-2 font-mono\">\n            AI HOLOGRAPHIC TRAINER\n          </h1>\n          <p className=\"text-cyan-100 text-sm\">\n            Experience the future of fitness with Alex, your AI holographic personal trainer\n          </p>\n        </div>\n\n        <div className=\"space-y-6\">\n          <div>\n            <label htmlFor=\"name\" className=\"block text-sm font-medium text-cyan-300 mb-2 font-mono\">\n              ENTER YOUR NAME:\n            </label>\n            <input\n              type=\"text\"\n              id=\"name\"\n              value={userName}\n              onChange={(e) => setUserName(e.target.value)}\n              placeholder=\"Your name here...\"\n              className=\"w-full px-3 py-2 bg-black bg-opacity-50 border border-cyan-400 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 text-cyan-100 placeholder-cyan-400 font-mono\"\n              onKeyPress={(e) => e.key === 'Enter' && handleStartAssessment()}\n            />\n          </div>\n\n          <div>\n            <label htmlFor=\"email\" className=\"block text-sm font-medium text-cyan-300 mb-2 font-mono\">\n              EMAIL (OPTIONAL):\n            </label>\n            <input\n              type=\"email\"\n              id=\"email\"\n              value={userEmail}\n              onChange={(e) => setUserEmail(e.target.value)}\n              placeholder=\"<EMAIL>\"\n              className=\"w-full px-3 py-2 bg-black bg-opacity-50 border border-cyan-400 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 text-cyan-100 placeholder-cyan-400 font-mono\"\n            />\n            <p className=\"text-xs text-cyan-400 mt-1\">\n              Email helps us save your progress and send you personalized tips\n            </p>\n          </div>\n\n          <button\n            onClick={handleStartAssessment}\n            disabled={!userName.trim()}\n            className=\"w-full bg-cyan-600 text-white py-3 px-4 rounded-md hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-offset-2 disabled:bg-gray-600 disabled:cursor-not-allowed transition-colors border border-cyan-400 font-mono\"\n            style={{\n              boxShadow: '0 0 20px rgba(34, 211, 238, 0.3)'\n            }}\n          >\n            START YOUR TRANSFORMATION\n          </button>\n        </div>\n\n        <div className=\"mt-8 text-center text-sm text-cyan-400 space-y-2\">\n          <p className=\"font-mono\">🎤 MICROPHONE ACCESS REQUIRED</p>\n          <p className=\"font-mono\">🔊 AUDIO OUTPUT RECOMMENDED</p>\n          <p className=\"font-mono\">🤖 PERSONALIZED AI EXPERIENCE</p>\n          <p className=\"font-mono\">💾 YOUR PROGRESS IS AUTOMATICALLY SAVED</p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AATA;;;;;;;;;AAWe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAkB;IAClE,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAc,EAAE;IACxD,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAC;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAC;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,iNAAQ,EAAC;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,iNAAQ,EAAC;IACvD,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,IAAA,iNAAQ,EAAC;IACvE,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,iNAAQ,EAAC;IACnD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,IAAA,iNAAQ,EAA4B;IAC1F,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,iNAAQ,EAAC;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IAEvC,qCAAqC;IACrC,IAAA,kNAAS,EAAC;QACR,MAAM,kBAAkB,2JAAkB,CAAC,iBAAiB;QAC5D,IAAI,iBAAiB;YACnB,YAAY,gBAAgB,IAAI;YAChC,aAAa,gBAAgB,KAAK,IAAI;YACtC,sBAAsB;QACxB;QACA,WAAW;IACb,GAAG,EAAE;IAEL,MAAM,wBAAwB;QAC5B,IAAI,SAAS,IAAI,IAAI;YACnB,gDAAgD;YAChD,IAAI,CAAC,oBAAoB;gBACvB,2BAA2B;YAC7B,OAAO;gBACL,kBAAkB;gBAClB,mBAAmB;YACrB;QACF;IACF;IAEA,MAAM,yBAAyB;QAC7B,IAAI,SAAS,IAAI,IAAI;YACnB,mBAAmB;YACnB,kBAAkB;YAClB,iBAAiB;QACnB;IACF;IAEA,MAAM,kBAAkB;QACtB,MAAM,UAAU,2JAAkB,CAAC,iBAAiB;QACpD,IAAI,SAAS;YACX,MAAM,cAAc,IAAA,6JAAkB,EAAC,QAAQ,WAAW,CAAC,kBAAkB;YAC7E,uBAAuB,eAAe;YACtC,iBAAiB;YACjB,kBAAkB;YAClB,mBAAmB;QACrB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;IAClB;IAEA,MAAM,iBAAiB,CAAC;QACtB,YAAY,CAAA,OAAQ;mBAAI;mBAAS;aAAY;IAC/C;IAEA,MAAM,4BAA4B,CAAC;QACjC,uBAAuB;QACvB,mCAAmC;QACnC,2JAAkB,CAAC,aAAa,CAAC;YAC/B,aAAa;gBACX,GAAG,2JAAkB,CAAC,iBAAiB,IAAI,WAAW;gBACtD,oBAAoB,YAAY,EAAE;YACpC;QACF;QACA,2BAA2B;QAC3B,kBAAkB;IACpB;IAEA,MAAM,wBAAwB;QAC5B,kBAAkB;QAClB,mBAAmB;QACnB,2BAA2B;QAC3B,iBAAiB;IACnB;IAEA,MAAM,gBAAgB;QACpB,iBAAiB;IACnB;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAA0B;;;;;;;;;;;IAG/C;IAEA,4BAA4B;IAC5B,IAAI,gBAAgB;QAClB,qBACE,8OAAC;;8BACC,8OAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;8BAGD,8OAAC,oJAAmB;oBAClB,UAAU;oBACV,WAAW;oBACX,eAAe,qBAAqB;oBACpC,eAAe;oBACf,YAAY;oBACZ,eAAe;;;;;;;;;;;;IAIvB;IAEA,8BAA8B;IAC9B,IAAI,iBAAiB;QACnB,qBACE,8OAAC;;8BACC,8OAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;8BAGD,8OAAC,oJAAmB;oBAClB,UAAU;oBACV,WAAW;oBACX,eAAe,qBAAqB;oBACpC,eAAe;oBACf,YAAY;oBACZ,eAAe;;;;;;;;;;;;IAIvB;IAEA,4BAA4B;IAC5B,IAAI,iBAAiB,qBAAqB;QACxC,qBACE,8OAAC,mJAAkB;YACjB,oBAAoB;YACpB,UAAU;YACV,WAAW;;;;;;IAGjB;IAEA,4BAA4B;IAC5B,IAAI,yBAAyB;QAC3B,qBACE,8OAAC;;8BACC,8OAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;8BAGD,8OAAC,oJAAmB;oBAClB,UAAU;oBACV,qBAAqB;;;;;;;;;;;;IAI7B;IAEA,oCAAoC;IACpC,IAAI,oBAAoB;QACtB,qBACE,8OAAC,8IAAa;YACZ,aAAa;YACb,aAAa;YACb,mBAAmB;;;;;;IAGzB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAkD;;;;;;sCAGhE,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAKvC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAO,WAAU;8CAAyD;;;;;;8CAGzF,8OAAC;oCACC,MAAK;oCACL,IAAG;oCACH,OAAO;oCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC3C,aAAY;oCACZ,WAAU;oCACV,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;;;;;;;sCAI5C,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAQ,WAAU;8CAAyD;;;;;;8CAG1F,8OAAC;oCACC,MAAK;oCACL,IAAG;oCACH,OAAO;oCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oCAC5C,aAAY;oCACZ,WAAU;;;;;;8CAEZ,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAK5C,8OAAC;4BACC,SAAS;4BACT,UAAU,CAAC,SAAS,IAAI;4BACxB,WAAU;4BACV,OAAO;gCACL,WAAW;4BACb;sCACD;;;;;;;;;;;;8BAKH,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAY;;;;;;sCACzB,8OAAC;4BAAE,WAAU;sCAAY;;;;;;sCACzB,8OAAC;4BAAE,WAAU;sCAAY;;;;;;sCACzB,8OAAC;4BAAE,WAAU;sCAAY;;;;;;;;;;;;;;;;;;;;;;;AAKnC", "debugId": null}}]}