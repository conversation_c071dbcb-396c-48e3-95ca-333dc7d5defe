// This file is generated automatically by Next.js
// Do not edit this file manually
// This file validates that all pages and layouts export the correct types

import type { AppRoutes, LayoutRoutes, ParamMap, AppRouteHandlerRoutes } from "./routes.js"
import type { ResolvingMetadata, ResolvingViewport } from "next/types.js"
import type { NextRequest } from 'next/server.js'

type AppPageConfig<Route extends AppRoutes = AppRoutes> = {
  default: React.ComponentType<{ params: Promise<ParamMap[Route]> } & any> | ((props: { params: Promise<ParamMap[Route]> } & any) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type LayoutConfig<Route extends LayoutRoutes = LayoutRoutes> = {
  default: React.ComponentType<LayoutProps<Route>> | ((props: LayoutProps<Route>) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type RouteHandlerConfig<Route extends AppRouteHandlerRoutes = AppRouteHandlerRoutes> = {
  GET?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  POST?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  PUT?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  PATCH?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  DELETE?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  HEAD?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  OPTIONS?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
}


// Validate ../../src/app/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/">> = Specific
  const handler = {} as typeof import("../../src/app/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/elevenlabs-tts/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/elevenlabs-tts">> = Specific
  const handler = {} as typeof import("../../src/app/api/elevenlabs-tts/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/general-fitness-chat/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/general-fitness-chat">> = Specific
  const handler = {} as typeof import("../../src/app/api/general-fitness-chat/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/text-to-speech/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/text-to-speech">> = Specific
  const handler = {} as typeof import("../../src/app/api/text-to-speech/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/trainer-chat/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/trainer-chat">> = Specific
  const handler = {} as typeof import("../../src/app/api/trainer-chat/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/transcribe/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/transcribe">> = Specific
  const handler = {} as typeof import("../../src/app/api/transcribe/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}





// Validate ../../src/app/layout.tsx
{
  type __IsExpected<Specific extends LayoutConfig<"/">> = Specific
  const handler = {} as typeof import("../../src/app/layout.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}
