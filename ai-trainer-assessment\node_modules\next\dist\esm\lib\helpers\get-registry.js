import { execSync } from 'child_process';
import { getPkgManager } from './get-pkg-manager';
import { getFormattedNodeOptionsWithoutInspect } from '../../server/lib/utils';
/**
 * Returns the package registry using the user's package manager.
 * The URL will have a trailing slash.
 * @default https://registry.npmjs.org/
 */ export function getRegistry(baseDir = process.cwd()) {
    const pkgManager = getPkgManager(baseDir);
    // Since `npm config` command fails in npm workspace to prevent workspace config conflicts,
    // add `--no-workspaces` flag to run under the context of the root project only.
    // Safe for non-workspace projects as it's equivalent to default `--workspaces=false`.
    // x-ref: https://github.com/vercel/next.js/issues/47121#issuecomment-1499044345
    // x-ref: https://github.com/npm/statusboard/issues/371#issue-920669998
    const resolvedFlags = pkgManager === 'npm' ? '--no-workspaces' : '';
    let registry = `https://registry.npmjs.org/`;
    try {
        const output = execSync(`${pkgManager} config get registry ${resolvedFlags}`, {
            env: {
                ...process.env,
                NODE_OPTIONS: getFormattedNodeOptionsWithoutInspect()
            }
        }).toString().trim();
        if (output.startsWith('http')) {
            registry = output.endsWith('/') ? output : `${output}/`;
        }
    } catch (err) {
        throw Object.defineProperty(new Error(`Failed to get registry from "${pkgManager}".`, {
            cause: err
        }), "__NEXT_ERROR_CODE", {
            value: "E508",
            enumerable: false,
            configurable: true
        });
    }
    return registry;
}

//# sourceMappingURL=get-registry.js.map