'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { User, Target, TrendingUp, Calendar, MessageCircle, Settings } from 'lucide-react';
import { UserProfileService, PersistentUserProfile } from '@/services/userProfileService';
import { getPersonalityById } from '@/services/trainerPersonalities';
import CallTrainerButton from './CallTrainerButton';

interface UserDashboardProps {
  onStartCall: () => void;
  onStartChat?: () => void;
  onStartAssessment?: () => void;
  className?: string;
}

export const UserDashboard: React.FC<UserDashboardProps> = ({
  onStartCall,
  onStartChat,
  onStartAssessment,
  className = ''
}) => {
  const [profile, setProfile] = useState<PersistentUserProfile | null>(null);
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadUserData();
  }, []);

  const loadUserData = () => {
    try {
      const currentProfile = UserProfileService.getCurrentProfile();
      const userStats = UserProfileService.getUserStats();
      
      setProfile(currentProfile);
      setStats(userStats);
    } catch (error) {
      console.error('Error loading user data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
        <div className="text-cyan-300 font-mono">Loading your profile...</div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-cyan-300 mb-4 font-mono">Welcome to AI Trainer</h2>
          <p className="text-cyan-100 mb-6">Let's get you started with your fitness journey!</p>
          <button
            onClick={onStartAssessment}
            className="bg-cyan-600 text-white py-3 px-6 rounded-lg hover:bg-cyan-700 transition-colors font-mono"
          >
            START YOUR ASSESSMENT
          </button>
        </div>
      </div>
    );
  }

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  };

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-4 ${className}`}>
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <motion.div
          className="text-center mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <h1 className="text-4xl font-bold text-cyan-300 mb-2 font-mono">
            {getGreeting()}, {profile.name}!
          </h1>
          <p className="text-cyan-100 text-lg">
            Ready to continue your transformation journey?
          </p>
        </motion.div>

        {/* Call Trainer Section */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          {profile && (
            <CallTrainerButton
              trainerPersonality={getPersonalityById(profile.preferences.trainerPersonality) || {
                id: 'supportive_coach',
                name: 'Coach Maya',
                description: 'Your supportive AI trainer',
                avatar: '🌟',
                color: 'green',
                voiceSettings: { rate: 0.9, pitch: 1.2, volume: 0.8 },
                systemPrompt: '',
                welcomeMessages: [],
                motivationalPhrases: [],
                encouragementPhrases: [],
                challengePhrases: []
              }}
              userName={profile.name}
              onStartCall={onStartCall}
            />
          )}
        </motion.div>

        {/* Secondary Actions */}
        {(onStartChat || onStartAssessment) && (
          <div className="grid md:grid-cols-2 gap-4 mb-8">
            {onStartChat && (
              <motion.button
                onClick={onStartChat}
                className="bg-white bg-opacity-5 backdrop-blur-xl border border-white border-opacity-10 rounded-2xl p-4 hover:bg-opacity-10 transition-all"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 }}
              >
                <MessageCircle className="w-6 h-6 text-white text-opacity-60 mb-2" />
                <h3 className="text-white font-medium mb-1">Quick Chat</h3>
                <p className="text-white text-opacity-60 text-sm">
                  Text-based conversation
                </p>
              </motion.button>
            )}

            {onStartAssessment && (
              <motion.button
                onClick={onStartAssessment}
                className="bg-white bg-opacity-5 backdrop-blur-xl border border-white border-opacity-10 rounded-2xl p-4 hover:bg-opacity-10 transition-all"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 }}
              >
                <Target className="w-6 h-6 text-white text-opacity-60 mb-2" />
                <h3 className="text-white font-medium mb-1">Assessment</h3>
                <p className="text-white text-opacity-60 text-sm">
                  Update your fitness goals
                </p>
              </motion.button>
            )}
          </div>
        )}

        {/* Stats Grid */}
        <div className="grid md:grid-cols-4 gap-4 mb-8">
          <motion.div
            className="bg-black bg-opacity-50 rounded-lg border border-cyan-400 p-4 backdrop-blur-sm text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Calendar className="w-6 h-6 text-cyan-400 mb-2 mx-auto" />
            <div className="text-2xl font-bold text-cyan-300 font-mono">{stats?.totalSessions || 0}</div>
            <div className="text-xs text-cyan-100">Sessions</div>
          </motion.div>

          <motion.div
            className="bg-black bg-opacity-50 rounded-lg border border-green-400 p-4 backdrop-blur-sm text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <MessageCircle className="w-6 h-6 text-green-400 mb-2 mx-auto" />
            <div className="text-2xl font-bold text-green-300 font-mono">{stats?.totalConversations || 0}</div>
            <div className="text-xs text-green-100">Conversations</div>
          </motion.div>

          <motion.div
            className="bg-black bg-opacity-50 rounded-lg border border-yellow-400 p-4 backdrop-blur-sm text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <TrendingUp className="w-6 h-6 text-yellow-400 mb-2 mx-auto" />
            <div className="text-2xl font-bold text-yellow-300 font-mono">{stats?.progressEntries || 0}</div>
            <div className="text-xs text-yellow-100">Progress Entries</div>
          </motion.div>

          <motion.div
            className="bg-black bg-opacity-50 rounded-lg border border-orange-400 p-4 backdrop-blur-sm text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
          >
            <Target className="w-6 h-6 text-orange-400 mb-2 mx-auto" />
            <div className="text-2xl font-bold text-orange-300 font-mono">{stats?.currentStreak || 0}</div>
            <div className="text-xs text-orange-100">Day Streak</div>
          </motion.div>
        </div>

        {/* Profile Summary */}
        <motion.div
          className="bg-black bg-opacity-50 rounded-lg border border-cyan-400 p-6 backdrop-blur-sm mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
        >
          <h3 className="text-xl font-bold text-cyan-300 mb-4 font-mono flex items-center">
            <User className="w-5 h-5 mr-2" />
            YOUR PROFILE
          </h3>
          
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h4 className="text-sm font-bold text-cyan-400 mb-2 font-mono">FITNESS GOALS</h4>
              <div className="space-y-1">
                {profile.goals.length > 0 ? (
                  profile.goals.map((goal, idx) => (
                    <div key={idx} className="text-sm text-cyan-100 bg-cyan-900 bg-opacity-30 px-2 py-1 rounded">
                      {goal.replace('_', ' ').toUpperCase()}
                    </div>
                  ))
                ) : (
                  <div className="text-sm text-gray-400">No goals set yet</div>
                )}
              </div>
            </div>

            <div>
              <h4 className="text-sm font-bold text-purple-400 mb-2 font-mono">KEY MOTIVATIONS</h4>
              <div className="space-y-1">
                {profile.motivations.length > 0 ? (
                  profile.motivations.map((motivation, idx) => (
                    <div key={idx} className="text-sm text-purple-100 bg-purple-900 bg-opacity-30 px-2 py-1 rounded">
                      {motivation.replace('_', ' ').toUpperCase()}
                    </div>
                  ))
                ) : (
                  <div className="text-sm text-gray-400">No motivations identified yet</div>
                )}
              </div>
            </div>
          </div>

          <div className="mt-4 pt-4 border-t border-gray-600">
            <div className="flex flex-wrap gap-4 text-sm">
              <div>
                <span className="text-gray-400">Fitness Level:</span>
                <span className="text-cyan-300 ml-2 font-mono">
                  {profile.fitnessLevel?.toUpperCase() || 'NOT ASSESSED'}
                </span>
              </div>
              <div>
                <span className="text-gray-400">Member Since:</span>
                <span className="text-cyan-300 ml-2 font-mono">
                  {profile.createdAt.toLocaleDateString()}
                </span>
              </div>
              <div>
                <span className="text-gray-400">Last Active:</span>
                <span className="text-cyan-300 ml-2 font-mono">
                  {profile.lastActiveAt.toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Recent Activity */}
        {profile.conversationHistory.length > 0 && (
          <motion.div
            className="bg-black bg-opacity-50 rounded-lg border border-gray-600 p-6 backdrop-blur-sm"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
          >
            <h3 className="text-xl font-bold text-gray-300 mb-4 font-mono">RECENT CONVERSATIONS</h3>
            <div className="space-y-2">
              {profile.conversationHistory.slice(-3).map((msg, idx) => (
                <div key={idx} className="text-sm">
                  <span className={`font-mono ${msg.role === 'trainer' ? 'text-cyan-400' : 'text-purple-400'}`}>
                    {msg.role === 'trainer' ? 'ALEX:' : 'YOU:'}
                  </span>
                  <span className="text-gray-300 ml-2">
                    {msg.content.length > 100 ? `${msg.content.substring(0, 100)}...` : msg.content}
                  </span>
                </div>
              ))}
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default UserDashboard;
