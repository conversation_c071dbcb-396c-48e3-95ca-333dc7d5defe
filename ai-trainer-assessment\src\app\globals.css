@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #581c87 100%);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  min-height: 100vh;
}

/* Holographic Theme Enhancements */
.hologram-glow {
  box-shadow:
    0 0 20px rgba(34, 211, 238, 0.5),
    0 0 40px rgba(34, 211, 238, 0.3),
    0 0 60px rgba(34, 211, 238, 0.1);
}

.scan-lines {
  background: repeating-linear-gradient(
    0deg,
    transparent,
    transparent 2px,
    rgba(34, 211, 238, 0.03) 2px,
    rgba(34, 211, 238, 0.03) 4px
  );
  animation: scan 2s linear infinite;
}

.hologram-text {
  text-shadow:
    0 0 5px rgba(34, 211, 238, 0.8),
    0 0 10px rgba(34, 211, 238, 0.6),
    0 0 15px rgba(34, 211, 238, 0.4);
}

.glitch {
  animation: glitch 3s infinite;
}

@keyframes scan {
  0% { transform: translateY(-100%); }
  100% { transform: translateY(100%); }
}

@keyframes glitch {
  0%, 90%, 100% { transform: translateX(0); }
  91% { transform: translateX(-2px); }
  92% { transform: translateX(2px); }
  93% { transform: translateX(-1px); }
  94% { transform: translateX(1px); }
  95% { transform: translateX(0); }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #22d3ee, #3b82f6);
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(34, 211, 238, 0.5);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #0891b2, #2563eb);
}
