{"version": 3, "sources": ["../../../../../src/server/normalizers/built/pages/index.ts"], "sourcesContent": ["import {\n  DevPagesBundlePathNormalizer,\n  PagesBundlePathNormalizer,\n} from './pages-bundle-path-normalizer'\nimport { PagesFilenameNormalizer } from './pages-filename-normalizer'\nimport { DevPagesPageNormalizer } from './pages-page-normalizer'\nimport { DevPagesPathnameNormalizer } from './pages-pathname-normalizer'\n\nexport class PagesNormalizers {\n  public readonly filename: PagesFilenameNormalizer\n  public readonly bundlePath: PagesBundlePathNormalizer\n\n  constructor(distDir: string) {\n    this.filename = new PagesFilenameNormalizer(distDir)\n    this.bundlePath = new PagesBundlePathNormalizer()\n\n    // You'd think that we'd require a `pathname` normalizer here, but for\n    // `/pages` we have to handle i18n routes, which means that we need to\n    // analyze the page path to determine the locale prefix and it's locale.\n  }\n}\n\nexport class DevPagesNormalizers {\n  public readonly page: DevPagesPageNormalizer\n  public readonly pathname: DevPagesPathnameNormalizer\n  public readonly bundlePath: Dev<PERSON>agesBundlePathNormalizer\n\n  constructor(pagesDir: string, extensions: ReadonlyArray<string>) {\n    this.page = new DevPagesPageNormalizer(pagesDir, extensions)\n    this.pathname = new DevPagesPathnameNormalizer(pagesDir, extensions)\n    this.bundlePath = new DevPagesBundlePathNormalizer(this.page)\n  }\n}\n"], "names": ["DevPagesNormalizers", "PagesNormalizers", "constructor", "distDir", "filename", "PagesFilenameNormalizer", "bundlePath", "PagesBundlePathNormalizer", "pagesDir", "extensions", "page", "DevPagesPageNormalizer", "pathname", "DevPagesPathnameNormalizer", "DevPagesBundlePathNormalizer"], "mappings": ";;;;;;;;;;;;;;;IAsBaA,mBAAmB;eAAnBA;;IAdAC,gBAAgB;eAAhBA;;;2CALN;yCACiC;qCACD;yCACI;AAEpC,MAAMA;IAIXC,YAAYC,OAAe,CAAE;QAC3B,IAAI,CAACC,QAAQ,GAAG,IAAIC,gDAAuB,CAACF;QAC5C,IAAI,CAACG,UAAU,GAAG,IAAIC,oDAAyB;IAE/C,sEAAsE;IACtE,sEAAsE;IACtE,wEAAwE;IAC1E;AACF;AAEO,MAAMP;IAKXE,YAAYM,QAAgB,EAAEC,UAAiC,CAAE;QAC/D,IAAI,CAACC,IAAI,GAAG,IAAIC,2CAAsB,CAACH,UAAUC;QACjD,IAAI,CAACG,QAAQ,GAAG,IAAIC,mDAA0B,CAACL,UAAUC;QACzD,IAAI,CAACH,UAAU,GAAG,IAAIQ,uDAA4B,CAAC,IAAI,CAACJ,IAAI;IAC9D;AACF", "ignoreList": [0]}