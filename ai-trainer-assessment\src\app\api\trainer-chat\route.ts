import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';
import { AssessmentPhase, ConversationMessage, AIInsight } from '@/types';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// AI Trainer Persona and System Prompts
const TRAINER_SYSTEM_PROMPT = `You are <PERSON>, a warm, charismatic, and highly skilled personal trainer conducting an initial fitness assessment. Your goal is to build trust, gather information, and naturally guide the conversation toward premium training services.

PERSONALITY:
- Warm, encouraging, and slightly humorous
- Uses the user's name frequently
- Provides positive reinforcement immediately
- Professional but approachable
- Genuinely cares about the user's success

CONVERSATION PHASES:
1. WELCOME: Build rapport, create excitement
2. DISCOVERY: Learn about goals, pain points, motivations
3. PHYSICAL_ASSESSMENT: Guide through movement tests
4. LIFESTYLE_ASSESSMENT: Understand daily habits, constraints
5. REVEAL_VISION: Show what's possible, build excitement
6. UPSELL: Naturally recommend premium services

GUIDELINES:
- Keep responses conversational and under 100 words
- Ask one focused question at a time
- Mirror back what the user says to show understanding
- Identify emotional drivers and pain points
- Build toward the vision of their transformed self
- Never hard sell - make recommendations feel like caring advice

Current phase will be provided in the request. Adapt your response accordingly.`;

const PHASE_PROMPTS = {
  welcome: `Focus on building rapport and excitement. Welcome them warmly, use their name, and set a positive tone for the session.`,
  
  discovery: `Ask layered questions to understand their goals and motivations:
- Surface level: Current fitness routine, experience
- Pain points: What's been holding them back
- Emotional drivers: Why this goal matters now
- Future vision: How they want to look and feel`,
  
  physical_assessment: `Guide them through simple movement assessments while providing encouraging feedback. Focus on form, mobility, and identifying areas for improvement.`,
  
  lifestyle_assessment: `Understand their daily life constraints:
- Sleep habits and stress levels
- Time availability for workouts
- Nutrition patterns
- Equipment access`,
  
  reveal_vision: `Paint a picture of their potential transformation. Use specific, visual language about how they could look and feel. Build excitement about what's possible.`,
  
  upsell: `Naturally transition to recommending services. Frame it as caring guidance rather than selling:
- "I'd love to work closely with you..."
- "Here's the difference between going alone vs. having guidance..."
- Present options without pressure`
};

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      message, 
      phase, 
      conversationHistory, 
      userProfile 
    }: {
      message: string;
      phase: AssessmentPhase;
      conversationHistory: ConversationMessage[];
      userProfile?: any;
    } = body;

    if (!message || !phase) {
      return NextResponse.json(
        { error: 'Message and phase are required' },
        { status: 400 }
      );
    }

    // Build conversation context
    const contextMessages = conversationHistory.map(msg => ({
      role: msg.role === 'trainer' ? 'assistant' : 'user',
      content: msg.content
    }));

    // Add current user message
    contextMessages.push({
      role: 'user',
      content: message
    });

    // Create phase-specific system prompt
    const phasePrompt = PHASE_PROMPTS[phase] || PHASE_PROMPTS.welcome;
    const fullSystemPrompt = `${TRAINER_SYSTEM_PROMPT}\n\nCURRENT PHASE: ${phase.toUpperCase()}\n${phasePrompt}`;

    // Add user profile context if available
    const profileContext = userProfile ? 
      `\n\nUSER PROFILE: ${JSON.stringify(userProfile)}` : '';

    const response = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: fullSystemPrompt + profileContext
        },
        ...contextMessages
      ],
      temperature: 0.8, // Slightly creative for personality
      max_tokens: 200, // Keep responses concise
      presence_penalty: 0.1,
      frequency_penalty: 0.1
    });

    const trainerResponse = response.choices[0]?.message?.content;

    if (!trainerResponse) {
      throw new Error('No response generated');
    }

    // Analyze the conversation for insights
    const insights = await analyzeConversation(message, trainerResponse, phase);

    return NextResponse.json({
      response: trainerResponse,
      insights,
      success: true
    });

  } catch (error) {
    console.error('Trainer chat error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to generate trainer response',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Analyze conversation for AI insights
async function analyzeConversation(
  userMessage: string, 
  trainerResponse: string, 
  phase: AssessmentPhase
): Promise<AIInsight[]> {
  try {
    const analysisPrompt = `Analyze this fitness assessment conversation for key insights:

USER MESSAGE: "${userMessage}"
TRAINER RESPONSE: "${trainerResponse}"
PHASE: ${phase}

Identify insights in these categories:
1. MOTIVATION: What drives this person?
2. PAIN_POINT: What frustrates or holds them back?
3. GOAL_CLARITY: How clear are their goals?
4. READINESS: How ready are they to commit?
5. UPSELL_OPPORTUNITY: When/how to recommend premium services?

Return a JSON array of insights with: type, confidence (0-1), insight, suggestedResponse.
Keep insights concise and actionable.`;

    const response = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [{ role: 'user', content: analysisPrompt }],
      temperature: 0.3,
      max_tokens: 300
    });

    const analysisText = response.choices[0]?.message?.content;
    
    if (analysisText) {
      try {
        return JSON.parse(analysisText);
      } catch {
        // If JSON parsing fails, return basic insights
        return [{
          type: 'motivation',
          confidence: 0.5,
          insight: 'User is engaged in the conversation',
          suggestedResponse: 'Continue building rapport'
        }];
      }
    }

    return [];
  } catch (error) {
    console.error('Analysis error:', error);
    return [];
  }
}
