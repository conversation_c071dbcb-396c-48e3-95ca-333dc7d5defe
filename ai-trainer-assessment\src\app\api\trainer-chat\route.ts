import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';
import { AssessmentPhase, ConversationMessage, AIInsight, DiscoverySubPhase } from '@/types';
import { ConversationEngine } from '@/services/conversationEngine';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Store conversation engines per session (in production, use Redis or database)
const conversationEngines = new Map<string, ConversationEngine>();

function getOrCreateEngine(sessionId: string, userName: string): ConversationEngine {
  if (!conversationEngines.has(sessionId)) {
    conversationEngines.set(sessionId, new ConversationEngine(userName));
  }
  return conversationEngines.get(sessionId)!;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      message, 
      phase, 
      conversationHistory, 
      userProfile 
    }: {
      message: string;
      phase: AssessmentPhase;
      conversationHistory: ConversationMessage[];
      userProfile?: any;
    } = body;

    if (!message || !phase) {
      return NextResponse.json(
        { error: 'Message and phase are required' },
        { status: 400 }
      );
    }

    // Build conversation context
    const contextMessages = conversationHistory.map(msg => ({
      role: msg.role === 'trainer' ? 'assistant' : 'user',
      content: msg.content
    }));

    // Add current user message
    contextMessages.push({
      role: 'user',
      content: message
    });

    // Get or create conversation engine for this session
    const userName = userProfile?.name || 'there';
    const sessionId = userProfile?.sessionId || 'default';
    const engine = getOrCreateEngine(sessionId, userName);

    // Update engine state
    engine.setCurrentPhase(phase);
    const subPhase = userProfile?.subPhase as DiscoverySubPhase;
    if (subPhase) {
      engine.setCurrentSubPhase(subPhase);
    }

    // Update user profile based on response
    engine.updateProfile(message, phase, subPhase);

    // Get phase-specific system prompt
    let systemPrompt: string;
    switch (phase) {
      case 'warm_welcome':
        systemPrompt = engine.getWelcomePrompt();
        break;
      case 'deep_discovery':
        systemPrompt = engine.getDiscoveryPrompt(subPhase || 'surface_level', message);
        break;
      case 'physical_assessment':
        systemPrompt = engine.getPhysicalAssessmentPrompt();
        break;
      case 'vision_reveal':
        systemPrompt = engine.getVisionRevealPrompt();
        break;
      case 'service_recommendation':
        systemPrompt = engine.getServiceRecommendationPrompt();
        break;
      default:
        systemPrompt = engine.getWelcomePrompt();
    }

    const response = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: systemPrompt
        },
        ...contextMessages.slice(-6) // Keep last 6 messages for context
      ],
      temperature: 0.8,
      max_tokens: 200,
      presence_penalty: 0.1,
      frequency_penalty: 0.1
    });

    const trainerResponse = response.choices[0]?.message?.content;

    if (!trainerResponse) {
      throw new Error('No response generated');
    }

    // Analyze the conversation for insights
    const insights = engine.analyzeResponse(message, phase);

    // Determine if phase should transition
    const responseCount = conversationHistory.filter(msg => msg.role === 'user').length + 1;
    const timeElapsed = (Date.now() - new Date().getTime()) / 1000; // Simplified
    const shouldTransition = engine.shouldTransitionPhase(responseCount, timeElapsed);
    const nextPhase = shouldTransition ? engine.getNextPhase() : null;

    return NextResponse.json({
      response: trainerResponse,
      insights,
      userProfile: engine.getUserProfile(),
      shouldTransition,
      nextPhase,
      currentSubPhase: engine.getCurrentSubPhase(),
      success: true
    });

  } catch (error) {
    console.error('Trainer chat error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to generate trainer response',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Legacy function removed - using ConversationEngine.analyzeResponse instead
